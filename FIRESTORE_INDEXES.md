# Firestore Indexes for Fetchly Chat System

This document explains the Firestore indexes required for the Fetchly chat system to work efficiently.

## 🎯 Overview

The chat system uses the following Firestore structure:
```
/chats/{chatId}
/chats/{chatId}/messages/{messageId}
/chat_notifications/{notificationId}
```

## 📊 Required Indexes

### 1. Chat Listing Indexes

#### Index 1: User Chats by Last Message Time
```json
{
  "collectionGroup": "chats",
  "fields": [
    { "fieldPath": "participantIds", "arrayConfig": "CONTAINS" },
    { "fieldPath": "lastMessageTime", "order": "DESCENDING" }
  ]
}
```
**Purpose**: Load user's chats ordered by most recent activity
**Query**: `chats.where('participantIds', 'array-contains', userId).orderBy('lastMessageTime', 'desc')`

#### Index 2: User Chats by Updated Time
```json
{
  "collectionGroup": "chats",
  "fields": [
    { "fieldPath": "participantIds", "arrayConfig": "CONTAINS" },
    { "fieldPath": "updatedAt", "order": "DESCENDING" }
  ]
}
```
**Purpose**: Alternative sorting for chat list
**Query**: `chats.where('participantIds', 'array-contains', userId).orderBy('updatedAt', 'desc')`

#### Index 3: Support Chat Filtering
```json
{
  "collectionGroup": "chats",
  "fields": [
    { "fieldPath": "isSupportChat", "order": "ASCENDING" },
    { "fieldPath": "participantIds", "arrayConfig": "CONTAINS" },
    { "fieldPath": "lastMessageTime", "order": "DESCENDING" }
  ]
}
```
**Purpose**: Filter support chats separately
**Query**: `chats.where('isSupportChat', '==', true).where('participantIds', 'array-contains', userId).orderBy('lastMessageTime', 'desc')`

### 2. Message Indexes

#### Index 4: Messages by Chat ID
```json
{
  "collectionGroup": "messages",
  "queryScope": "COLLECTION_GROUP",
  "fields": [
    { "fieldPath": "chatId", "order": "ASCENDING" },
    { "fieldPath": "timestamp", "order": "ASCENDING" }
  ]
}
```
**Purpose**: Load messages for a specific chat in chronological order
**Query**: `collectionGroup('messages').where('chatId', '==', chatId).orderBy('timestamp', 'asc')`

#### Index 5: Messages by Sender
```json
{
  "collectionGroup": "messages",
  "queryScope": "COLLECTION_GROUP",
  "fields": [
    { "fieldPath": "senderId", "order": "ASCENDING" },
    { "fieldPath": "timestamp", "order": "DESCENDING" }
  ]
}
```
**Purpose**: Find messages sent by a specific user
**Query**: `collectionGroup('messages').where('senderId', '==', userId).orderBy('timestamp', 'desc')`

#### Index 6: Unread Messages
```json
{
  "collectionGroup": "messages",
  "queryScope": "COLLECTION_GROUP",
  "fields": [
    { "fieldPath": "chatId", "order": "ASCENDING" },
    { "fieldPath": "readBy", "arrayConfig": "CONTAINS" },
    { "fieldPath": "timestamp", "order": "DESCENDING" }
  ]
}
```
**Purpose**: Find unread messages in a chat
**Query**: `collectionGroup('messages').where('chatId', '==', chatId).where('readBy', 'array-contains', userId).orderBy('timestamp', 'desc')`

### 3. Notification Indexes

#### Index 7: User Notifications
```json
{
  "collectionGroup": "chat_notifications",
  "fields": [
    { "fieldPath": "userId", "order": "ASCENDING" },
    { "fieldPath": "isRead", "order": "ASCENDING" },
    { "fieldPath": "createdAt", "order": "DESCENDING" }
  ]
}
```
**Purpose**: Get unread notifications for a user
**Query**: `chat_notifications.where('userId', '==', userId).where('isRead', '==', false).orderBy('createdAt', 'desc')`

#### Index 8: Chat-Specific Notifications
```json
{
  "collectionGroup": "chat_notifications",
  "fields": [
    { "fieldPath": "userId", "order": "ASCENDING" },
    { "fieldPath": "chatId", "order": "ASCENDING" },
    { "fieldPath": "isRead", "order": "ASCENDING" }
  ]
}
```
**Purpose**: Mark all notifications for a specific chat as read
**Query**: `chat_notifications.where('userId', '==', userId).where('chatId', '==', chatId).where('isRead', '==', false)`

## 🚀 Deployment

### Option 1: Automatic Deployment
Run the deployment script:
```bash
./deploy-indexes.sh
```

### Option 2: Manual Deployment
```bash
firebase deploy --only firestore:indexes
```

### Option 3: Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Go to Firestore Database
4. Click on "Indexes" tab
5. Create each index manually using the configurations above

## ⚡ Performance Benefits

With these indexes, your chat system will have:

- **Fast chat loading**: User's chat list loads instantly
- **Efficient message queries**: Messages load quickly in chronological order
- **Quick notification handling**: Unread notifications are retrieved efficiently
- **Scalable architecture**: Supports thousands of users and messages
- **Optimized filtering**: Support chats and regular chats can be filtered separately

## 🔍 Monitoring

After deployment, monitor your indexes in the Firebase Console:
1. Check index build status
2. Monitor query performance
3. Watch for any missing index warnings in your app logs

## 📝 Notes

- Indexes are built asynchronously and may take time for large datasets
- Each index increases storage costs slightly but dramatically improves query performance
- Missing indexes will cause queries to fail in production mode
- Always test your queries after deploying indexes

## 🆘 Troubleshooting

If you see "Missing index" errors:
1. Check the error message for the exact index needed
2. Add the index to `firestore.indexes.json`
3. Deploy with `firebase deploy --only firestore:indexes`
4. Wait for the index to build (can take several minutes)

The chat system is now optimized for production use! 🎉
