'use client';

import { useEffect, useState } from 'react';
import { StoryProgressProps } from '@/types/stories';

export default function StoryProgress({ 
  segments, 
  currentSegment, 
  progress, 
  duration 
}: StoryProgressProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0);

  useEffect(() => {
    setAnimatedProgress(progress);
  }, [progress]);

  return (
    <div className="absolute top-4 left-4 right-4 z-20">
      <div className="flex space-x-1">
        {Array.from({ length: segments }, (_, index) => (
          <div
            key={index}
            className="flex-1 h-1 bg-white/30 rounded-full overflow-hidden backdrop-blur-sm"
          >
            <div
              className={`h-full transition-all duration-100 ease-linear ${
                index < currentSegment
                  ? 'bg-white w-full' // Completed segments
                  : index === currentSegment
                  ? 'bg-white' // Current segment with progress
                  : 'bg-transparent w-0' // Future segments
              }`}
              style={{
                width: index === currentSegment ? `${animatedProgress}%` : 
                       index < currentSegment ? '100%' : '0%',
                transitionDuration: index === currentSegment ? '100ms' : '300ms'
              }}
            />
          </div>
        ))}
      </div>
      
      {/* Optional: Show time remaining */}
      <div className="flex justify-between items-center mt-2 text-white/80 text-xs">
        <span>{currentSegment + 1} of {segments}</span>
        <span>{Math.ceil((duration * (1 - progress / 100)) / 1000)}s</span>
      </div>
    </div>
  );
}
