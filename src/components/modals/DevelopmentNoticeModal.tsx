'use client';

import React from 'react';
import { X, AlertTriangle, Mail, Code, Wrench } from 'lucide-react';

interface DevelopmentNoticeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: () => void;
  userType: 'provider' | 'petowner';
}

export default function DevelopmentNoticeModal({ 
  isOpen, 
  onClose, 
  onContinue, 
  userType 
}: DevelopmentNoticeModalProps) {
  if (!isOpen) return null;

  const handleContinue = () => {
    onContinue();
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 p-6 rounded-t-3xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Development Notice</h2>
                <p className="text-orange-100">Important Information</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white p-2 rounded-full hover:bg-white/20 transition-all duration-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl">
              <Code className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
              Welcome to Fetchly {userType === 'provider' ? 'Provider' : 'Pet Owner'} Dashboard!
            </h3>
          </div>

          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 mb-6 border border-yellow-200">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <Wrench className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="text-lg font-bold text-yellow-800 mb-2">🚧 Under Active Development</h4>
                <p className="text-yellow-700 leading-relaxed">
                  This website is currently under active development. While most features are functional, 
                  some parts may still be underway or experiencing issues as we continue to improve the platform.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl p-6 mb-6 border border-blue-200">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <Mail className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="text-lg font-bold text-blue-800 mb-2">📧 Report Issues</h4>
                <p className="text-blue-700 leading-relaxed mb-3">
                  If you encounter any part of the webapp that is not functioning correctly, please notify us at:
                </p>
                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-blue-200">
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-blue-600 font-semibold hover:text-blue-800 transition-colors text-lg"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 mb-8 border border-green-200">
            <h4 className="text-lg font-bold text-gray-800 mb-3">✨ What to Expect:</h4>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Most core features are fully functional</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>New features being added regularly</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Your feedback helps us improve</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>Issues are resolved quickly</span>
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleContinue}
              className="flex-1 px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-bold text-lg"
            >
              Continue to Dashboard
            </button>
            <button
              onClick={onClose}
              className="px-6 py-4 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-2xl transition-all duration-300 font-semibold"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
