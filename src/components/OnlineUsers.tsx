'use client';

import { useState, useEffect, useCallback } from 'react';
import { MessageCircle, Crown, Shield, User } from 'lucide-react';
import { onlineStatusService, OnlineUser } from '@/lib/services/online-status-service';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, onSnapshot, getDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { User as FirebaseUser } from 'firebase/auth';

interface OnlineUsersProps {
  onMessageUser?: (userId: string, userName: string) => void;
  onViewProfile?: (userId: string) => void;
  className?: string;
}

interface AppUser extends Omit<FirebaseUser, 'displayName' | 'photoURL'> {
  uid: string;
  displayName: string | null;
  photoURL: string | null;
  role?: string;
  friends?: string[];
  followedProviders?: string[];
}

export default function OnlineUsers({ 
  onMessageUser, 
  onViewProfile, 
  className = '' 
}: OnlineUsersProps) {
  const { user } = useAuth() as { user: AppUser | null };
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchConnections = useCallback(async () => {
    if (!user?.uid) return [];
    
    try {
      // Get user's friends and followed providers
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      const userData = userDoc.data() as AppUser | undefined;
      const friendsList = userData?.friends || [];
      const followedProviders = userData?.followedProviders || [];
      
      // Convert to array and deduplicate
      const connections = Array.from(new Set([...friendsList, ...followedProviders]));
      return connections;
    } catch (error) {
      console.error('Error fetching connections:', error);
      return [];
    }
  }, [user]);

  useEffect(() => {
    if (!user?.uid) {
      setLoading(false);
      return;
    }

    let unsubscribe = () => {};
    
    const setupListeners = async () => {
      const connections = await fetchConnections();
      if (connections.length === 0) {
        setLoading(false);
        return;
      }
      
      console.log('🔔 Setting up online users listener...');
      
      // Cast to any to bypass TypeScript error with 'in' operator
      const usersRef = collection(db, 'users') as any;
      const q = query(
        usersRef,
        where('isOnline', '==', true),
        where('uid', 'in', connections as any)
      );
      
      unsubscribe = onSnapshot(q, 
        (snapshot: any) => {
          const users: OnlineUser[] = [];
          snapshot.forEach((doc: any) => {
            const data = doc.data();
            if (data.uid !== user.uid) {
              users.push({
                id: data.uid,
                name: data.displayName || 'Anonymous',
                avatar: data.photoURL,
                role: data.role || 'pet_owner',
                lastSeen: data.lastSeen,
                isOnline: data.isOnline
              });
            }
          });
          
          setOnlineUsers(users);
          setLoading(false);
          console.log('📱 Online users updated:', users.length);
        }, 
        (error: Error) => {
          console.error('Error fetching online users:', error);
          setLoading(false);
        }
      );
    };
    
    setupListeners();
    
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, fetchConnections]);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-3 h-3 text-yellow-500" />;
      case 'provider':
        return <Shield className="w-3 h-3 text-blue-500" />;
      default:
        return <User className="w-3 h-3 text-gray-500" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'border-yellow-400';
      case 'provider':
        return 'border-blue-400';
      default:
        return 'border-green-400';
    }
  };

  const handleMessageClick = (onlineUser: OnlineUser) => {
    console.log('💬 Opening message for user:', onlineUser.name);
    onMessageUser?.(onlineUser.id, onlineUser.name);
  };

  const handleProfileClick = (onlineUser: OnlineUser) => {
    console.log('👤 Opening profile for user:', onlineUser.name);
    onViewProfile?.(onlineUser.id);
  };

  if (loading) {
    return (
      <div className={`bg-white/80 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-gray-200/50 ${className}`}>
        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
          Online Users
        </h3>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3 animate-pulse">
              <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-300 rounded w-24 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (onlineUsers.length === 0) {
    return (
      <div className={`bg-white/80 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-gray-200/50 ${className}`}>
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <span className="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
          Online
        </h2>
        <div className="text-center py-6">
          <div className="text-gray-500 text-sm">
            <User className="w-8 h-8 mx-auto mb-2 opacity-50" />
            No users online right now
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white/80 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-gray-200/50 ${className}`}>
      <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
        <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
        Online Users ({onlineUsers.length})
      </h3>
      
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {onlineUsers.map((onlineUser) => (
          <div 
            key={onlineUser.id} 
            className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gray-50/80 transition-all duration-200 group"
          >
            {/* Profile Image with Online Indicator */}
            <div className="relative">
              <div 
                className={`w-10 h-10 rounded-full border-2 ${getRoleColor(onlineUser.role)} cursor-pointer hover:scale-105 transition-transform duration-200`}
                onClick={() => handleProfileClick(onlineUser)}
              >
                {onlineUser.avatar ? (
                  <img
                    src={onlineUser.avatar}
                    alt={onlineUser.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full rounded-full bg-gradient-to-br from-blue-400 to-green-400 flex items-center justify-center text-white font-semibold">
                    {onlineUser.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              {/* Green Online Indicator */}
              <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-1">
                <p 
                  className="text-sm font-semibold text-gray-800 truncate cursor-pointer hover:text-blue-600 transition-colors"
                  onClick={() => handleProfileClick(onlineUser)}
                >
                  {onlineUser.name}
                </p>
                {getRoleIcon(onlineUser.role)}
                {onlineUser.verified && (
                  <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500 truncate">
                {onlineUser.city || 'Online now'}
              </p>
            </div>

            {/* Message Button */}
            <button
              onClick={() => handleMessageClick(onlineUser)}
              className="p-2 rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 opacity-0 group-hover:opacity-100"
              title={`Message ${onlineUser.name}`}
            >
              <MessageCircle className="w-4 h-4" />
            </button>
          </div>
        ))}
      </div>

      {onlineUsers.length > 5 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Showing {Math.min(onlineUsers.length, 50)} online users
          </p>
        </div>
      )}
    </div>
  );
}
