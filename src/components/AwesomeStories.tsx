'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Plus, Play, Eye, Heart, MessageCircle, Share, Sparkles, Camera, Video, Image as ImageIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { StoriesService } from '@/lib/services/stories-service';
import { UserStories as StoryUserStories, formatStoryTime } from '@/types/stories';
import StoryViewer from './stories/StoryViewer';
import { toast } from 'react-hot-toast';

interface AwesomeStoriesProps {
  onCreateStory?: () => void;
}

export default function AwesomeStories({ onCreateStory }: AwesomeStoriesProps) {
  const { user } = useAuth();
  
  // States
  const [userStories, setUserStories] = useState<StoryUserStories[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showStoryViewer, setShowStoryViewer] = useState(false);
  const [initialUserIndex, setInitialUserIndex] = useState(0);
  const [initialStoryIndex, setInitialStoryIndex] = useState(0);
  
  // Create story states
  const [storyContent, setStoryContent] = useState('');
  const [storyImage, setStoryImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load stories
  const loadStories = useCallback(async () => {
    try {
      setIsLoading(true);
      const stories = await StoriesService.getActiveStories();
      setUserStories(stories);
    } catch (error) {
      console.error('Error loading stories:', error);
      toast.error('Failed to load stories');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load stories on mount
  useEffect(() => {
    loadStories();
  }, [loadStories]);

  // Handle story click
  const handleStoryClick = (userStory: StoryUserStories, storyIndex = 0) => {
    const userIndex = userStories.findIndex(us => us.userId === userStory.userId);
    setInitialUserIndex(userIndex >= 0 ? userIndex : 0);
    setInitialStoryIndex(storyIndex);
    setShowStoryViewer(true);
  };

  // Handle story view tracking
  const handleStoryView = async (storyId: string, userId: string) => {
    if (!user) return;
    
    try {
      await fetch(`/api/stories/${storyId}/view`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ viewerId: user.id })
      });
    } catch (error) {
      console.error('Error tracking story view:', error);
    }
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      setStoryImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Handle story creation
  const handleCreateStory = async () => {
    if (!user) {
      toast.error('Please sign in to create a story');
      return;
    }

    if (!storyContent.trim() && !storyImage) {
      toast.error('Please add some content to your story');
      return;
    }

    setIsSubmitting(true);
    try {
      await StoriesService.createStory({
        userId: user.id,
        userName: user.name || 'Anonymous',
        userAvatar: user.avatar || '/favicon.png',
        mediaUrl: storyImage || '',
        type: 'image',
        content: storyContent || '',
        isPublic: true
      });

      setShowCreateModal(false);
      setStoryContent('');
      setStoryImage(null);
      toast.success('🎉 Story posted successfully!');
      
      // Reload stories
      await loadStories();
      
      if (onCreateStory) {
        onCreateStory();
      }
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to post story');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center space-x-4 overflow-x-auto pb-4 px-4">
          {/* Loading skeletons */}
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex-shrink-0">
              <div className="w-24 h-32 bg-gradient-to-br from-gray-200 to-gray-300 rounded-3xl animate-pulse" />
              <div className="w-16 h-3 bg-gray-200 rounded-full mt-2 mx-auto animate-pulse" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Stories Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              Stories
            </h2>
            <p className="text-sm text-gray-500">Share your moments</p>
          </div>
        </div>
        
        {userStories.length > 0 && (
          <div className="text-sm text-gray-500 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full border border-gray-200">
            {userStories.length} active
          </div>
        )}
      </div>

      {/* Stories Carousel */}
      <div className="flex items-start space-x-4 overflow-x-auto pb-6 px-6 scrollbar-hide">
        {/* Enhanced Create Story Button */}
        {user && (
          <div className="flex-shrink-0 group cursor-pointer" onClick={() => setShowCreateModal(true)}>
            <div className="relative">
              {/* Main Story Card with Enhanced Design */}
              <div className="w-28 h-36 bg-gradient-to-br from-green-50 via-blue-50 to-green-100 rounded-3xl border-2 border-dashed border-green-300 hover:border-green-500 transition-all duration-500 flex flex-col items-center justify-center group-hover:scale-105 group-hover:shadow-2xl relative overflow-hidden">
                {/* Animated Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-100/50 via-blue-100/50 to-green-200/50 animate-pulse" />

                {/* User Avatar Background */}
                <div className="absolute top-3 left-3 w-8 h-8 rounded-full overflow-hidden border-2 border-white shadow-lg">
                  <img
                    src={user.avatar || '/favicon.png'}
                    alt="Your avatar"
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Main Plus Icon */}
                <div className="relative z-10 w-14 h-14 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-500 shadow-xl">
                  <Plus className="w-7 h-7 text-white drop-shadow-lg" />
                </div>

                <span className="relative z-10 text-xs font-bold text-gray-700 text-center px-2">Create Story</span>

                {/* Sparkle Effects */}
                <div className="absolute top-2 right-2 text-yellow-400 animate-bounce">
                  <Sparkles className="w-4 h-4" />
                </div>
              </div>

              {/* Enhanced Floating Action Buttons */}
              <div className="absolute -bottom-3 -right-3 flex flex-col space-y-1">
                <div className="w-7 h-7 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300 border-2 border-white">
                  <Camera className="w-3 h-3 text-white" />
                </div>
                <div className="w-7 h-7 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300 border-2 border-white">
                  <Video className="w-3 h-3 text-white" />
                </div>
                <div className="w-7 h-7 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300 border-2 border-white">
                  <ImageIcon className="w-3 h-3 text-white" />
                </div>
              </div>

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-400/20 via-blue-400/20 to-green-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl" />
            </div>

            <div className="mt-4 text-center">
              <p className="text-sm font-bold text-gray-800">✨ Your Story</p>
              <p className="text-xs text-gray-500 font-medium">Share your moment</p>
            </div>
          </div>
        )}

        {/* User Stories */}
        {userStories.map((userStory, index) => (
          <div key={userStory.userId} className="flex-shrink-0 group cursor-pointer" onClick={() => handleStoryClick(userStory)}>
            <div className="relative">
              {/* Story Card with Enhanced Design */}
              <div className="w-28 h-36 rounded-3xl overflow-hidden group-hover:scale-105 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative">
                {/* Animated Border */}
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 via-blue-500 via-green-600 via-blue-600 to-green-700 rounded-3xl p-0.5 animate-pulse">
                  <div className="w-full h-full bg-white rounded-3xl" />
                </div>

                {/* Background Image */}
                <div className="absolute inset-0.5 rounded-3xl bg-gradient-to-br from-green-500 via-blue-500 to-green-600 relative overflow-hidden">
                  {/* Media Content */}
                  {userStory.latestStory.mediaUrl ? (
                    userStory.latestStory.type === 'video' ? (
                      <video
                        src={userStory.latestStory.mediaUrl}
                        className="w-full h-full object-cover"
                        muted
                        playsInline
                        preload="metadata"
                        poster={userStory.latestStory.thumbnailUrl}
                      />
                    ) : (
                      <img
                        src={userStory.latestStory.mediaUrl}
                        alt="Story thumbnail"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to gradient background if image fails to load
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    )
                  ) : null}
                  {/* Dynamic Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/40" />

                  {/* Floating Particles Effect */}
                  <div className="absolute inset-0 opacity-30">
                    {[...Array(6)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-1 h-1 bg-white rounded-full animate-bounce"
                        style={{
                          left: `${20 + i * 15}%`,
                          top: `${10 + i * 10}%`,
                          animationDelay: `${i * 0.2}s`,
                          animationDuration: '2s'
                        }}
                      />
                    ))}
                  </div>

                  {/* User Avatar with Ring */}
                  <div className="absolute top-3 left-3">
                    <div className="relative">
                      <div className="w-9 h-9 rounded-full bg-gradient-to-r from-green-400 to-blue-400 p-0.5 animate-pulse">
                        <div className="w-full h-full rounded-full border-2 border-white overflow-hidden shadow-lg">
                          <img
                            src={userStory.userAvatar}
                            alt={userStory.userName}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      {/* Online Status */}
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                    </div>
                  </div>

                  {/* Story Count Badge with Animation */}
                  {userStory.stories.length > 1 && (
                    <div className="absolute top-3 right-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg animate-bounce">
                      {userStory.stories.length}
                    </div>
                  )}

                  {/* Video Play Icon Overlay */}
                  {userStory.latestStory.type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-12 bg-black/50 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <Play className="w-6 h-6 text-white ml-1" fill="white" />
                      </div>
                    </div>
                  )}

                  {/* Content Preview with Better Typography */}
                  <div className="absolute bottom-4 left-3 right-3">
                    <p className="text-white text-xs font-semibold truncate drop-shadow-lg">
                      {userStory.latestStory.content || (userStory.latestStory.type === 'video' ? '🎥 Video Story' : '📸 Photo Story')}
                    </p>
                  </div>

                  {/* Unviewed Indicator with Pulse */}
                  {userStory.hasUnviewed && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full border-2 border-white animate-ping" />
                  )}

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <Play className="w-8 h-8 text-white drop-shadow-lg" />
                  </div>
                </div>
              </div>

              {/* Enhanced Story Stats */}
              <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 shadow-lg border border-gray-200 flex items-center space-x-1">
                  <Eye className="w-3 h-3 text-blue-500" />
                  <span className="text-xs font-bold text-gray-700">{userStory.latestStory.views || 0}</span>
                </div>
                {userStory.stories.length > 1 && (
                  <div className="bg-purple-500 text-white rounded-full px-2 py-1 shadow-lg flex items-center space-x-1">
                    <Sparkles className="w-3 h-3" />
                    <span className="text-xs font-bold">{userStory.stories.length}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced User Info */}
            <div className="mt-4 text-center">
              <p className="text-sm font-bold text-gray-800 truncate px-1">
                {userStory.userId === user?.id ? '✨ You' : userStory.userName}
              </p>
              <p className="text-xs text-gray-500 font-medium">
                {formatStoryTime(userStory.latestStory.createdAt)}
              </p>
            </div>
          </div>
        ))}

        {/* Empty State */}
        {userStories.length === 0 && (
          <div className="flex-1 text-center py-12">
            <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl mx-auto mb-4 flex items-center justify-center">
              <Play className="w-10 h-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">No Stories Yet</h3>
            <p className="text-gray-500 text-sm">Be the first to share a story!</p>
          </div>
        )}
      </div>

      {/* Create Story Modal - Rendered outside container using portal */}
      {showCreateModal && typeof window !== 'undefined' && createPortal(
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-white">Create Story</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-white/80 hover:text-white p-2 hover:bg-white/20 rounded-full transition-colors"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Text Content */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  What's on your mind?
                </label>
                <textarea
                  value={storyContent}
                  onChange={(e) => setStoryContent(e.target.value)}
                  placeholder="Share your thoughts..."
                  className="w-full px-4 py-3 rounded-2xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none resize-none h-24 transition-colors"
                />
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Add Photo
                </label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="story-image-upload"
                  />
                  <label
                    htmlFor="story-image-upload"
                    className="block w-full p-4 border-2 border-dashed border-gray-300 rounded-2xl text-center cursor-pointer hover:border-purple-500 hover:bg-purple-50 transition-colors"
                  >
                    <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <span className="text-gray-600">Click to upload image</span>
                  </label>
                </div>
              </div>

              {/* Image Preview */}
              {storyImage && (
                <div className="relative">
                  <img
                    src={storyImage}
                    alt="Story preview"
                    className="w-full h-48 object-cover rounded-2xl"
                  />
                  <button
                    onClick={() => setStoryImage(null)}
                    className="absolute top-3 right-3 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors"
                  >
                    ×
                  </button>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-2xl hover:bg-gray-50 transition-colors font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateStory}
                  disabled={isSubmitting || (!storyContent.trim() && !storyImage)}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-2xl hover:from-green-600 hover:to-blue-600 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-semibold shadow-lg"
                >
                  {isSubmitting ? 'Sharing...' : 'Share Story'}
                </button>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Full-screen Story Viewer - Rendered outside container using portal */}
      {showStoryViewer && typeof window !== 'undefined' && createPortal(
        <div className="fixed inset-0 bg-black z-[9999] flex flex-col">
          <StoryViewer 
            userStories={userStories}
            initialUserIndex={initialUserIndex}
            initialStoryIndex={initialStoryIndex}
            onClose={() => setShowStoryViewer(false)}
            onStoryView={handleStoryView}
          />
        </div>,
        document.body
      )}
    </div>
  );
}
