'use client';

import React, { useState } from 'react';
import { Mail, Check, AlertCircle, Loader2 } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';

interface NewsletterSubscriptionProps {
  source?: string;
  className?: string;
  placeholder?: string;
  buttonText?: string;
  title?: string;
  description?: string;
  compact?: boolean;
}

export default function NewsletterSubscription({
  source = 'newsletter',
  className = '',
  placeholder,
  buttonText,
  title,
  description,
  compact = false
}: NewsletterSubscriptionProps) {
  const [email, setEmail] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const { subscribe, isLoading, error } = useSubscription();

  // Default English text
  const finalPlaceholder = placeholder || 'Enter your email';
  const finalButtonText = buttonText || 'Subscribe';
  const finalTitle = title || 'Stay Updated';
  const finalDescription = description || 'Subscribe to our newsletter for the latest updates and offers.';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) return;

    const result = await subscribe(email.trim(), source);
    
    if (result.success) {
      setShowSuccess(true);
      setEmail('');
      
      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
    }
  };

  if (showSuccess) {
    return (
      <div className={`${compact ? 'p-4' : 'p-6'} ${className}`}>
        <div className="flex items-center justify-center space-x-3 text-green-600">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <Check className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-semibold">Successfully Subscribed!</h3>
            <p className="text-sm text-gray-600">Thank you for subscribing to our newsletter.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${compact ? 'p-4' : 'p-6'} ${className}`}>
      {!compact && (
        <div className="text-center mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Mail className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-lg font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
            {finalTitle}
          </h3>
          <p className="text-gray-600 text-sm">
            {finalDescription}
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={finalPlaceholder}
            required
            disabled={isLoading}
            className={`w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 ${
              error ? 'border-red-300 bg-red-50' : ''
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          />
        </div>

        {error && (
          <div className="flex items-center space-x-2 text-red-600 text-sm">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}

        <button
          type="submit"
          disabled={isLoading || !email.trim()}
          className={`w-full flex items-center justify-center space-x-2 py-3 px-6 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 font-semibold shadow-lg ${
            isLoading || !email.trim() 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:shadow-xl transform hover:-translate-y-0.5'
          }`}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Subscribing...</span>
            </>
          ) : (
            <>
              <Mail className="w-4 h-4" />
              <span>{finalButtonText}</span>
            </>
          )}
        </button>
      </form>

      {!compact && (
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      )}
    </div>
  );
}

// Compact version for footers, sidebars, etc.
export function CompactNewsletterSubscription(props: Omit<NewsletterSubscriptionProps, 'compact'>) {
  return <NewsletterSubscription {...props} compact={true} />;
}

// Inline version for within content
export function InlineNewsletterSubscription({
  source = 'inline',
  className = '',
  ...props
}: NewsletterSubscriptionProps) {
  const [email, setEmail] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const { subscribe, isLoading, error } = useSubscription();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) return;

    const result = await subscribe(email.trim(), source);
    
    if (result.success) {
      setShowSuccess(true);
      setEmail('');
      setTimeout(() => setShowSuccess(false), 5000);
    }
  };

  if (showSuccess) {
    return (
      <div className={`inline-flex items-center space-x-2 text-green-600 ${className}`}>
        <Check className="w-4 h-4" />
        <span className="text-sm font-medium">Subscribed successfully!</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className={`inline-flex items-center space-x-2 ${className}`}>
      <div className="relative">
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Your email"
          required
          disabled={isLoading}
          className="pl-3 pr-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
        />
      </div>
      
      <button
        type="submit"
        disabled={isLoading || !email.trim()}
        className="px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          'Subscribe'
        )}
      </button>
      
      {error && (
        <div className="text-red-600 text-xs">
          {error}
        </div>
      )}
    </form>
  );
}
