'use client';

import { useMemo } from 'react';
import { Check, X, Shield, AlertTriangle } from 'lucide-react';
import { 
  calculatePasswordStrength, 
  getPasswordRequirements, 
  getPasswordStrengthLabel,
  type PasswordStrength,
  type PasswordRequirement
} from '@/lib/utils/password-strength';

interface PasswordStrengthIndicatorProps {
  password: string;
  showRequirements?: boolean;
  className?: string;
}

export default function PasswordStrengthIndicator({ 
  password, 
  showRequirements = true,
  className = '' 
}: PasswordStrengthIndicatorProps) {
  const strength = useMemo(() => calculatePasswordStrength(password), [password]);
  const requirements = useMemo(() => getPasswordRequirements(password), [password]);

  if (!password) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Password Strength
          </span>
          <div className="flex items-center space-x-2">
            {strength.isValid ? (
              <Shield className="w-4 h-4 text-green-600" />
            ) : (
              <AlertTriangle className="w-4 h-4 text-orange-500" />
            )}
            <span 
              className="text-sm font-semibold"
              style={{ color: strength.color }}
            >
              {getPasswordStrengthLabel(strength.level)}
            </span>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div
            className="h-full transition-all duration-300 ease-out rounded-full"
            style={{
              width: `${strength.percentage}%`,
              backgroundColor: strength.color
            }}
          />
        </div>
        
        {/* Feedback */}
        {strength.feedback.length > 0 && (
          <div className="space-y-1">
            {strength.feedback.map((feedback, index) => (
              <p 
                key={index}
                className={`text-xs ${
                  feedback.includes('✓') 
                    ? 'text-green-600' 
                    : 'text-gray-600'
                }`}
              >
                {feedback}
              </p>
            ))}
          </div>
        )}
      </div>

      {/* Requirements Checklist */}
      {showRequirements && (
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span>Password Requirements</span>
          </h4>
          
          <div className="space-y-2">
            {requirements.map((requirement, index) => (
              <div 
                key={index}
                className="flex items-center space-x-3"
              >
                <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                  requirement.met 
                    ? 'bg-green-100 text-green-600' 
                    : 'bg-gray-100 text-gray-400'
                }`}>
                  {requirement.met ? (
                    <Check className="w-3 h-3" />
                  ) : (
                    <X className="w-3 h-3" />
                  )}
                </div>
                <span className={`text-sm ${
                  requirement.met 
                    ? 'text-green-700 font-medium' 
                    : 'text-gray-600'
                }`}>
                  {requirement.text}
                </span>
              </div>
            ))}
          </div>
          
          {/* Security Tips */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <h5 className="text-xs font-semibold text-blue-800 mb-2">💡 Security Tips</h5>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Use a unique password for your Fetchly account</li>
              <li>• Consider using a password manager</li>
              <li>• Avoid personal information (names, birthdays)</li>
              <li>• Mix different types of characters</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

// Export strength calculation for external use
export { calculatePasswordStrength, getPasswordRequirements };
