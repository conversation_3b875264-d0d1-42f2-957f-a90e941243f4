'use client';

import Link from 'next/link';
import { Heart, Mail, Phone, MapPin } from 'lucide-react';
import { CompactNewsletterSubscription } from '@/components/NewsletterSubscription';

export function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="container mx-auto px-4 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <img
                src="/fetchlylogo.png"
                alt="Fetchly"
                className="h-8 w-auto"
              />
            </Link>
            <p className="text-gray-700 mb-6 leading-relaxed">
              Connecting pet parents with trusted, verified pet care professionals.
              Your pet's happiness and wellbeing is our mission.
            </p>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-bold text-gray-800 mb-6">Services</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/search?service=grooming" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Pet Grooming
                </Link>
              </li>
              <li>
                <Link href="/search?service=veterinary" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Veterinary Care
                </Link>
              </li>
              <li>
                <Link href="/search?service=hotel" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Pet Hotels
                </Link>
              </li>
              <li>
                <Link href="/search?service=daycare" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Pet Daycare
                </Link>
              </li>
              <li>
                <Link href="/search?service=training" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Pet Training
                </Link>
              </li>
              <li>
                <Link href="/emergency" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Emergency Care
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-bold text-gray-800 mb-6">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/providers" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  For Providers
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Pet Care Blog
                </Link>
              </li>
              <li>
                <Link href="/community" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Community
                </Link>
              </li>
              <li>
                <Link href="/press" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-bold text-gray-800 mb-6">Support</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/help" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/safety" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Safety Guidelines
                </Link>
              </li>
              <li>
                <Link href="/insurance" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                  Insurance
                </Link>
              </li>
              <li className="mt-4">
                <form onSubmit={async (e) => {
                  e.preventDefault();
                  const form = e.target as HTMLFormElement;
                  const formData = new FormData(form);
                  const email = formData.get('email') as string;
                  
                  try {
                    const response = await fetch('/api/subscribe', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({ email, source: 'footer' }),
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                      // Show success message
                      form.reset();
                      alert('Thank you for subscribing!');
                    } else {
                      alert(result.message || 'Failed to subscribe. Please try again.');
                    }
                  } catch (error) {
                    console.error('Subscription error:', error);
                    alert('An error occurred. Please try again later.');
                  }
                }} className="space-y-2">
                  <div className="flex gap-2">
                    <input
                      name="email"
                      type="email"
                      required
                      placeholder="Your email"
                      className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                    <button 
                      type="submit"
                      className="bg-green-600 hover:bg-green-700 text-white text-sm font-medium px-4 py-2 rounded-lg transition-colors duration-200"
                    >
                      Subscribe
                    </button>
                  </div>
                </form>
              </li>
            </ul>
          </div>
        </div>



        {/* Bottom Bar */}
        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-600 text-sm">
              © 2025 Fetchly. All rights reserved.
            </div>

            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <Link href="/legal/privacy-policy" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                Privacy Policy
              </Link>
              <Link href="/legal/terms-of-service" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                Terms of Service
              </Link>
              <Link href="/legal/cookie-policy" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                Cookie Policy
              </Link>
              <Link href="/legal/accessibility" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                Accessibility
              </Link>
              <Link href="/legal/disclaimer" className="text-gray-600 hover:text-green-600 transition-colors duration-200">
                Legal Disclaimer
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
