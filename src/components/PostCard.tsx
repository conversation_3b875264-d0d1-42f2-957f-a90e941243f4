'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import NoSSR from './NoSSR';

import {
  Heart, MessageCircle, Share2, MoreHorizontal, Edit, Trash2,
  Globe, Lock, Eye, EyeOff
} from 'lucide-react';
import { Post } from '@/contexts/DataContext';
import CommentsModal from './CommentsModal';
import ShareModal from './ShareModal';

interface PostCardProps {
  post: Post;
  showPrivacyIndicator?: boolean;
  onEdit?: (post: Post) => void;
  onDelete?: (postId: string) => void;
  onImageClick?: (imageUrl: string, title: string) => void;
}

export default function PostCard({
  post,
  showPrivacyIndicator = false,
  onEdit,
  onDelete,
  onImageClick
}: PostCardProps) {
  const { user } = useAuth();
  const { toggleLike } = useData();
  const router = useRouter();
  const [showMenu, setShowMenu] = useState(false);
  const [isLiking, setIsLiking] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [showShare, setShowShare] = useState(false);

  const isOwnPost = post.userId === user?.id;
  const isLiked = user ? post.likedBy.includes(user.id) : false;

  // Handle username click to navigate to profile
  const handleUsernameClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!post.userId) return;

    try {
      // Get the user's actual role from Firestore
      const userDoc = await getDoc(doc(db, 'users', post.userId));
      const userData = userDoc.data();
      const userRole = userData?.role;

      console.log('🔍 User role for', post.userName, ':', userRole);

      if (userRole === 'provider') {
        console.log('🏢 Navigating to provider profile:', `/provider/public/${post.userId}`);
        router.push(`/provider/public/${post.userId}`);
      } else {
        console.log('🐕 Navigating to pet owner profile:', `/profile?id=${post.userId}`);
        router.push(`/profile?id=${post.userId}`);
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
      // Fallback to pet owner profile
      router.push(`/profile?id=${post.userId}`);
    }
  };

  const handleLike = async () => {
    if (isLiking || !user) return;

    try {
      setIsLiking(true);
      await toggleLike(post.id);
    } catch (error) {
      console.error('Error liking post:', error);
    } finally {
      setIsLiking(false);
    }
  };

  const handleShare = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Check out this post on Fetchly',
          text: post.content,
          url: `${window.location.origin}/posts/${post.id}`,
        });
      } else {
        // Fallback for browsers that don't support Web Share API
        await navigator.clipboard.writeText(`${window.location.origin}/posts/${post.id}`);
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      // User cancelled the share or an error occurred
      console.error('Error sharing post:', error);
    } finally {
      setIsLiking(false);
    }
  };

  const formatTimeAgo = (timestamp: any) => {
    try {
      // Handle Firebase Timestamp or Date object
      let date: Date;

      if (!timestamp) {
        return 'Just now';
      }

      // If it's a Firebase Timestamp
      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      }
      // If it's already a Date object
      else if (timestamp instanceof Date) {
        date = timestamp;
      }
      // If it's a timestamp number
      else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      }
      // If it's a string
      else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      }
      // Fallback
      else {
        return 'Just now';
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        return 'Just now';
      }

      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 60) return 'Just now';
      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
      if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
      if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting time:', error, 'timestamp:', timestamp);
      return 'Just now';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden"
    >
      {/* Post Header */}
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <img
              src={post.userAvatar || 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face'}
              alt={post.userName}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleUsernameClick}
                  className="font-semibold text-gray-900 hover:text-blue-600 transition-colors cursor-pointer"
                >
                  {post.userName}
                </button>
                {showPrivacyIndicator && (
                  <div className="flex items-center space-x-1">
                    {post.isPublic ? (
                      <Globe className="w-3 h-3 text-green-600" />
                    ) : (
                      <Lock className="w-3 h-3 text-gray-600" />
                    )}
                  </div>
                )}
              </div>
              <NoSSR fallback={<p className="text-sm text-gray-500">Just now</p>}>
                <p className="text-sm text-gray-500">{formatTimeAgo(post.timestamp)}</p>
              </NoSSR>
            </div>
          </div>
          
          {isOwnPost && (
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <MoreHorizontal className="w-5 h-5" />
              </button>
              
              {showMenu && (
                <div className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10 min-w-[120px]">
                  {onEdit && (
                    <button
                      onClick={() => {
                        onEdit(post);
                        setShowMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                    >
                      <Edit className="w-4 h-4" />
                      <span>Edit</span>
                    </button>
                  )}
                  
                  {onDelete && (
                    <button
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this post?')) {
                          onDelete(post.id);
                        }
                        setShowMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Delete</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        <p className="text-gray-900 whitespace-pre-wrap">{post.content}</p>
      </div>

      {/* Post Image */}
      {post.image && (
        <div className="px-4 pb-3">
          <div className="relative group">
            <img
              src={post.image}
              alt="Post image"
              className="w-full rounded-lg object-cover max-h-96 cursor-pointer hover:opacity-90 transition-all duration-200 group-hover:scale-[1.02]"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖼️ Image clicked:', post.image);
                if (post.image && onImageClick) {
                  onImageClick(post.image, `Post by ${post.userName}`);
                }
              }}
              onError={(e) => {
                console.error('Post image failed to load:', post.image);
                e.currentTarget.src = '/fetchlylogo.png';
              }}
              onLoad={() => {
                console.log('Post image loaded:', post.image);
              }}
            />
            {/* Click overlay indicator */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
              <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 transform scale-90 group-hover:scale-100 transition-transform duration-200">
                <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Post Actions */}
      <div className="px-4 py-3 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={handleLike}
              disabled={isLiking || !user}
              className={`flex items-center space-x-2 transition-colors ${
                isLiked
                  ? 'text-red-600 hover:text-red-700'
                  : 'text-gray-600 hover:text-red-600'
              } ${isLiking || !user ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={!user ? 'Sign in to like posts' : undefined}
            >
              <div className="flex items-center">
                <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                <span className="text-sm font-medium ml-2">{post.likes}</span>
              </div>
            </button>

            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (!user) {
                  // Show login prompt or redirect to sign in
                  const shouldLogin = confirm('Please sign in to view and post comments. Would you like to sign in now?');
                  if (shouldLogin) {
                    router.push(`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`);
                  }
                } else {
                  setShowComments(true);
                }
              }}
              className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
              title={!user ? 'Sign in to comment' : 'View comments'}
            >
              <div className="flex items-center">
                <MessageCircle className="w-5 h-5" />
                <span className="text-sm font-medium ml-2">{post.comments}</span>
              </div>
            </button>

            <button
              onClick={() => setShowShare(true)}
              className="flex items-center space-x-2 text-gray-600 hover:text-green-600 transition-colors"
              title="Share post"
            >
              <div className="flex items-center">
                <Share2 className="w-5 h-5" />
                <span className="text-sm font-medium ml-2">Share</span>
              </div>
            </button>
          </div>
          
          {showPrivacyIndicator && isOwnPost && (
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              {post.isPublic ? (
                <>
                  <Eye className="w-3 h-3" />
                  <span>Public</span>
                </>
              ) : (
                <>
                  <EyeOff className="w-3 h-3" />
                  <span>Private</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowMenu(false)}
        />
      )}

      {/* Modals */}
      <CommentsModal
        postId={post.id}
        postAuthor={post.userName}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
      />

      <ShareModal
        post={post}
        isOpen={showShare}
        onClose={() => setShowShare(false)}
      />
    </motion.div>
  );
}
