'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  PawPrint,
  CreditCard,
  Send,
  FileText,
  Filter,
  Search
} from 'lucide-react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { apiClient } from '@/lib/api-client';
import toast from 'react-hot-toast';

interface Booking {
  id: string;
  providerId: string;
  providerName: string;
  userId: string;
  userName: string;
  userEmail: string;
  serviceName: string;
  petName: string;
  scheduledDate: string;
  scheduledTime: string;
  duration: number;
  totalPrice: number;
  finalPrice?: number;
  status: string;
  paymentStatus: string;
  notes: string;
  createdAt: any;
  workflow?: {
    step: number;
    description: string;
    nextAction: string;
  };
}

export default function NewBookingsTab() {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [adjustedPrice, setAdjustedPrice] = useState<number | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');

  // Load bookings
  useEffect(() => {
    const loadBookings = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        console.log('📋 Loading bookings for provider:', user.id);

        const bookingsRef = collection(db, 'bookings');
        const q = query(
          bookingsRef,
          where('providerId', '==', user.id),
          orderBy('createdAt', 'desc')
        );

        const querySnapshot = await getDocs(q);
        const bookingData: Booking[] = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          bookingData.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate() || new Date(),
          } as Booking);
        });

        console.log('📋 Loaded bookings:', bookingData.length);
        setBookings(bookingData);
      } catch (error) {
        console.error('❌ Error loading bookings:', error);
        toast.error('Failed to load bookings');
      } finally {
        setLoading(false);
      }
    };

    loadBookings();
  }, [user?.id]);

  // Handle booking approval
  const handleApproveBooking = async () => {
    if (!selectedBooking) return;

    setIsProcessing(true);
    try {
      const response = await apiClient.post('/api/bookings/approve-and-invoice', {
        bookingId: selectedBooking.id,
        approvalNotes,
        adjustedPrice: adjustedPrice || selectedBooking.totalPrice
      });

      if (response.success) {
        toast.success('🎉 Booking approved and invoice sent to customer!');
        
        // Update local state
        setBookings(prev => prev.map(booking => 
          booking.id === selectedBooking.id 
            ? { 
                ...booking, 
                status: 'approved_awaiting_payment',
                finalPrice: adjustedPrice || booking.totalPrice
              }
            : booking
        ));

        setShowApprovalModal(false);
        setSelectedBooking(null);
        setApprovalNotes('');
        setAdjustedPrice(null);
      } else {
        toast.error(response.error || 'Failed to approve booking');
      }
    } catch (error: any) {
      console.error('❌ Error approving booking:', error);
      toast.error(error.message || 'Failed to approve booking');
    } finally {
      setIsProcessing(false);
    }
  };

  // Filter bookings
  const filteredBookings = bookings.filter(booking => {
    if (statusFilter === 'all') return true;
    return booking.status === statusFilter;
  });

  // Get status badge
  const getStatusBadge = (status: string, paymentStatus: string) => {
    if (paymentStatus === 'paid') {
      return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Confirmed</span>;
    }
    
    switch (status) {
      case 'pending_provider_approval':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">Pending Approval</span>;
      case 'approved_awaiting_payment':
        return <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">Awaiting Payment</span>;
      case 'confirmed':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Confirmed</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">{status}</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading bookings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Bookings</h2>
          <p className="text-gray-600">Manage your service bookings and payments</p>
        </div>
        
        {/* Filter */}
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Bookings</option>
          <option value="pending_provider_approval">Pending Approval</option>
          <option value="approved_awaiting_payment">Awaiting Payment</option>
          <option value="confirmed">Confirmed</option>
        </select>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <span className="text-sm text-gray-600">Pending</span>
          </div>
          <p className="text-2xl font-bold text-gray-800">
            {bookings.filter(b => b.status === 'pending_provider_approval').length}
          </p>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-blue-600" />
            <span className="text-sm text-gray-600">Awaiting Payment</span>
          </div>
          <p className="text-2xl font-bold text-gray-800">
            {bookings.filter(b => b.status === 'approved_awaiting_payment').length}
          </p>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-600">Confirmed</span>
          </div>
          <p className="text-2xl font-bold text-gray-800">
            {bookings.filter(b => b.paymentStatus === 'paid').length}
          </p>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-600">Total Revenue</span>
          </div>
          <p className="text-2xl font-bold text-gray-800">
            ${bookings
              .filter(b => b.paymentStatus === 'paid')
              .reduce((sum, b) => sum + (b.finalPrice || b.totalPrice), 0)
              .toFixed(2)}
          </p>
        </div>
      </div>

      {/* Bookings List */}
      <div className="bg-white rounded-lg shadow-sm border">
        {filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">No bookings found</h3>
            <p className="text-gray-600">
              {statusFilter === 'all' 
                ? 'You haven\'t received any bookings yet.' 
                : `No bookings with status: ${statusFilter}`}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="p-6 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-800">{booking.userName}</h3>
                        <p className="text-sm text-gray-600">{booking.userEmail}</p>
                      </div>
                      {getStatusBadge(booking.status, booking.paymentStatus)}
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <PawPrint className="w-4 h-4 text-gray-500" />
                        <span>{booking.serviceName} for {booking.petName}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span>{booking.scheduledDate}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span>{booking.scheduledTime}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-gray-500" />
                        <span>${(booking.finalPrice || booking.totalPrice).toFixed(2)}</span>
                      </div>
                    </div>
                    
                    {booking.notes && (
                      <p className="text-sm text-gray-600 mt-2">
                        <strong>Notes:</strong> {booking.notes}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    {booking.status === 'pending_provider_approval' && (
                      <button
                        onClick={() => {
                          setSelectedBooking(booking);
                          setAdjustedPrice(booking.totalPrice);
                          setShowApprovalModal(true);
                        }}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                      >
                        <CheckCircle className="w-4 h-4" />
                        Approve & Send Invoice
                      </button>
                    )}
                    
                    <button
                      onClick={() => setSelectedBooking(booking)}
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Approval Modal */}
      {showApprovalModal && selectedBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl w-full max-w-md">
            <div className="p-6 border-b">
              <h3 className="text-xl font-bold text-gray-800">Approve Booking</h3>
              <p className="text-gray-600">Send invoice to {selectedBooking.userName}</p>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Final Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={adjustedPrice || ''}
                  onChange={(e) => setAdjustedPrice(parseFloat(e.target.value) || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter final price"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes for Customer (Optional)
                </label>
                <textarea
                  value={approvalNotes}
                  onChange={(e) => setApprovalNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Any additional notes or instructions..."
                />
              </div>
            </div>
            
            <div className="p-6 border-t flex gap-3">
              <button
                onClick={() => setShowApprovalModal(false)}
                className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={isProcessing}
              >
                Cancel
              </button>
              <button
                onClick={handleApproveBooking}
                disabled={isProcessing || !adjustedPrice}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4" />
                    Send Invoice
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
