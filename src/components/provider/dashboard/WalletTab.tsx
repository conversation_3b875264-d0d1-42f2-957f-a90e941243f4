'use client';

import { useState, useMemo, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  Wallet,
  ArrowUpRight,
  ArrowDownLeft,
  ExternalLink,
  Plus,
  Settings,
  Clock,
  BarChart3
} from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import toast from 'react-hot-toast';

interface StripeOnboardingStatus {
  isOnboarded: boolean;
  accountId?: string;
  status?: {
    detailsSubmitted: boolean;
    chargesEnabled: boolean;
    payoutsEnabled: boolean;
  };
}

interface StripeBalance {
  available: number;
  pending: number;
}

interface StripePayout {
  id: string;
  amount: number;
  currency: string;
  status: string;
  arrival_date: number;
  created: number;
}

export default function WalletTab() {
  const { earnings, payouts, isLoading } = useProvider();
  const { user, getIdToken } = useAuth();
  const [timeFilter, setTimeFilter] = useState<string>('month');
  const [transactionFilter, setTransactionFilter] = useState<string>('all');
  const [stripeOnboardingStatus, setStripeOnboardingStatus] = useState<StripeOnboardingStatus | null>(null);
  const [stripeBalance, setStripeBalance] = useState<StripeBalance | null>(null);
  const [stripePayouts, setStripePayouts] = useState<StripePayout[]>([]);
  const [isLoadingStripe, setIsLoadingStripe] = useState(true);
  const [showCreateInvoiceModal, setShowCreateInvoiceModal] = useState(false);

  // Load Stripe data on component mount
  useEffect(() => {
    if (user?.id) {
      loadStripeData();
    }
  }, [user?.id]);

  const loadStripeData = async () => {
    if (!user) {
      console.log('No user found, skipping Stripe data load');
      return;
    }

    try {
      setIsLoadingStripe(true);
      console.log('Loading Stripe data for user:', user.id);

      const token = await getIdToken();
      console.log('Got ID token, making API call...');

      // Get real wallet data from our API
      const walletResponse = await fetch('/api/providers/wallet', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Wallet API response status:', walletResponse.status);

      if (walletResponse.ok) {
        const walletData = await walletResponse.json();
        console.log('Wallet data received:', walletData);

        if (walletData.success) {
          // Set real Stripe data
          setStripeOnboardingStatus({
            isOnboarded: walletData.isOnboarded,
            accountId: walletData.accountId,
            status: walletData.status,
          });

          setStripeBalance({
            available: walletData.balance.available,
            pending: walletData.balance.pending,
          });

          setStripePayouts(walletData.payouts || []);

          if (walletData.error) {
            toast.error(walletData.error);
          }
        } else {
          throw new Error(walletData.error || 'Failed to load wallet data');
        }
      } else {
        const errorText = await walletResponse.text();
        console.error('API Error Response:', errorText);
        throw new Error(`API Error: ${walletResponse.status} - ${errorText}`);
      }
    } catch (error: any) {
      console.error('Error loading Stripe data:', error);

      if (error.message.includes('Not authenticated')) {
        toast.error('Please sign in to view wallet data');
      } else {
        toast.error('Failed to load wallet data. Please try again.');
      }

      // Set empty state instead of demo data
      setStripeOnboardingStatus({
        isOnboarded: false,
        accountId: undefined,
      });
      setStripeBalance({ available: 0, pending: 0 });
      setStripePayouts([]);
    } finally {
      setIsLoadingStripe(false);
    }
  };

  // Handle Stripe Connect onboarding
  const handleStripeOnboarding = async () => {
    if (!user) {
      toast.error('Please sign in to continue');
      return;
    }

    try {
      const token = await getIdToken();
      
      const response = await fetch('/api/stripe/onboard', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          email: user.email,
          returnUrl: `${window.location.origin}/provider/dashboard?tab=wallet`,
        }),
      });

      const data = await response.json();
      
      if (data.success && data.url) {
        window.location.href = data.url;
      } else {
        throw new Error(data.error || 'Failed to create onboarding link');
      }
    } catch (error: any) {
      console.error('Error starting Stripe onboarding:', error);
      toast.error('Failed to start onboarding. Please try again.');
    }
  };

  // Calculate wallet statistics
  const walletStats = useMemo(() => {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    let thisMonthEarnings = 0;
    let lastMonthEarnings = 0;
    let totalEarnings = 0;

    if (earnings) {
      earnings.forEach((earning) => {
        const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
        totalEarnings += earning.amount;

        if (earningDate >= thisMonth) {
          thisMonthEarnings += earning.amount;
        } else if (earningDate >= lastMonth && earningDate < thisMonth) {
          lastMonthEarnings += earning.amount;
        }
      });
    }

    const monthlyGrowth = lastMonthEarnings > 0 
      ? ((thisMonthEarnings - lastMonthEarnings) / lastMonthEarnings) * 100 
      : thisMonthEarnings > 0 ? 100 : 0;

    return {
      total: totalEarnings,
      thisMonth: thisMonthEarnings,
      lastMonth: lastMonthEarnings,
      monthlyGrowth,
      pendingPayouts: payouts?.filter(p => p.status === 'pending').length || 0,
    };
  }, [earnings, payouts]);

  if (isLoading || isLoadingStripe) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If not onboarded with Stripe, show onboarding component
  if (!stripeOnboardingStatus?.isOnboarded) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Fetchly Wallet</h2>
              <p className="text-gray-600">Connect with Stripe to start receiving payments</p>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Connect Your Stripe Account</h3>
            <p className="text-blue-700 mb-4">
              To receive payments from clients, you need to connect your Stripe account. This is secure and takes just a few minutes.
            </p>
            <button
              onClick={handleStripeOnboarding}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Connect Stripe Account
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Settings className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-medium text-gray-800 mb-1">Quick Setup</h4>
              <p className="text-sm text-gray-600">Connect in under 5 minutes</p>
            </div>
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-800 mb-1">Secure Payments</h4>
              <p className="text-sm text-gray-600">Bank-level security</p>
            </div>
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-medium text-gray-800 mb-1">Fast Payouts</h4>
              <p className="text-sm text-gray-600">Get paid in 2-3 business days</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Wallet Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Available Balance</p>
              <p className="text-2xl font-bold text-gray-800">${stripeBalance?.available.toFixed(2) || '0.00'}</p>
              <p className="text-sm text-green-600 mt-1">Ready to withdraw</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Balance</p>
              <p className="text-2xl font-bold text-gray-800">${stripeBalance?.pending.toFixed(2) || '0.00'}</p>
              <p className="text-sm text-yellow-600 mt-1">Processing</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.thisMonth.toFixed(2)}</p>
              <div className="flex items-center mt-1">
                {walletStats.monthlyGrowth >= 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${walletStats.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(walletStats.monthlyGrowth).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Earnings</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.total.toFixed(2)}</p>
              <p className="text-sm text-blue-600 mt-1">All time</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setShowCreateInvoiceModal(true)}
            className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-4 rounded-xl hover:from-green-700 hover:to-blue-700 transition-all flex items-center justify-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>Create Invoice</span>
          </button>
          <button
            onClick={() => window.open('https://dashboard.stripe.com', '_blank')}
            className="bg-white border-2 border-gray-200 text-gray-700 p-4 rounded-xl hover:border-blue-300 hover:text-blue-600 transition-all flex items-center justify-center space-x-2"
          >
            <ExternalLink className="w-5 h-5" />
            <span>Stripe Dashboard</span>
          </button>
          <button className="bg-white border-2 border-gray-200 text-gray-700 p-4 rounded-xl hover:border-green-300 hover:text-green-600 transition-all flex items-center justify-center space-x-2">
            <Download className="w-5 h-5" />
            <span>Tax Documents</span>
          </button>
        </div>
      </div>
    </div>
  );
}
