'use client';

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamically import the EnhancedWallet component with no SSR
const EnhancedWallet = dynamic(
  () => import('./EnhancedWallet'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    )
  }
);

const WalletDashboard = () => {
  return <EnhancedWallet />;
};

export default WalletDashboard;
