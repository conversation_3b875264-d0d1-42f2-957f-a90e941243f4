'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { doc, onSnapshot, collection, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { stripeService } from '@/lib/services/stripe';
import { format } from 'date-fns';
import { Loader2, ExternalLink, Zap } from 'lucide-react';

interface StripeAccount {
  id: string;
  isConnected: boolean;
  email?: string;
  payouts_enabled?: boolean;
  charges_enabled?: boolean;
  requirements?: {
    currently_due: string[];
    past_due: string[];
  };
}

interface Balance {
  available: number;
  pending: number;
  currency: string;
}

interface Transaction {
  id: string;
  amount: number;
  currency: string;
  description: string;
  created: number;
  type: string;
  status: string;
}

export default function EnhancedWallet() {
  const { user } = useAuth();
  const [stripeAccount, setStripeAccount] = useState<StripeAccount | null>(null);
  const [balance, setBalance] = useState<Balance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  // Handle Stripe connection
  const connectStripe = async () => {
    if (!user?.id || !user?.email) return;
    
    setIsLoading(true);
    try {
      const response = await fetch('/api/stripe/onboard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          email: user.email,
          returnUrl: `${window.location.origin}/provider/dashboard?tab=wallet`,
        }),
      });
      
      const data = await response.json();
      if (data.url) window.location.href = data.url;
    } catch (err) {
      setError('Failed to connect to Stripe');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Open Stripe dashboard
  const openStripeDashboard = async () => {
    try {
      const url = await stripeService.getDashboardLink();
      window.open(url, '_blank');
    } catch (err) {
      setError('Failed to open Stripe dashboard');
      console.error(err);
    }
  };

  // Fetch account data
  useEffect(() => {
    if (!user?.id) return;

    const accountUnsubscribe = onSnapshot(
      doc(db, 'stripeAccounts', user.id),
      async (doc) => {
        if (doc.exists()) {
          const accountData = doc.data() as StripeAccount;
          setStripeAccount(accountData);
          
          if (accountData.isConnected) {
            try {
              setIsLoadingBalance(true);
              const balanceData = await stripeService.getBalance();
              setBalance(balanceData);
            } catch (err) {
              console.error('Error fetching balance:', err);
              setError('Failed to load balance');
            } finally {
              setIsLoadingBalance(false);
            }
          }
        }
      },
      (error) => {
        console.error('Error fetching account:', error);
        setError('Failed to load account');
      }
    );

    // Subscribe to transactions
    const transactionsQuery = query(
      collection(db, 'transactions'),
      where('userId', '==', user.id),
      orderBy('created', 'desc'),
      limit(10)
    );
    
    const transactionsUnsubscribe = onSnapshot(
      transactionsQuery,
      (snapshot) => {
        const transactionsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Transaction[];
        setTransactions(transactionsData);
      },
      (error) => {
        console.error('Error fetching transactions:', error);
        setError('Failed to load transactions');
      }
    );

    return () => {
      accountUnsubscribe();
      transactionsUnsubscribe();
    };
  }, [user?.id]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* Connect Stripe Banner */}
      {!stripeAccount?.isConnected ? (
        <div className="bg-indigo-50 border-l-4 border-indigo-400 p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Zap className="h-5 w-5 text-indigo-400 mr-3" />
              <p className="text-sm text-indigo-700">
                Connect your Stripe account to receive payments
              </p>
            </div>
            <button
              onClick={connectStripe}
              disabled={isLoading}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              {isLoading ? 'Connecting...' : 'Connect Stripe'}
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Available Balance */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Available Balance</h3>
            <div className="mt-2 flex items-baseline">
              {isLoadingBalance ? (
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              ) : (
                <>
                  <span className="text-3xl font-bold text-gray-900">
                    {balance ? formatCurrency(balance.available, balance.currency) : '$0.00'}
                  </span>
                  <button
                    onClick={openStripeDashboard}
                    className="ml-4 text-sm text-indigo-600 hover:text-indigo-500 flex items-center"
                  >
                    View in Stripe <ExternalLink className="ml-1 h-4 w-4" />
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Pending Balance */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Pending Balance</h3>
            <div className="mt-2">
              {isLoadingBalance ? (
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              ) : (
                <span className="text-3xl font-bold text-gray-900">
                  {balance ? formatCurrency(balance.pending, balance.currency) : '$0.00'}
                </span>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Will be available in 2-5 business days
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Transactions */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Recent Transactions</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {transactions.length > 0 ? (
            transactions.map((transaction) => (
              <div key={transaction.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {transaction.description || 'Payment'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {format(new Date(transaction.created * 1000), 'MMM d, yyyy')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {transaction.type === 'payout' ? '-' : ''}
                      {formatCurrency(transaction.amount, transaction.currency)}
                    </p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      transaction.status === 'succeeded' || transaction.status === 'paid'
                        ? 'bg-green-100 text-green-800'
                        : transaction.status === 'pending' || transaction.status === 'in_transit'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="p-6 text-center text-gray-500">
              No transactions found
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
