'use client';

import { useState, useEffect } from 'react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { 
  CreditCard, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  DollarSign,
  Users,
  TrendingUp,
  Eye
} from 'lucide-react';

interface ProviderAccount {
  id: string;
  businessName: string;
  email: string;
  stripeAccountId?: string;
  stripeAccountStatus?: string;
  stripeChargesEnabled?: boolean;
  stripePayoutsEnabled?: boolean;
  stripeDetailsSubmitted?: boolean;
  createdAt: any;
}

interface DashboardStats {
  totalProviders: number;
  connectedProviders: number;
  activeProviders: number;
  pendingProviders: number;
  totalRevenue: number;
  platformFees: number;
}

export default function StripeConnectDashboard() {
  const [providers, setProviders] = useState<ProviderAccount[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalProviders: 0,
    connectedProviders: 0,
    activeProviders: 0,
    pendingProviders: 0,
    totalRevenue: 0,
    platformFees: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedProvider, setSelectedProvider] = useState<ProviderAccount | null>(null);

  useEffect(() => {
    fetchProviders();
    fetchStats();
  }, []);

  const fetchProviders = async () => {
    try {
      const providersRef = collection(db, 'providers');
      const q = query(providersRef, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      const providersList: ProviderAccount[] = [];
      querySnapshot.forEach((doc) => {
        providersList.push({
          id: doc.id,
          ...doc.data()
        } as ProviderAccount);
      });

      setProviders(providersList);
    } catch (error) {
      console.error('Error fetching providers:', error);
    }
  };

  const fetchStats = async () => {
    try {
      // Fetch transactions for revenue calculation
      const transactionsRef = collection(db, 'transactions');
      const transactionsQuery = query(
        transactionsRef, 
        where('type', '==', 'payment'),
        where('status', '==', 'succeeded')
      );
      const transactionsSnapshot = await getDocs(transactionsQuery);

      let totalRevenue = 0;
      let platformFees = 0;

      transactionsSnapshot.forEach((doc) => {
        const data = doc.data();
        totalRevenue += (data.totalAmount || 0) / 100;
        platformFees += (data.platformFee || 0) / 100;
      });

      // Calculate provider stats
      const totalProviders = providers.length;
      const connectedProviders = providers.filter(p => p.stripeAccountId).length;
      const activeProviders = providers.filter(p => p.stripeAccountStatus === 'active').length;
      const pendingProviders = providers.filter(p => p.stripeAccountStatus === 'pending').length;

      setStats({
        totalProviders,
        connectedProviders,
        activeProviders,
        pendingProviders,
        totalRevenue,
        platformFees,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'restricted': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'restricted': return <AlertCircle className="w-4 h-4" />;
      default: return <CreditCard className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Providers</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProviders}</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Connected</p>
              <p className="text-2xl font-bold text-green-600">{stats.connectedProviders}</p>
            </div>
            <CreditCard className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue.toFixed(2)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Platform Fees</p>
              <p className="text-2xl font-bold text-green-600">${stats.platformFees.toFixed(2)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Providers Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Provider Accounts</h3>
          <p className="text-sm text-gray-600">Manage Stripe Connect accounts for all providers</p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Provider
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stripe Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Capabilities
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Account ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {providers.map((provider) => (
                <tr key={provider.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {provider.businessName}
                      </div>
                      <div className="text-sm text-gray-500">{provider.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {provider.stripeAccountStatus ? (
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(provider.stripeAccountStatus)}`}>
                        {getStatusIcon(provider.stripeAccountStatus)}
                        {provider.stripeAccountStatus}
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">Not connected</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex gap-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        provider.stripeChargesEnabled ? 'text-green-600 bg-green-50' : 'text-gray-600 bg-gray-50'
                      }`}>
                        {provider.stripeChargesEnabled ? 'Charges ✓' : 'Charges ✗'}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        provider.stripePayoutsEnabled ? 'text-green-600 bg-green-50' : 'text-gray-600 bg-gray-50'
                      }`}>
                        {provider.stripePayoutsEnabled ? 'Payouts ✓' : 'Payouts ✗'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-500 font-mono">
                      {provider.stripeAccountId ? 
                        `${provider.stripeAccountId.substring(0, 12)}...` : 
                        'None'
                      }
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => setSelectedProvider(provider)}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center gap-1"
                    >
                      <Eye className="w-4 h-4" />
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Provider Details Modal */}
      {selectedProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {selectedProvider.businessName} - Stripe Details
              </h3>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Business Name</label>
                  <p className="text-sm text-gray-900">{selectedProvider.businessName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <p className="text-sm text-gray-900">{selectedProvider.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Stripe Account ID</label>
                  <p className="text-sm text-gray-900 font-mono">
                    {selectedProvider.stripeAccountId || 'Not connected'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Account Status</label>
                  <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedProvider.stripeAccountStatus)}`}>
                    {getStatusIcon(selectedProvider.stripeAccountStatus)}
                    {selectedProvider.stripeAccountStatus || 'Not connected'}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex justify-end">
              <button
                onClick={() => setSelectedProvider(null)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
