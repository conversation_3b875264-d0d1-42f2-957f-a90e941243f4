'use client';

import { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, Users, Zap, CreditCard, Calendar, Target, Star } from 'lucide-react';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

interface RevenueMetrics {
  totalRevenue: number;
  monthlyRecurring: number;
  commissionRevenue: number;
  boostRevenue: number;
  tipCommissions: number;
  activeSubscriptions: number;
  totalBookings: number;
  averageBookingValue: number;
}

export default function RevenueDashboard() {
  const [metrics, setMetrics] = useState<RevenueMetrics>({
    totalRevenue: 0,
    monthlyRecurring: 0,
    commissionRevenue: 0,
    boostRevenue: 0,
    tipCommissions: 0,
    activeSubscriptions: 0,
    totalBookings: 0,
    averageBookingValue: 0,
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30'); // days

  useEffect(() => {
    loadRevenueData();
  }, [timeRange]);

  const loadRevenueData = async () => {
    try {
      setLoading(true);
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(timeRange));

      // Load transactions for the time period
      const transactionsQuery = query(
        collection(db, COLLECTIONS.TRANSACTIONS),
        where('createdAt', '>=', startDate.toISOString()),
        where('createdAt', '<=', endDate.toISOString()),
        where('status', '==', 'completed')
      );
      const transactionsSnapshot = await getDocs(transactionsQuery);
      const transactions = transactionsSnapshot.docs.map(doc => doc.data());

      // Calculate revenue metrics
      let totalRevenue = 0;
      let commissionRevenue = 0;
      let boostRevenue = 0;
      let tipCommissions = 0;
      let totalBookingValue = 0;
      let bookingCount = 0;

      transactions.forEach(transaction => {
        const platformFee = transaction.platformFee || 0;
        totalRevenue += platformFee;

        switch (transaction.type) {
          case 'booking_payment':
            commissionRevenue += platformFee;
            totalBookingValue += transaction.amount;
            bookingCount++;
            break;
          case 'boost_purchase':
            boostRevenue += transaction.amount; // Platform keeps 100% of boost fees
            break;
          case 'tip_payment':
            tipCommissions += platformFee;
            break;
          case 'cancellation_fee':
          case 'express_booking_fee':
            totalRevenue += transaction.amount; // Platform keeps 100% of fees
            break;
        }
      });

      // Load active subscriptions
      const subscriptionsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS),
        where('status', '==', 'active')
      );
      const subscriptionsSnapshot = await getDocs(subscriptionsQuery);
      const activeSubscriptions = subscriptionsSnapshot.docs.length;

      // Calculate monthly recurring revenue
      let monthlyRecurring = 0;
      subscriptionsSnapshot.docs.forEach(doc => {
        const subscription = doc.data();
        if (subscription.tier === 'pro') {
          monthlyRecurring += 9.99;
        } else if (subscription.tier === 'premium') {
          monthlyRecurring += 29.99;
        }
      });

      setMetrics({
        totalRevenue,
        monthlyRecurring,
        commissionRevenue,
        boostRevenue,
        tipCommissions,
        activeSubscriptions,
        totalBookings: bookingCount,
        averageBookingValue: bookingCount > 0 ? totalBookingValue / bookingCount : 0,
      });
    } catch (error) {
      console.error('Error loading revenue data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Revenue Dashboard</h2>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
          <option value="365">Last year</option>
        </select>
      </div>

      {/* Revenue Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Revenue */}
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Total Revenue</p>
              <p className="text-3xl font-bold">${metrics.totalRevenue.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6" />
            </div>
          </div>
        </div>

        {/* Monthly Recurring Revenue */}
        <div className="bg-gradient-to-r from-blue-500 to-cyan-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Monthly Recurring</p>
              <p className="text-3xl font-bold">${metrics.monthlyRecurring.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6" />
            </div>
          </div>
        </div>

        {/* Active Subscriptions */}
        <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Active Subscriptions</p>
              <p className="text-3xl font-bold">{metrics.activeSubscriptions}</p>
            </div>
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6" />
            </div>
          </div>
        </div>

        {/* Average Booking Value */}
        <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm">Avg Booking Value</p>
              <p className="text-3xl font-bold">${metrics.averageBookingValue.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Sources */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Sources</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-4 h-4 text-blue-600" />
                </div>
                <span className="font-medium text-gray-900">Booking Commissions</span>
              </div>
              <span className="font-semibold text-gray-900">${metrics.commissionRevenue.toFixed(2)}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-4 h-4 text-purple-600" />
                </div>
                <span className="font-medium text-gray-900">Boost Purchases</span>
              </div>
              <span className="font-semibold text-gray-900">${metrics.boostRevenue.toFixed(2)}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Star className="w-4 h-4 text-green-600" />
                </div>
                <span className="font-medium text-gray-900">Tip Commissions</span>
              </div>
              <span className="font-semibold text-gray-900">${metrics.tipCommissions.toFixed(2)}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Target className="w-4 h-4 text-yellow-600" />
                </div>
                <span className="font-medium text-gray-900">Subscriptions</span>
              </div>
              <span className="font-semibold text-gray-900">${metrics.monthlyRecurring.toFixed(2)}/mo</span>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Total Bookings</span>
              <span className="font-semibold text-gray-900">{metrics.totalBookings}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Commission Rate</span>
              <span className="font-semibold text-gray-900">10%</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Tip Commission Rate</span>
              <span className="font-semibold text-gray-900">5%</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Revenue per Booking</span>
              <span className="font-semibold text-gray-900">
                ${metrics.totalBookings > 0 ? (metrics.commissionRevenue / metrics.totalBookings).toFixed(2) : '0.00'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Subscription Conversion</span>
              <span className="font-semibold text-gray-900">
                {((metrics.activeSubscriptions / Math.max(metrics.totalBookings, 1)) * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Projections */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-6 border border-indigo-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Projections</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <p className="text-2xl font-bold text-indigo-600">
              ${(metrics.monthlyRecurring).toFixed(2)}
            </p>
            <p className="text-sm text-gray-600">Recurring Revenue</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              ${((metrics.commissionRevenue / parseInt(timeRange)) * 30).toFixed(2)}
            </p>
            <p className="text-sm text-gray-600">Projected Commissions</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-pink-600">
              ${(metrics.monthlyRecurring + ((metrics.commissionRevenue / parseInt(timeRange)) * 30)).toFixed(2)}
            </p>
            <p className="text-sm text-gray-600">Total Projected</p>
          </div>
        </div>
      </div>
    </div>
  );
}
