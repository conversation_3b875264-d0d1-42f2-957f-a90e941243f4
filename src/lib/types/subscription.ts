import { Timestamp } from 'firebase/firestore';

export type MembershipTier = 'free' | 'pro' | 'premium';

export interface ProviderSubscription {
  id: string;
  providerId: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'incomplete' | 'incomplete_expired' | 'trialing' | 'all' | 'ended';
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  planId: string;
  productId: string;
  subscriptionId: string;
  customerId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
