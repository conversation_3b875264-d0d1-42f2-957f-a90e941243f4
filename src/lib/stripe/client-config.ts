// Client-side Stripe configuration (no server-side Stripe instance)
// This file can be safely imported in client components

export interface SubscriptionTier {
  id: string;
  name: string;
  price: number; // in dollars
  stripePriceId: string;
  features: {
    bookingLimit: number | null; // null = unlimited
    analytics: 'basic' | 'advanced';
    calendarSync: boolean;
    prioritySearch: boolean;
    featuredListing: boolean;
    smsAlerts: boolean;
    customUrl: boolean;
    weeklyBoosts: number;
  };
}

export const SUBSCRIPTION_TIERS: Record<string, SubscriptionTier> = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    stripePriceId: '', // No Stripe price for free tier
    features: {
      bookingLimit: 5,
      analytics: 'basic',
      calendarSync: false,
      prioritySearch: false,
      featuredListing: false,
      smsAlerts: false,
      customUrl: false,
      weeklyBoosts: 0,
    },
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    price: 9.99,
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || '',
    features: {
      bookingLimit: null, // unlimited
      analytics: 'advanced',
      calendarSync: true,
      prioritySearch: true,
      featuredListing: false,
      smsAlerts: false,
      customUrl: false,
      weeklyBoosts: 0,
    },
  },
  premium: {
    id: 'premium',
    name: 'Premium',
    price: 29.99,
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID || '',
    features: {
      bookingLimit: null, // unlimited
      analytics: 'advanced',
      calendarSync: true,
      prioritySearch: true,
      featuredListing: true,
      smsAlerts: true,
      customUrl: true,
      weeklyBoosts: 1,
    },
  },
};

export interface BoostType {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number; // in hours
  stripePriceId: string;
  features: string[];
}

export const BOOST_TYPES: Record<string, BoostType> = {
  topInSearch: {
    id: 'topInSearch',
    name: 'Top in Search',
    description: 'Appear at the top of search results for 7 days',
    price: 4.99,
    duration: 168, // 7 days in hours
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_TOP_SEARCH_PRICE_ID || '',
    features: [
      'Top position in search results',
      'Priority placement for 7 days',
      'Increased visibility to pet owners',
      'Analytics tracking included'
    ],
  },
  featuredToday: {
    id: 'featuredToday',
    name: 'Featured Today',
    description: 'Featured placement on homepage for 24 hours',
    price: 1.99,
    duration: 24, // 24 hours
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_FEATURED_TODAY_PRICE_ID || '',
    features: [
      'Homepage featured section',
      'Premium badge display',
      '24-hour featured placement',
      'Enhanced profile visibility'
    ],
  },
};

// Commission rates and fees (client-safe constants)
export const COMMISSION_RATES = {
  PLATFORM_COMMISSION_RATE: 0.10, // 10%
  TIP_COMMISSION_RATE: 0.05, // 5%
  EXPRESS_BOOKING_FEE: 5.00, // $5 for bookings <3 hours
  CANCELLATION_FEE_RATE: 0.10, // 10% for cancellations <12 hours
} as const;

// Stripe configuration constants (client-safe)
export const STRIPE_CONFIG = {
  // Platform fee percentage (10% as mentioned in requirements)
  PLATFORM_FEE_PERCENTAGE: 0.10,
  
  // Minimum amounts (in cents)
  MIN_PAYMENT_AMOUNT: 100, // $1.00
  MIN_WALLET_TOPUP: 500,   // $5.00
  
  // Maximum amounts (in cents)
  MAX_PAYMENT_AMOUNT: 100000, // $1,000.00
  MAX_WALLET_TOPUP: 50000,     // $500.00
  
  // Currency
  DEFAULT_CURRENCY: 'usd',
  
  // Connect account settings
  CONNECT: {
    REFRESH_URL: `${process.env.NEXT_PUBLIC_APP_URL}/provider/onboarding/refresh`,
    RETURN_URL: `${process.env.NEXT_PUBLIC_APP_URL}/provider/onboarding/complete`,
    DASHBOARD_URL: `${process.env.NEXT_PUBLIC_APP_URL}/provider/dashboard`,
  },
} as const;

// Helper function to calculate platform fee
export const calculatePlatformFee = (amount: number): number => {
  return Math.round(amount * STRIPE_CONFIG.PLATFORM_FEE_PERCENTAGE);
};

// Helper function to format amount for Stripe (convert dollars to cents)
export const formatAmountForStripe = (amount: number): number => {
  return Math.round(amount * 100);
};

// Helper function to format amount for display (convert cents to dollars)
export const formatAmountForDisplay = (amount: number): number => {
  return amount / 100;
};

// Transaction types for our system
export const TRANSACTION_TYPES = {
  PAYMENT: 'payment',
  REFUND: 'refund',
  WALLET_TOPUP: 'wallet_topup',
  WALLET_PAYMENT: 'wallet_payment',
  PAYOUT: 'payout',
  PLATFORM_FEE: 'platform_fee',
  BOOKING_PAYMENT: 'booking_payment',
  TIP_PAYMENT: 'tip_payment',
  BOOST_PURCHASE: 'boost_purchase',
  CANCELLATION_FEE: 'cancellation_fee',
  EXPRESS_BOOKING_FEE: 'express_booking_fee',
} as const;

// Transaction statuses
export const TRANSACTION_STATUSES = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELED: 'canceled',
  REFUNDED: 'refunded',
} as const;

export type TransactionType = typeof TRANSACTION_TYPES[keyof typeof TRANSACTION_TYPES];
export type TransactionStatus = typeof TRANSACTION_STATUSES[keyof typeof TRANSACTION_STATUSES];

// Provider subscription interface
export interface ProviderSubscription {
  id: string;
  providerId: string;
  tier: 'free' | 'pro' | 'premium';
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  bookingsThisMonth: number;
  createdAt: string;
  updatedAt: string;
}

// Provider boost interface
export interface ProviderBoost {
  id: string;
  providerId: string;
  boostType: string;
  status: 'active' | 'expired' | 'pending';
  startDate: string;
  endDate: string;
  price: number;
  stripePaymentIntentId?: string;
  impressions: number;
  clicks: number;
  createdAt: string;
  updatedAt: string;
}
