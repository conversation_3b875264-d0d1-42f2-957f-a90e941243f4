import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  updateDoc,
  doc,
  getDoc,
  setDoc
} from 'firebase/firestore';
import { db } from './firebase/config';
import { COLLECTIONS } from './database';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'pet_owner' | 'provider' | 'admin';
  avatar?: string;
  phone?: string;
  location?: string;
  verified: boolean;
  joinedDate: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  return await bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined');
  }
  
  return jwt.sign(payload, secret, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  });
}

// Generate refresh token
export function generateRefreshToken(userId: string): string {
  const secret = process.env.REFRESH_TOKEN_SECRET;
  if (!secret) {
    throw new Error('REFRESH_TOKEN_SECRET is not defined');
  }
  
  return jwt.sign({ userId }, secret, {
    expiresIn: '30d',
  });
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined');
  }
  
  try {
    return jwt.verify(token, secret) as JWTPayload;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}

// Verify refresh token
export function verifyRefreshToken(token: string): { userId: string } {
  const secret = process.env.REFRESH_TOKEN_SECRET;
  if (!secret) {
    throw new Error('REFRESH_TOKEN_SECRET is not defined');
  }
  
  try {
    return jwt.verify(token, secret) as { userId: string };
  } catch (error) {
    throw new Error('Invalid or expired refresh token');
  }
}

// Create user account
export async function createUser(userData: {
  email: string;
  password: string;
  name: string;
  role?: 'pet_owner' | 'provider' | 'admin';
  phone?: string;
  location?: string;
}): Promise<AuthUser> {
  const { email, password, name, role = 'pet_owner', phone, location } = userData;

  // Check if user already exists
  const usersRef = collection(db, COLLECTIONS.USERS);
  const existingUserQuery = query(usersRef, where('email', '==', email.toLowerCase()));
  const existingUserSnapshot = await getDocs(existingUserQuery);

  if (!existingUserSnapshot.empty) {
    throw new Error('User with this email already exists');
  }

  // Hash password
  const passwordHash = await hashPassword(password);

  // Generate email verification token
  const emailVerificationToken = uuidv4();
  const userId = uuidv4();

  // Create user document
  const userDoc = {
    id: userId,
    email: email.toLowerCase(),
    passwordHash,
    name,
    role,
    phone: phone || '',
    location: location || '',
    emailVerificationToken,
    verified: false,
    failedLoginAttempts: 0,
    lockedUntil: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Save to Firestore
  await setDoc(doc(db, COLLECTIONS.USERS, userId), userDoc);

  return {
    id: userId,
    email: userDoc.email,
    name: userDoc.name,
    role: userDoc.role,
    phone: userDoc.phone,
    location: userDoc.location,
    verified: userDoc.verified,
    joinedDate: userDoc.createdAt,
  };
}

// Authenticate user
export async function authenticateUser(email: string, password: string): Promise<{
  user: AuthUser;
  token: string;
  refreshToken: string;
}> {
  // Get user by email
  const usersRef = collection(db, COLLECTIONS.USERS);
  const userQuery = query(usersRef, where('email', '==', email.toLowerCase()));
  const userSnapshot = await getDocs(userQuery);

  if (userSnapshot.empty) {
    throw new Error('Invalid email or password');
  }

  const userDoc = userSnapshot.docs[0];
  const user = userDoc.data();

  // Check if account is locked
  if (user.lockedUntil && new Date() < new Date(user.lockedUntil)) {
    throw new Error('Account is temporarily locked due to too many failed login attempts');
  }

  // Verify password
  const isValidPassword = await verifyPassword(password, user.passwordHash);

  if (!isValidPassword) {
    // Increment failed login attempts
    const failedAttempts = (user.failedLoginAttempts || 0) + 1;
    const lockUntil = failedAttempts >= 5 ? new Date(Date.now() + 15 * 60 * 1000).toISOString() : null;

    await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {
      failedLoginAttempts: failedAttempts,
      lockedUntil: lockUntil,
      updatedAt: new Date().toISOString()
    });

    throw new Error('Invalid email or password');
  }

  // Reset failed login attempts and update last login
  await updateDoc(doc(db, COLLECTIONS.USERS, user.id), {
    failedLoginAttempts: 0,
    lockedUntil: null,
    lastLoginAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  // Generate tokens
  const token = generateToken({
    userId: user.id,
    email: user.email,
    role: user.role,
  });

  const refreshToken = generateRefreshToken(user.id);

  const authUser: AuthUser = {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    avatar: user.avatar,
    phone: user.phone,
    location: user.location,
    verified: user.verified,
    joinedDate: user.createdAt,
  };

  return {
    user: authUser,
    token,
    refreshToken,
  };
}

// Get user by ID
export async function getUserById(userId: string): Promise<AuthUser | null> {
  try {
    const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, userId));

    if (!userDoc.exists()) {
      return null;
    }

    const user = userDoc.data();

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      avatar: user.avatar,
      phone: user.phone,
      location: user.location,
      verified: user.verified,
      joinedDate: user.createdAt,
    };
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

// Generate password reset token
export async function generatePasswordResetToken(email: string): Promise<string> {
  const token = uuidv4();
  const expires = new Date(Date.now() + 60 * 60 * 1000).toISOString(); // 1 hour

  // Find user by email
  const usersRef = collection(db, COLLECTIONS.USERS);
  const userQuery = query(usersRef, where('email', '==', email.toLowerCase()));
  const userSnapshot = await getDocs(userQuery);

  if (userSnapshot.empty) {
    throw new Error('User not found');
  }

  const userDoc = userSnapshot.docs[0];
  await updateDoc(userDoc.ref, {
    passwordResetToken: token,
    passwordResetExpires: expires,
    updatedAt: new Date().toISOString()
  });

  return token;
}

// Reset password
export async function resetPassword(token: string, newPassword: string): Promise<boolean> {
  // Find user by reset token
  const usersRef = collection(db, COLLECTIONS.USERS);
  const userQuery = query(usersRef, where('passwordResetToken', '==', token));
  const userSnapshot = await getDocs(userQuery);

  if (userSnapshot.empty) {
    throw new Error('Invalid or expired reset token');
  }

  const userDoc = userSnapshot.docs[0];
  const user = userDoc.data();

  // Check if token is expired
  if (!user.passwordResetExpires || new Date() > new Date(user.passwordResetExpires)) {
    throw new Error('Invalid or expired reset token');
  }

  const passwordHash = await hashPassword(newPassword);

  await updateDoc(userDoc.ref, {
    passwordHash,
    passwordResetToken: null,
    passwordResetExpires: null,
    updatedAt: new Date().toISOString()
  });

  return true;
}
