/**
 * Integration examples for adding notifications to existing services
 * Copy these patterns to integrate notifications throughout your app
 */

import {
  notifyBookingRequest,
  notifyBookingConfirmed,
  notifyBookingCancelled,
  notifyBookingCompleted,
  notifyPostLiked,
  notifyPostCommented,
  notifyNewFollower,
  notifyNewReview,
  notifyPaymentReceived,
  notifyAccountVerified,
  notifyEmergencyAlert
} from '@/lib/notifications/notification-helpers';

// ==========================================
// BOOKING SERVICE INTEGRATION EXAMPLES
// ==========================================

/**
 * Example: Add to your booking creation function
 */
export const createBookingWithNotification = async (bookingData: any) => {
  // Your existing booking creation logic here...
  // const booking = await createBooking(bookingData);
  
  // Add notification
  await notifyBookingRequest(
    bookingData.providerId,
    bookingData.petOwnerId,
    bookingData.petOwnerName,
    bookingData.serviceType,
    bookingData.bookingId,
    new Date(bookingData.bookingDate),
    bookingData.amount
  );
  
  console.log('✅ Booking created with notification sent!');
};

/**
 * Example: Add to your booking confirmation function
 */
export const confirmBookingWithNotification = async (bookingId: string, providerId: string, providerName: string) => {
  // Your existing booking confirmation logic here...
  // const booking = await confirmBooking(bookingId);
  
  // Add notification
  await notifyBookingConfirmed(
    'petOwnerId', // Get from booking data
    providerId,
    providerName,
    'Pet Sitting', // Get from booking data
    bookingId,
    new Date() // Get actual booking date
  );
  
  console.log('✅ Booking confirmed with notification sent!');
};

// ==========================================
// SOCIAL FEATURES INTEGRATION EXAMPLES
// ==========================================

/**
 * Example: Add to your post like function
 */
export const likePostWithNotification = async (postId: string, likerId: string, likerName: string, postAuthorId: string) => {
  // Your existing like logic here...
  // await likePost(postId, likerId);
  
  // Don't notify if user likes their own post
  if (likerId !== postAuthorId) {
    await notifyPostLiked(
      postAuthorId,
      likerId,
      likerName,
      postId,
      'Check out this amazing pet photo!', // Get actual post content
      '/avatar.jpg' // Get actual liker avatar
    );
  }
  
  console.log('✅ Post liked with notification sent!');
};

/**
 * Example: Add to your comment creation function
 */
export const addCommentWithNotification = async (postId: string, commentData: any) => {
  // Your existing comment creation logic here...
  // const comment = await createComment(postId, commentData);
  
  // Add notification
  await notifyPostCommented(
    commentData.postAuthorId,
    commentData.commenterId,
    commentData.commenterName,
    postId,
    'commentId', // Get actual comment ID
    commentData.commentText,
    'Original post content...', // Get actual post content
    commentData.commenterAvatar
  );
  
  console.log('✅ Comment added with notification sent!');
};

/**
 * Example: Add to your follow function
 */
export const followUserWithNotification = async (userId: string, followerId: string, followerName: string) => {
  // Your existing follow logic here...
  // await followUser(userId, followerId);
  
  // Add notification
  await notifyNewFollower(
    userId,
    followerId,
    followerName,
    '/follower-avatar.jpg' // Get actual follower avatar
  );
  
  console.log('✅ User followed with notification sent!');
};

// ==========================================
// REVIEW SYSTEM INTEGRATION EXAMPLES
// ==========================================

/**
 * Example: Add to your review creation function
 */
export const createReviewWithNotification = async (reviewData: any) => {
  // Your existing review creation logic here...
  // const review = await createReview(reviewData);
  
  // Add notification
  await notifyNewReview(
    reviewData.providerId,
    reviewData.reviewerId,
    reviewData.reviewerName,
    'reviewId', // Get actual review ID
    reviewData.rating,
    reviewData.reviewText,
    reviewData.serviceType,
    reviewData.reviewerAvatar
  );
  
  console.log('✅ Review created with notification sent!');
};

// ==========================================
// PAYMENT SYSTEM INTEGRATION EXAMPLES
// ==========================================

/**
 * Example: Add to your payment processing function
 */
export const processPaymentWithNotification = async (paymentData: any) => {
  // Your existing payment processing logic here...
  // const payment = await processPayment(paymentData);
  
  // Add notification
  await notifyPaymentReceived(
    paymentData.providerId,
    paymentData.amount,
    paymentData.payerName,
    paymentData.serviceType,
    'transactionId' // Get actual transaction ID
  );
  
  console.log('✅ Payment processed with notification sent!');
};

// ==========================================
// SYSTEM EVENTS INTEGRATION EXAMPLES
// ==========================================

/**
 * Example: Add to your account verification function
 */
export const verifyAccountWithNotification = async (userId: string) => {
  // Your existing verification logic here...
  // await verifyAccount(userId);
  
  // Add notification
  await notifyAccountVerified(userId);
  
  console.log('✅ Account verified with notification sent!');
};

/**
 * Example: Add to your emergency alert system
 */
export const sendEmergencyAlertWithNotification = async (userId: string, alertData: any) => {
  // Your existing emergency alert logic here...
  // await sendEmergencyAlert(alertData);
  
  // Add notification
  await notifyEmergencyAlert(
    userId,
    alertData.title,
    alertData.message,
    alertData.actionUrl
  );
  
  console.log('✅ Emergency alert sent with notification!');
};

// ==========================================
// BATCH NOTIFICATION EXAMPLES
// ==========================================

/**
 * Example: Send notifications to multiple users
 */
export const notifyMultipleUsers = async (userIds: string[], notificationData: any) => {
  const promises = userIds.map(userId => 
    notifyEmergencyAlert(
      userId,
      notificationData.title,
      notificationData.message,
      notificationData.actionUrl
    )
  );
  
  await Promise.all(promises);
  console.log(`✅ Notifications sent to ${userIds.length} users!`);
};

// ==========================================
// HOW TO USE THESE INTEGRATIONS
// ==========================================

/*

1. REPLACE YOUR EXISTING FUNCTIONS:

   // OLD CODE:
   const createBooking = async (data) => {
     // booking logic
   };

   // NEW CODE:
   const createBooking = async (data) => {
     // booking logic
     
     // ADD THIS LINE:
     await notifyBookingRequest(data.providerId, data.petOwnerId, ...);
   };

2. IMPORT THE HELPERS:

   import { notifyBookingRequest, notifyPostLiked } from '@/lib/notifications/notification-helpers';

3. CALL THEM AFTER YOUR EXISTING LOGIC:

   // Your existing code
   await likePost(postId, userId);
   
   // Add notification
   await notifyPostLiked(postAuthorId, userId, userName, postId, postContent);

4. NOTIFICATIONS WILL AUTOMATICALLY APPEAR IN THE HEADER BELL!

*/
