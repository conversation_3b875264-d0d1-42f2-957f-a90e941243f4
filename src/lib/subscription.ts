import { db } from './firebase/config';
import { doc, getDoc, updateDoc, serverTimestamp, DocumentData } from 'firebase/firestore';
import { ProviderSubscription } from './types';

export const createSubscriptionCheckout = async (
  providerId: string,
  tier: 'pro' | 'premium',
  successUrl: string,
  cancelUrl: string
) => {
  try {
    const response = await fetch('/api/subscriptions/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        providerId,
        tier,
        successUrl,
        cancelUrl,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create checkout session');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating subscription checkout:', error);
    throw error;
  }
};

export const getSubscription = async (providerId: string): Promise<ProviderSubscription | null> => {
  try {
    const docRef = doc(db, 'subscriptions', providerId);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) {
      return null;
    }
    
    return { id: docSnap.id, ...docSnap.data() } as ProviderSubscription;
  } catch (error) {
    console.error('Error getting subscription:', error);
    throw error;
  }
};

export const cancelSubscription = async (providerId: string, atPeriodEnd = true) => {
  try {
    const response = await fetch('/api/subscriptions/cancel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        providerId,
        atPeriodEnd,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel subscription');
    }

    return await response.json();
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw error;
  }
};

export const updateSubscription = async (providerId: string, updates: any) => {
  try {
    const docRef = doc(db, 'subscriptions', providerId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    throw error;
  }
};
