import { doc, updateDoc, addDoc, collection, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';
import { COMMISSION_RATES } from '@/lib/stripe/client-config';

export interface BookingPricing {
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
  }>;
  tip?: number;
  expressBookingFee?: number;
  cancellationFee?: number;
  subtotal: number;
  platformFee: number;
  total: number;
  providerEarnings: number;
}

export interface CommissionBreakdown {
  serviceAmount: number;
  addOnAmount: number;
  tipAmount: number;
  expressBookingFee: number;
  cancellationFee: number;
  subtotal: number;
  platformCommission: number; // 10% of service + add-ons
  tipCommission: number; // 5% of tips
  totalPlatformFee: number;
  providerEarnings: number;
  total: number;
}

export class CommissionService {
  // Platform commission rates (from client config)
  static readonly PLATFORM_COMMISSION_RATE = COMMISSION_RATES.PLATFORM_COMMISSION_RATE;
  static readonly TIP_COMMISSION_RATE = COMMISSION_RATES.TIP_COMMISSION_RATE;
  static readonly EXPRESS_BOOKING_FEE = COMMISSION_RATES.EXPRESS_BOOKING_FEE;
  static readonly CANCELLATION_FEE_RATE = COMMISSION_RATES.CANCELLATION_FEE_RATE;

  /**
   * Calculate comprehensive booking pricing with all fees
   */
  static calculateBookingPricing(
    servicePrice: number,
    addOns: Array<{ id: string; name: string; price: number }> = [],
    tip: number = 0,
    isExpressBooking: boolean = false,
    cancellationFee: number = 0
  ): CommissionBreakdown {
    // Calculate base amounts
    const serviceAmount = servicePrice;
    const addOnAmount = addOns.reduce((sum, addOn) => sum + addOn.price, 0);
    const tipAmount = tip;
    const expressBookingFee = isExpressBooking ? this.EXPRESS_BOOKING_FEE : 0;

    // Calculate subtotal (service + add-ons + express fee + cancellation fee)
    const subtotal = serviceAmount + addOnAmount + expressBookingFee + cancellationFee;

    // Calculate platform commissions
    const platformCommission = (serviceAmount + addOnAmount) * this.PLATFORM_COMMISSION_RATE;
    const tipCommission = tipAmount * this.TIP_COMMISSION_RATE;
    const totalPlatformFee = platformCommission + tipCommission;

    // Calculate provider earnings
    const providerEarnings = subtotal + tipAmount - totalPlatformFee;

    // Calculate total customer pays
    const total = subtotal + tipAmount;

    return {
      serviceAmount,
      addOnAmount,
      tipAmount,
      expressBookingFee,
      cancellationFee,
      subtotal,
      platformCommission,
      tipCommission,
      totalPlatformFee,
      providerEarnings,
      total,
    };
  }

  /**
   * Calculate cancellation fee based on timing
   */
  static calculateCancellationFee(
    bookingAmount: number,
    hoursUntilBooking: number
  ): number {
    if (hoursUntilBooking < 12) {
      return bookingAmount * this.CANCELLATION_FEE_RATE;
    }
    return 0;
  }

  /**
   * Check if booking qualifies for express fee
   */
  static isExpressBooking(hoursUntilBooking: number): boolean {
    return hoursUntilBooking < 3;
  }

  // Note: Payment intent creation moved to server-side API endpoints
  // This service now only handles calculations for client-side use

  // Note: Tip and cancellation fee processing moved to server-side API endpoints

  /**
   * Get pricing breakdown for display
   */
  static getPricingBreakdown(
    servicePrice: number,
    addOns: Array<{ id: string; name: string; price: number }> = [],
    tip: number = 0,
    isExpressBooking: boolean = false,
    cancellationFee: number = 0
  ): {
    items: Array<{ label: string; amount: number; type: 'service' | 'addon' | 'fee' | 'tip' }>;
    subtotal: number;
    platformFee: number;
    total: number;
  } {
    const pricing = this.calculateBookingPricing(
      servicePrice,
      addOns,
      tip,
      isExpressBooking,
      cancellationFee
    );

    const items = [
      { label: 'Service', amount: pricing.serviceAmount, type: 'service' as const },
      ...addOns.map(addOn => ({
        label: addOn.name,
        amount: addOn.price,
        type: 'addon' as const,
      })),
    ];

    if (pricing.expressBookingFee > 0) {
      items.push({
        label: 'Express Booking Fee',
        amount: pricing.expressBookingFee,
        type: 'fee' as const,
      });
    }

    if (pricing.cancellationFee > 0) {
      items.push({
        label: 'Cancellation Fee',
        amount: pricing.cancellationFee,
        type: 'fee' as const,
      });
    }

    if (pricing.tipAmount > 0) {
      items.push({
        label: 'Tip',
        amount: pricing.tipAmount,
        type: 'tip' as const,
      });
    }

    return {
      items,
      subtotal: pricing.subtotal,
      platformFee: pricing.totalPlatformFee,
      total: pricing.total,
    };
  }
}
