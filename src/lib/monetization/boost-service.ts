import { doc, updateDoc, addDoc, collection, getDoc, setDoc, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';
import { BOOST_TYPES } from '@/lib/stripe/client-config';

// Re-export types from client config
export type { BoostType, ProviderBoost } from '@/lib/stripe/client-config';
export { BOOST_TYPES } from '@/lib/stripe/client-config';

export class BoostService {
  /**
   * Create boost purchase payment intent (client-side API call)
   */
  static async createBoostPaymentIntent(
    providerId: string,
    boostType: string,
    customerId: string
  ) {
    try {
      const response = await fetch('/api/boosts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boostType,
          customerId,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Error creating boost payment intent:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Activate boost after successful payment
   */
  static async activateBoost(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      await updateDoc(boostRef, {
        status: 'active',
        updatedAt: new Date().toISOString(),
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error activating boost:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get active boosts for a provider
   */
  static async getActiveBoosts(providerId: string): Promise<ProviderBoost[]> {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('providerId', '==', providerId),
        where('status', '==', 'active')
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const boosts = boostsSnapshot.docs.map(doc => doc.data() as ProviderBoost);
      
      // Check if any boosts have expired
      const now = new Date();
      for (const boost of boosts) {
        if (new Date(boost.endDate) < now) {
          await this.expireBoost(boost.id);
        }
      }

      // Return only still-active boosts
      return boosts.filter(boost => new Date(boost.endDate) >= now);
    } catch (error) {
      console.error('Error getting active boosts:', error);
      return [];
    }
  }

  /**
   * Expire a boost
   */
  static async expireBoost(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      await updateDoc(boostRef, {
        status: 'expired',
        updatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error expiring boost:', error);
    }
  }

  /**
   * Track boost impression
   */
  static async trackImpression(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      const boostDoc = await getDoc(boostRef);
      
      if (boostDoc.exists()) {
        const boost = boostDoc.data() as ProviderBoost;
        await updateDoc(boostRef, {
          impressions: boost.impressions + 1,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error tracking impression:', error);
    }
  }

  /**
   * Track boost click
   */
  static async trackClick(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      const boostDoc = await getDoc(boostRef);
      
      if (boostDoc.exists()) {
        const boost = boostDoc.data() as ProviderBoost;
        await updateDoc(boostRef, {
          clicks: boost.clicks + 1,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  }

  /**
   * Get boosted providers for search results
   */
  static async getBoostedProvidersForSearch(): Promise<string[]> {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('boostType', '==', 'topInSearch'),
        where('status', '==', 'active')
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const boostedProviders: string[] = [];
      const now = new Date();

      for (const doc of boostsSnapshot.docs) {
        const boost = doc.data() as ProviderBoost;
        if (new Date(boost.endDate) >= now) {
          boostedProviders.push(boost.providerId);
          // Track impression
          await this.trackImpression(boost.id);
        } else {
          // Expire the boost
          await this.expireBoost(boost.id);
        }
      }

      return boostedProviders;
    } catch (error) {
      console.error('Error getting boosted providers for search:', error);
      return [];
    }
  }

  /**
   * Get featured providers for homepage
   */
  static async getFeaturedProvidersForHomepage(): Promise<string[]> {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('boostType', '==', 'featuredToday'),
        where('status', '==', 'active')
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const featuredProviders: string[] = [];
      const now = new Date();

      for (const doc of boostsSnapshot.docs) {
        const boost = doc.data() as ProviderBoost;
        if (new Date(boost.endDate) >= now) {
          featuredProviders.push(boost.providerId);
          // Track impression
          await this.trackImpression(boost.id);
        } else {
          // Expire the boost
          await this.expireBoost(boost.id);
        }
      }

      return featuredProviders;
    } catch (error) {
      console.error('Error getting featured providers for homepage:', error);
      return [];
    }
  }

  /**
   * Get boost analytics for provider
   */
  static async getBoostAnalytics(providerId: string) {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('providerId', '==', providerId)
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const boosts = boostsSnapshot.docs.map(doc => doc.data() as ProviderBoost);
      
      const analytics = {
        totalSpent: boosts.reduce((sum, boost) => sum + boost.price, 0),
        totalImpressions: boosts.reduce((sum, boost) => sum + boost.impressions, 0),
        totalClicks: boosts.reduce((sum, boost) => sum + boost.clicks, 0),
        activeBoosts: boosts.filter(boost => boost.status === 'active').length,
        boostHistory: boosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()),
      };

      return { success: true, analytics };
    } catch (error: any) {
      console.error('Error getting boost analytics:', error);
      return { success: false, error: error.message };
    }
  }
}
