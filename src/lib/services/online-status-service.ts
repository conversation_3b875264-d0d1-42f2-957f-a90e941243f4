import { 
  collection, 
  doc, 
  setDoc, 
  deleteDoc, 
  onSnapshot, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export interface OnlineUser {
  id: string;
  name: string;
  avatar?: string;
  role: 'pet_owner' | 'provider' | 'admin';
  lastSeen: Timestamp;
  isOnline: boolean;
  city?: string;
  verified?: boolean;
}

class OnlineStatusService {
  private unsubscribeCallbacks: Map<string, () => void> = new Map();

  /**
   * Set user as online
   */
  async setUserOnline(userId: string, userData: Partial<OnlineUser>): Promise<void> {
    try {
      const userRef = doc(db, 'onlineUsers', userId);
      await setDoc(userRef, {
        ...userData,
        id: userId,
        lastSeen: serverTimestamp(),
        isOnline: true,
        updatedAt: serverTimestamp()
      }, { merge: true });

      console.log('✅ User set as online:', userId);
    } catch (error) {
      console.error('❌ Error setting user online:', error);
      throw error;
    }
  }

  /**
   * Set user as offline
   */
  async setUserOffline(userId: string): Promise<void> {
    try {
      const userRef = doc(db, 'onlineUsers', userId);
      await setDoc(userRef, {
        isOnline: false,
        lastSeen: serverTimestamp(),
        updatedAt: serverTimestamp()
      }, { merge: true });

      console.log('✅ User set as offline:', userId);
    } catch (error) {
      console.error('❌ Error setting user offline:', error);
      throw error;
    }
  }

  /**
   * Remove user from online status (on logout)
   */
  async removeUserFromOnline(userId: string): Promise<void> {
    try {
      const userRef = doc(db, 'onlineUsers', userId);
      await deleteDoc(userRef);

      console.log('✅ User removed from online status:', userId);
    } catch (error) {
      console.error('❌ Error removing user from online:', error);
      throw error;
    }
  }

  /**
   * Get all online users
   */
  getOnlineUsers(callback: (users: OnlineUser[]) => void): () => void {
    try {
      const q = query(
        collection(db, 'onlineUsers'),
        where('isOnline', '==', true),
        orderBy('lastSeen', 'desc'),
        limit(50)
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const onlineUsers: OnlineUser[] = [];
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          onlineUsers.push({
            id: doc.id,
            name: data.name || 'Unknown User',
            avatar: data.avatar,
            role: data.role || 'pet_owner',
            lastSeen: data.lastSeen,
            isOnline: data.isOnline,
            city: data.city,
            verified: data.verified
          });
        });

        console.log('📱 Online users updated:', onlineUsers.length);
        callback(onlineUsers);
      }, (error) => {
        console.error('❌ Error listening to online users:', error);
        callback([]);
      });

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error getting online users:', error);
      callback([]);
      return () => {};
    }
  }

  /**
   * Check if user is online
   */
  getUserOnlineStatus(userId: string, callback: (isOnline: boolean) => void): () => void {
    try {
      const userRef = doc(db, 'onlineUsers', userId);
      
      const unsubscribe = onSnapshot(userRef, (doc) => {
        if (doc.exists()) {
          const data = doc.data();
          callback(data.isOnline || false);
        } else {
          callback(false);
        }
      }, (error) => {
        console.error('❌ Error checking user online status:', error);
        callback(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error('❌ Error getting user online status:', error);
      callback(false);
      return () => {};
    }
  }

  /**
   * Update user's last seen timestamp (heartbeat)
   */
  async updateHeartbeat(userId: string): Promise<void> {
    try {
      const userRef = doc(db, 'onlineUsers', userId);
      await setDoc(userRef, {
        lastSeen: serverTimestamp(),
        updatedAt: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.error('❌ Error updating heartbeat:', error);
    }
  }

  /**
   * Start heartbeat for user (call every 30 seconds)
   */
  startHeartbeat(userId: string): () => void {
    const interval = setInterval(() => {
      this.updateHeartbeat(userId);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup(): void {
    this.unsubscribeCallbacks.forEach((unsubscribe) => {
      unsubscribe();
    });
    this.unsubscribeCallbacks.clear();
  }
}

export const onlineStatusService = new OnlineStatusService();
