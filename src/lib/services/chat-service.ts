import { db, storage } from '@/lib/firebase';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  getDocs,
  getDoc,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { Chat, Message, ChatUser, CreateChatRequest, SendMessageRequest, TypingIndicator } from '@/types/chat';
import { contentModerationService } from './content-moderation';
import { notificationService } from './notification-service';
import { notifyNewMessage } from '@/lib/notifications/notification-helpers';

class ChatService {
  private readonly COLLECTIONS = {
    CHATS: 'chats',
    MESSAGES: 'messages',
    USERS: 'users',
    TYPING: 'typing_indicators',
    NOTIFICATIONS: 'chat_notifications'
  };

  /**
   * Create a new chat or get existing one
   */
  async createOrGetChat(request: CreateChatRequest): Promise<string> {
    const { participantIds, isGroupChat = false, chatName, initialMessage } = request;

    // For one-on-one chats, check if chat already exists
    if (!isGroupChat && participantIds.length === 2) {
      const existingChat = await this.findExistingChat(participantIds);
      if (existingChat) {
        return existingChat.id;
      }
    }

    // Get participant details
    const participantDetails = await this.getUserDetails(participantIds);

    // Check if this is a support chat
    const isSupportChat = participantDetails.some(user =>
      user.userType === 'support' || user.userType === 'admin'
    );

    // Create new chat
    const chatData: Omit<Chat, 'id'> = {
      participantIds: participantIds,
      participantDetails,
      lastMessage: initialMessage || '',
      lastMessageSender: '',
      lastMessageTime: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isSupportChat,
      isGroupChat,
      unreadCount: participantIds.reduce((acc, uid) => ({ ...acc, [uid]: 0 }), {}),
      typingUsers: []
    };

    // Only add chatName if it's a group chat and has a name
    if (isGroupChat && chatName) {
      (chatData as any).chatName = chatName;
    }

    const docRef = await addDoc(collection(db, this.COLLECTIONS.CHATS), {
      ...chatData,
      createdAt: Timestamp.fromDate(chatData.createdAt),
      updatedAt: Timestamp.fromDate(chatData.updatedAt),
      lastMessageTime: Timestamp.fromDate(chatData.lastMessageTime)
    });

    // Send initial message if provided
    if (initialMessage && participantIds.length > 0) {
      // Use the first participant as the sender of the initial message
      const senderId = participantIds[0];
      await this.sendMessage({
        chatId: docRef.id,
        text: initialMessage
      }, senderId);
    }

    return docRef.id;
  }

  /**
   * Find existing chat between participants
   */
  private async findExistingChat(participantIds: string[]): Promise<Chat | undefined> {
    const chatsQuery = query(
      collection(db, this.COLLECTIONS.CHATS),
      where('participantIds', 'array-contains', participantIds[0])
    );

    const snapshot = await getDocs(chatsQuery);

    for (const doc of snapshot.docs) {
      const chat = doc.data();
      if (chat.participantIds.length === participantIds.length &&
          participantIds.every(id => chat.participantIds.includes(id))) {
        return { id: doc.id, ...chat } as Chat;
      }
    }

    return undefined;
  }

  /**
   * Get user details for participants
   */
  private async getUserDetails(userIds: string[]): Promise<ChatUser[]> {
    const users: ChatUser[] = [];

    for (const uid of userIds) {
      const userDoc = await getDoc(doc(db, this.COLLECTIONS.USERS, uid));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        users.push({
          uid,
          displayName: userData.name || userData.displayName || 'Unknown User',
          userType: userData.role || userData.userType || 'pet_owner',
          avatarUrl: userData.avatar || userData.avatarUrl,
          email: userData.email,
          isOnline: userData.isOnline || false,
          lastSeen: userData.lastSeen?.toDate()
        });
      }
    }

    return users;
  }

  /**
   * Send a message
   */
  async sendMessage(request: SendMessageRequest, senderId: string): Promise<string> {
    const { chatId, text, mediaFile, replyToMessageId } = request;



    // TEMPORARILY DISABLED - Check if user is suspended (waiting for index to build)
    // const suspensionCheck = await contentModerationService.isUserSuspended(senderId);
    // if (suspensionCheck.isSuspended) {
    //   throw new Error(`You are suspended until ${suspensionCheck.suspendedUntil?.toLocaleDateString()}. Reason: ${suspensionCheck.reason}`);
    // }

    // Get sender details
    const senderDoc = await getDoc(doc(db, this.COLLECTIONS.USERS, senderId));
    if (!senderDoc.exists()) {
      throw new Error('Sender not found');
    }

    const senderData = senderDoc.data();
    let messageText = text || '';
    let mediaUrl = '';
    let mediaType: 'image' | 'file' | 'video' | undefined;
    let fileName = '';
    let fileSize = 0;

    // Handle media upload
    if (mediaFile) {
      const uploadResult = await this.uploadMedia(mediaFile, chatId);
      mediaUrl = uploadResult.url;
      mediaType = uploadResult.type;
      fileName = mediaFile.name;
      fileSize = mediaFile.size;
    }

    // TEMPORARILY DISABLED - Content moderation for text messages (too aggressive)
    // if (messageText) {
    //   const moderationResult = await contentModerationService.flagContent(
    //     senderId,
    //     messageText,
    //     'message',
    //     `${chatId}_${Date.now()}`
    //   );

    //   if (moderationResult.action === 'deleted') {
    //     throw new Error(moderationResult.message);
    //   }

    //   if (moderationResult.action === 'suspended') {
    //     throw new Error(moderationResult.message);
    //   }

    //   // Clean the message if it contains mild profanity
    //   if (!moderationResult.action || moderationResult.action === 'warning') {
    //     messageText = contentModerationService.cleanContent(messageText);
    //   }
    // }

    // Create message
    const messageData: any = {
      chatId,
      senderId,
      senderName: senderData.name || senderData.displayName || 'Unknown User',
      text: messageText,
      timestamp: new Date(),
      readBy: [senderId] // Sender has read their own message
    };

    // Only add optional fields if they have values
    if (senderData.avatar || senderData.avatarUrl) {
      messageData.senderAvatar = senderData.avatar || senderData.avatarUrl;
    }
    if (mediaUrl) {
      messageData.mediaUrl = mediaUrl;
    }
    if (mediaType) {
      messageData.mediaType = mediaType;
    }
    if (fileName) {
      messageData.fileName = fileName;
    }
    if (fileSize) {
      messageData.fileSize = fileSize;
    }
    if (replyToMessageId) {
      messageData.replyTo = replyToMessageId;
    }

    // Add message to chat subcollection
    const chatMessagesRef = collection(db, this.COLLECTIONS.CHATS, chatId, 'messages');
    const messageRef = await addDoc(chatMessagesRef, {
      ...messageData,
      timestamp: Timestamp.fromDate(messageData.timestamp)
    });

    // Update chat with last message info
    await this.updateChatLastMessage(chatId, messageText || 'Media', senderId);

    // Get chat details for notifications
    const chatDoc = await getDoc(doc(db, this.COLLECTIONS.CHATS, chatId));
    if (chatDoc.exists()) {
      const chatData = chatDoc.data() as Chat;

      // Send notifications to all participants except sender
      await notificationService.notifyParticipants(
        chatData.participantIds,
        senderId,
        chatId,
        messageRef.id,
        senderData.name || senderData.displayName || 'Unknown User',
        messageText || 'Sent a file',
        chatData.chatName,
        chatData.isGroupChat,
        chatData.isSupportChat,
        senderData.avatar || senderData.avatarUrl
      );

      // Send unified notifications to all recipients
      const recipients = chatData.participantIds.filter(id => id !== senderId);
      for (const recipientId of recipients) {
        const chatDisplayName = chatData.isGroupChat
          ? (chatData.chatName || 'Group Chat')
          : (senderData.name || senderData.displayName || 'Someone');

        // Send unified notification (appears in notification bell)
        console.log('🔔 Sending unified notification to:', recipientId, 'from:', senderId);
        await notifyNewMessage(
          recipientId,
          senderId,
          senderData.name || senderData.displayName || 'Someone',
          messageText || 'Sent a file',
          chatId,
          messageRef.id,
          senderData.avatar || senderData.avatarUrl,
          chatData.isGroupChat
        );
        console.log('✅ Unified notification sent successfully');

        // Send browser notification
        await notificationService.sendBrowserNotification(
          `New message from ${chatDisplayName}`,
          messageText || 'Sent a file',
          senderData.avatar || senderData.avatarUrl || '/favicon.png',
          chatId
        );
      }
    }

    // Clear typing indicator
    await this.setTyping(chatId, senderId, false);

    return messageRef.id;
  }

  /**
   * Upload media file
   */
  private async uploadMedia(file: File, chatId: string): Promise<{
    url: string;
    type: 'image' | 'file' | 'video';
  }> {
    const timestamp = Date.now();
    const fileName = `${timestamp}_${file.name}`;
    const storageRef = ref(storage, `chat_media/${chatId}/${fileName}`);

    await uploadBytes(storageRef, file);
    const url = await getDownloadURL(storageRef);

    let type: 'image' | 'file' | 'video' = 'file';
    if (file.type.startsWith('image/')) type = 'image';
    else if (file.type.startsWith('video/')) type = 'video';

    return { url, type };
  }

  /**
   * Update chat's last message
   */
  private async updateChatLastMessage(chatId: string, lastMessage: string, senderId: string): Promise<void> {
    const chatRef = doc(db, this.COLLECTIONS.CHATS, chatId);

    await updateDoc(chatRef, {
      lastMessage,
      lastMessageSender: senderId,
      lastMessageTime: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(chatId: string, messageId: string, userId: string): Promise<void> {
    const messageRef = doc(db, this.COLLECTIONS.CHATS, chatId, 'messages', messageId);

    await updateDoc(messageRef, {
      readBy: arrayUnion(userId)
    });
  }

  /**
   * Mark all messages in chat as read
   */
  async markChatAsRead(chatId: string, userId: string): Promise<void> {
    const messagesQuery = query(
      collection(db, this.COLLECTIONS.CHATS, chatId, 'messages'),
      where('readBy', 'not-in', [[userId]])
    );

    const snapshot = await getDocs(messagesQuery);
    const batch = writeBatch(db);

    snapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        readBy: arrayUnion(userId)
      });
    });

    await batch.commit();

    // Reset unread count for this user
    const chatRef = doc(db, this.COLLECTIONS.CHATS, chatId);
    await updateDoc(chatRef, {
      [`unreadCount.${userId}`]: 0
    });
  }

  /**
   * Set typing indicator
   */
  async setTyping(chatId: string, userId: string, isTyping: boolean): Promise<void> {
    const chatRef = doc(db, this.COLLECTIONS.CHATS, chatId);

    if (isTyping) {
      await updateDoc(chatRef, {
        typingUsers: arrayUnion(userId)
      });
    } else {
      await updateDoc(chatRef, {
        typingUsers: arrayRemove(userId)
      });
    }
  }

  /**
   * Get user's chats
   */
  getUserChats(userId: string, callback: (chats: Chat[]) => void): () => void {
    console.log('ChatService: Loading chats for user:', userId);
    const chatsQuery = query(
      collection(db, this.COLLECTIONS.CHATS),
      where('participantIds', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    return onSnapshot(chatsQuery, (snapshot) => {
      const chats: Chat[] = [];
      snapshot.forEach(doc => {
        const data = doc.data();
        chats.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          lastMessageTime: data.lastMessageTime?.toDate() || new Date()
        } as Chat);
      });
      console.log('ChatService: Loaded chats:', chats.length, chats.map(c => ({ id: c.id, participantIds: c.participantIds })));
      callback(chats);
    });
  }

  /**
   * Get a chat by ID
   */
  async getChatById(chatId: string): Promise<Chat | undefined> {
    try {
      const chatDoc = await getDoc(doc(db, this.COLLECTIONS.CHATS, chatId));
      if (chatDoc.exists()) {
        const chatData = chatDoc.data();
        return {
          id: chatDoc.id,
          ...chatData,
          createdAt: chatData.createdAt.toDate(),
          updatedAt: chatData.updatedAt.toDate(),
          lastMessageTime: chatData.lastMessageTime.toDate()
        } as Chat;
      }
      return undefined;
    } catch (error) {
      console.error('Error getting chat by ID:', error);
      return undefined;
    }
  }

  /**
   * Get messages for a chat
   */
  getChatMessages(chatId: string, callback: (messages: Message[]) => void): () => void {
    const messagesQuery = query(
      collection(db, this.COLLECTIONS.CHATS, chatId, 'messages'),
      orderBy('timestamp', 'asc')
    );

    return onSnapshot(messagesQuery, (snapshot) => {
      const messages: Message[] = [];
      snapshot.forEach(doc => {
        const data = doc.data();
        messages.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          editedAt: data.editedAt?.toDate()
        } as Message);
      });
      callback(messages);
    });
  }

  /**
   * Create support chat
   */
  async createSupportChat(userId: string, subject: string, initialMessage: string): Promise<string> {
    // Create or ensure system support user exists
    const supportUserId = 'fetchly_support';
    await this.ensureSupportUserExists();

    return await this.createOrGetChat({
      participantIds: [userId, supportUserId],
      isGroupChat: false,
      initialMessage: `Subject: ${subject}\n\n${initialMessage}`
    });
  }

  /**
   * Ensure system support user exists
   */
  private async ensureSupportUserExists(): Promise<void> {
    const supportUserId = 'fetchly_support';
    const supportUserRef = doc(db, this.COLLECTIONS.USERS, supportUserId);
    const supportUserDoc = await getDoc(supportUserRef);

    if (!supportUserDoc.exists()) {
      await setDoc(supportUserRef, {
        name: 'Fetchly Support',
        displayName: 'Fetchly Support',
        email: '<EMAIL>',
        role: 'support',
        userType: 'support',
        avatar: '/fetchlylogo.png',
        avatarUrl: '/fetchlylogo.png',
        isOnline: true,
        lastSeen: Timestamp.now(),
        createdAt: Timestamp.now()
      });
    }
  }

  /**
   * Delete message
   */
  async deleteMessage(chatId: string, messageId: string, userId: string): Promise<void> {
    const messageRef = doc(db, this.COLLECTIONS.CHATS, chatId, 'messages', messageId);
    const messageDoc = await getDoc(messageRef);

    if (!messageDoc.exists()) {
      throw new Error('Message not found');
    }

    const messageData = messageDoc.data();

    // Only sender can delete their message
    if (messageData.senderId !== userId) {
      throw new Error('You can only delete your own messages');
    }

    await updateDoc(messageRef, {
      isDeleted: true,
      text: 'This message was deleted',
      mediaUrl: '',
      editedAt: serverTimestamp()
    });
  }
}

export const chatService = new ChatService();
export default chatService;
