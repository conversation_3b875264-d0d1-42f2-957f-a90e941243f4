import { DatabaseService, COLLECTIONS, timestampToDate, dateToTimestamp } from '../database';
import { Pet, Vaccination, Medication, VetInfo } from '@/types/user';

export class PetService {
  // Get all pets for a user
  static async getUserPets(userId: string): Promise<Pet[]> {
    try {
      const pets = await DatabaseService.query(
        COLLECTIONS.PETS,
        [{ field: 'userId', operator: '==', value: userId }],
        'createdAt',
        'asc'
      );

      return pets.map(this.mapFirebasePetToPet);
    } catch (error) {
      console.error('Error getting user pets:', error);
      throw new Error('Failed to get pets');
    }
  }

  // Get pet by ID
  static async getPetById(petId: string, userId?: string): Promise<Pet | null> {
    try {
      const pet = await DatabaseService.getById(COLLECTIONS.PETS, petId);

      if (!pet) {
        return null;
      }

      // Check if user owns this pet (if userId provided)
      if (userId && pet.userId !== userId) {
        return null;
      }

      return this.mapFirebasePetToPet(pet);
    } catch (error) {
      console.error('Error getting pet by ID:', error);
      throw new Error('Failed to get pet');
    }
  }

  // Create new pet
  static async createPet(userId: string, petData: Omit<Pet, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<Pet> {
    try {
      const newPetData = {
        userId,
        name: petData.name,
        type: petData.type,
        breed: petData.breed,
        dateOfBirth: petData.dateOfBirth ? dateToTimestamp(new Date(petData.dateOfBirth)) : null,
        weight: petData.weight,
        color: petData.color,
        gender: petData.gender,
        microchipId: petData.microchipId,
        photo: petData.photo,
        vaccinations: petData.vaccinations || [],
        allergies: petData.allergies || [],
        medications: petData.medications || [],
        medicalNotes: petData.medicalNotes,
        vetInfo: petData.vetInfo
      };

      const petId = await DatabaseService.create(COLLECTIONS.PETS, newPetData);
      const createdPet = await DatabaseService.getById(COLLECTIONS.PETS, petId);

      return this.mapFirebasePetToPet(createdPet);
    } catch (error) {
      console.error('Error creating pet:', error);
      throw new Error('Failed to create pet');
    }
  }

  // Update pet - FIRESTORE IMPLEMENTATION
  static async updatePet(petId: string, userId: string, updates: Partial<Pet>): Promise<Pet> {
    try {
      // First verify the pet exists and belongs to the user
      const existingPet = await this.getPetById(petId, userId);
      if (!existingPet) {
        throw new Error('Pet not found or access denied');
      }

      // Prepare update data
      const updateData: any = {};

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.type !== undefined) updateData.type = updates.type;
      if (updates.breed !== undefined) updateData.breed = updates.breed;
      if (updates.dateOfBirth !== undefined) {
        updateData.dateOfBirth = updates.dateOfBirth ? dateToTimestamp(new Date(updates.dateOfBirth)) : null;
      }
      if (updates.weight !== undefined) updateData.weight = updates.weight;
      if (updates.color !== undefined) updateData.color = updates.color;
      if (updates.gender !== undefined) updateData.gender = updates.gender;
      if (updates.microchipId !== undefined) updateData.microchipId = updates.microchipId;
      if (updates.photo !== undefined) updateData.photo = updates.photo;
      if (updates.vaccinations !== undefined) updateData.vaccinations = updates.vaccinations;
      if (updates.allergies !== undefined) updateData.allergies = updates.allergies;
      if (updates.medications !== undefined) updateData.medications = updates.medications;
      if (updates.medicalNotes !== undefined) updateData.medicalNotes = updates.medicalNotes;
      if (updates.vetInfo !== undefined) updateData.vetInfo = updates.vetInfo;

      if (Object.keys(updateData).length === 0) {
        throw new Error('No valid fields to update');
      }

      // Add timestamp
      updateData.updatedAt = dateToTimestamp(new Date());

      // Update in Firestore
      await DatabaseService.update(COLLECTIONS.PETS, petId, updateData);

      // Return updated pet
      const updatedPet = await this.getPetById(petId, userId);
      if (!updatedPet) {
        throw new Error('Failed to retrieve updated pet');
      }

      return updatedPet;
    } catch (error) {
      console.error('Error updating pet:', error);
      throw new Error('Failed to update pet');
    }
  }

  // Delete pet - FIRESTORE IMPLEMENTATION
  static async deletePet(petId: string, userId: string): Promise<void> {
    try {
      // First verify the pet exists and belongs to the user
      const existingPet = await this.getPetById(petId, userId);
      if (!existingPet) {
        throw new Error('Pet not found or access denied');
      }

      // Delete from Firestore
      await DatabaseService.delete(COLLECTIONS.PETS, petId);
    } catch (error) {
      console.error('Error deleting pet:', error);
      throw new Error('Failed to delete pet');
    }
  }

  // Add vaccination record
  static async addVaccination(petId: string, userId: string, vaccination: Vaccination): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedVaccinations = [...(pet.vaccinations || []), vaccination];

      return await this.updatePet(petId, userId, {
        vaccinations: updatedVaccinations
      });
    } catch (error) {
      console.error('Error adding vaccination:', error);
      throw new Error('Failed to add vaccination');
    }
  }

  // Update vaccination record
  static async updateVaccination(petId: string, userId: string, vaccinationIndex: number, vaccination: Vaccination): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedVaccinations = [...(pet.vaccinations || [])];
      if (vaccinationIndex >= 0 && vaccinationIndex < updatedVaccinations.length) {
        updatedVaccinations[vaccinationIndex] = vaccination;
      } else {
        throw new Error('Invalid vaccination index');
      }

      return await this.updatePet(petId, userId, {
        vaccinations: updatedVaccinations
      });
    } catch (error) {
      console.error('Error updating vaccination:', error);
      throw new Error('Failed to update vaccination');
    }
  }

  // Remove vaccination record
  static async removeVaccination(petId: string, userId: string, vaccinationIndex: number): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedVaccinations = [...(pet.vaccinations || [])];
      if (vaccinationIndex >= 0 && vaccinationIndex < updatedVaccinations.length) {
        updatedVaccinations.splice(vaccinationIndex, 1);
      } else {
        throw new Error('Invalid vaccination index');
      }

      return await this.updatePet(petId, userId, {
        vaccinations: updatedVaccinations
      });
    } catch (error) {
      console.error('Error removing vaccination:', error);
      throw new Error('Failed to remove vaccination');
    }
  }

  // Add medication
  static async addMedication(petId: string, userId: string, medication: Medication): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedMedications = [...(pet.medications || []), medication];

      return await this.updatePet(petId, userId, {
        medications: updatedMedications
      });
    } catch (error) {
      console.error('Error adding medication:', error);
      throw new Error('Failed to add medication');
    }
  }

  // Helper method to map Firebase pet to Pet interface
  private static mapFirebasePetToPet(fbPet: any): Pet {
    return {
      id: fbPet.id,
      userId: fbPet.userId,
      name: fbPet.name,
      type: fbPet.type,
      breed: fbPet.breed,
      dateOfBirth: fbPet.dateOfBirth ? timestampToDate(fbPet.dateOfBirth).toISOString().split('T')[0] : undefined,
      weight: fbPet.weight,
      color: fbPet.color,
      gender: fbPet.gender,
      microchipId: fbPet.microchipId,
      photo: fbPet.photo,
      vaccinations: fbPet.vaccinations || [],
      allergies: fbPet.allergies || [],
      medications: fbPet.medications || [],
      medicalNotes: fbPet.medicalNotes,
      vetInfo: fbPet.vetInfo,
      createdAt: fbPet.createdAt ? timestampToDate(fbPet.createdAt).toISOString() : new Date().toISOString(),
      updatedAt: fbPet.updatedAt ? timestampToDate(fbPet.updatedAt).toISOString() : new Date().toISOString()
    };
  }

  // Legacy method for backward compatibility
  private static mapDatabasePetToPet(dbPet: any): Pet {
    console.warn('Legacy PostgreSQL mapping function called. Please use mapFirebasePetToPet instead.');
    return this.mapFirebasePetToPet(dbPet);
  }
}
