import { DatabaseService } from '../database';
import { Booking, BookingStatus } from '@/types/user';
import { Timestamp } from 'firebase/firestore';

export class BookingService {
  // Get all bookings for a user
  static async getUserBookings(userId: string, status?: BookingStatus): Promise<Booking[]> {
    try {
      const conditions = [
        { field: 'userId', operator: '==', value: userId }
      ];

      if (status) {
        conditions.push({ field: 'status', operator: '==', value: status });
      }

      const bookings = await DatabaseService.query(
        'bookings',
        conditions,
        'scheduledDate',
        'desc'
      );

      return bookings.map(booking => this.mapFirestoreBookingToBooking(booking));
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw new Error('Failed to get bookings');
    }
  }

  // Helper method to map Firestore document to Booking object
  private static mapFirestoreBookingToBooking(doc: any): Booking {
    const data = doc.data ? doc.data() : doc;
    return {
      id: doc.id || '',
      userId: data.userId || data.user_id || '',
      petId: data.petId || data.pet_id || '',
      providerId: data.providerId || data.provider_id || '',
      serviceId: data.serviceId || data.service_id || '',
      serviceName: data.serviceName || data.service_name || '',
      providerName: data.providerName || data.provider_name || '',
      petName: data.petName || data.pet_name || '',
      scheduledDate: data.scheduledDate?.toDate?.() || data.scheduled_date?.toDate?.() || data.scheduledDate || data.scheduled_date,
      scheduledTime: data.scheduledTime || data.scheduled_time || '',
      duration: data.duration || 0,
      status: data.status || 'pending_provider_approval',
      totalPrice: data.totalPrice || data.total_price || 0,
      finalAmount: data.finalAmount || data.final_amount,
      paidAmount: data.paidAmount || data.paid_amount || 0,
      paymentMethod: data.paymentMethod || data.payment_method,

      // New payment flow fields
      paymentStatus: data.paymentStatus || data.payment_status || 'pending',
      paymentAuthorized: data.paymentAuthorized || data.payment_authorized || false,
      invoiceDetails: data.invoiceDetails || data.invoice_details,
      invoiceCreatedAt: data.invoiceCreatedAt || data.invoice_created_at,
      paidAt: data.paidAt || data.paid_at,
      notes: data.notes || '',
      specialRequests: data.specialRequests || data.special_requests || '',
      rating: data.rating || 0,
      review: data.review || '',
      reviewDate: data.reviewDate?.toDate?.() || data.review_date?.toDate?.() || data.reviewDate || data.review_date,
      createdAt: data.createdAt?.toDate?.() || data.created_at?.toDate?.() || data.createdAt || data.created_at,
      updatedAt: data.updatedAt?.toDate?.() || data.updated_at?.toDate?.() || data.updatedAt || data.updated_at,
      completedAt: data.completedAt?.toDate?.() || data.completed_at?.toDate?.() || data.completedAt || data.completed_at,
      cancelledAt: data.cancelledAt?.toDate?.() || data.cancelled_at?.toDate?.() || data.cancelledAt || data.cancelled_at,
      cancellationReason: data.cancellationReason || data.cancellation_reason || ''
    };
  }

  // Get booking by ID
  static async getBookingById(bookingId: string, userId?: string): Promise<Booking | null> {
    try {
      const conditions = [];
      
      if (userId) {
        conditions.push({ field: 'userId', operator: '==', value: userId });
      }

      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking || (userId && booking.userId !== userId)) {
        return null;
      }

      return this.mapFirestoreBookingToBooking(booking);
    } catch (error) {
      console.error('Error getting booking by ID:', error);
      throw new Error('Failed to get booking');
    }
  }

  // Create new booking - NO PAYMENT CHARGED YET
  static async createBooking(bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<Booking> {
    try {
      const booking = {
        ...bookingData,
        status: 'pending_provider_approval', // New status - waiting for provider to confirm
        paidAmount: 0, // NO PAYMENT CHARGED YET
        paymentStatus: 'pending', // Track payment status separately
        paymentAuthorized: false, // No payment authorization yet
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      console.log('🔔 Creating booking without charging payment - waiting for provider approval');

      const bookingId = await DatabaseService.create('bookings', booking);
      const createdBooking = await DatabaseService.getById('bookings', bookingId);

      if (!createdBooking) {
        throw new Error('Failed to retrieve created booking');
      }

      const mappedBooking = this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...createdBooking
      });

      // Send notification to provider about new booking request
      try {
        const { notifyBookingRequest } = await import('@/lib/notifications/notification-helpers');
        await notifyBookingRequest(
          bookingData.providerId,
          bookingData.userId,
          bookingData.petName || 'Pet Owner',
          bookingData.serviceName,
          bookingId,
          bookingData.scheduledDate.toDate(),
          bookingData.totalPrice
        );
        console.log('✅ Provider notification sent for new booking request');
      } catch (error) {
        console.error('❌ Failed to send provider notification:', error);
        // Don't fail the booking if notification fails
      }

      return mappedBooking;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw new Error('Failed to create booking');
    }
  }

  // Provider confirms booking and sends invoice - ONLY THEN customer gets charged
  static async confirmBookingAndCreateInvoice(
    bookingId: string,
    providerId: string,
    finalAmount: number,
    invoiceDetails: string
  ): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);

      if (!booking) {
        throw new Error('Booking not found');
      }

      if (booking.providerId !== providerId) {
        throw new Error('Not authorized to confirm this booking');
      }

      if (booking.status !== 'pending_provider_approval') {
        throw new Error('Booking is not in pending approval status');
      }

      // Update booking with provider confirmation and invoice
      const updateData = {
        status: 'confirmed_awaiting_payment', // New status - confirmed but payment pending
        finalAmount, // Provider sets the final amount
        invoiceDetails,
        invoiceCreatedAt: Timestamp.now(),
        paymentStatus: 'invoice_sent', // Invoice sent, waiting for payment
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);

      console.log('🔔 Provider confirmed booking and sent invoice - customer will now be notified to pay');

      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      if (!updatedBooking) {
        throw new Error('Failed to retrieve confirmed booking');
      }

      const mappedBooking = this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });

      // Send notification to customer about invoice and payment request
      try {
        const { notifyBookingConfirmed } = await import('@/lib/notifications/notification-helpers');
        await notifyBookingConfirmed(
          mappedBooking.userId,
          providerId,
          'Provider', // We could get provider name from database if needed
          mappedBooking.serviceName,
          bookingId,
          mappedBooking.scheduledDate.toDate()
        );
        console.log('✅ Customer notification sent for booking confirmation and invoice');
      } catch (error) {
        console.error('❌ Failed to send customer notification:', error);
        // Don't fail the booking if notification fails
      }

      return mappedBooking;
    } catch (error) {
      console.error('Error confirming booking and creating invoice:', error);
      throw new Error('Failed to confirm booking');
    }
  }

  // Customer pays the invoice - ONLY NOW payment is processed
  static async payInvoice(bookingId: string, userId: string, paymentMethodId: string): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);

      if (!booking) {
        throw new Error('Booking not found');
      }

      if (booking.userId !== userId) {
        throw new Error('Not authorized to pay for this booking');
      }

      if (booking.status !== 'confirmed_awaiting_payment') {
        throw new Error('Booking is not ready for payment');
      }

      // TODO: Process actual payment here using Stripe
      // const paymentResult = await PaymentService.processPayment(...)

      // Update booking with payment completion
      const updateData = {
        status: 'confirmed', // Now fully confirmed and paid
        paidAmount: booking.finalAmount,
        paymentStatus: 'paid',
        paymentMethod: paymentMethodId,
        paidAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);

      console.log('✅ Customer paid invoice - booking is now fully confirmed');

      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      if (!updatedBooking) {
        throw new Error('Failed to retrieve paid booking');
      }

      const mappedBooking = this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });

      // Send confirmation notifications to both customer and provider
      try {
        const { notifyPaymentReceived } = await import('@/lib/notifications/notification-helpers');

        // Notify provider about payment received
        await notifyPaymentReceived(
          mappedBooking.providerId,
          mappedBooking.finalAmount || mappedBooking.totalPrice,
          'Customer', // We could get customer name from database if needed
          mappedBooking.serviceName,
          `booking_${bookingId}`
        );

        console.log('✅ Provider notification sent for payment received');
      } catch (error) {
        console.error('❌ Failed to send payment notification:', error);
        // Don't fail the booking if notification fails
      }

      return mappedBooking;
    } catch (error) {
      console.error('Error paying invoice:', error);
      throw new Error('Failed to process payment');
    }
  }

  // Update booking status
  static async updateBookingStatus(bookingId: string, status: BookingStatus, userId?: string): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (userId && booking.userId !== userId) {
        throw new Error('Not authorized to update this booking');
      }

      const updateData: any = {
        status,
        updatedAt: Timestamp.now()
      };

      if (status === 'completed') {
        updateData.completedAt = Timestamp.now();
      } else if (status === 'cancelled') {
        updateData.cancelledAt = Timestamp.now();
      }

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve updated booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw new Error('Failed to update booking status');
    }
  }

  // Cancel booking
  static async cancelBooking(bookingId: string, userId: string, reason?: string): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to cancel this booking');
      }

      const updateData = {
        status: 'cancelled' as const,
        cancelledAt: Timestamp.now(),
        cancellationReason: reason || 'No reason provided',
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve cancelled booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw new Error('Failed to cancel booking');
    }
  }

  // Reschedule booking
  static async rescheduleBooking(
    bookingId: string, 
    userId: string, 
    newDate: string, 
    newTime: string
  ): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to reschedule this booking');
      }

      if (!['pending', 'confirmed'].includes(booking.status)) {
        throw new Error('Only pending or confirmed bookings can be rescheduled');
      }

      const updateData = {
        scheduledDate: newDate,
        scheduledTime: newTime,
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve rescheduled booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      throw new Error('Failed to reschedule booking');
    }
  }

  // Add review and rating
  static async addReview(
    bookingId: string, 
    userId: string, 
    rating: number, 
    review: string
  ): Promise<Booking> {
    try {
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to review this booking');
      }

      if (booking.status !== 'completed') {
        throw new Error('Only completed bookings can be reviewed');
      }

      const updateData = {
        rating,
        review,
        reviewDate: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve booking after review');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error adding review:', error);
      throw new Error('Failed to add review');
    }
  }

  // Process payment for booking
  static async processPayment(
    bookingId: string, 
    userId: string, 
    amount: number, 
    paymentMethod: string
  ): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to process payment for this booking');
      }

      // Update booking with payment
      const updateData = {
        paidAmount: (booking.paidAmount || 0) + amount,
        paymentMethod,
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);

      // Create transaction record
      await DatabaseService.create('transactions', {
        userId,
        bookingId,
        type: 'payment',
        amount,
        description: `Payment for booking ${bookingId}`,
        paymentMethod,
        status: 'completed',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      // Return updated booking
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      if (!updatedBooking) {
        throw new Error('Failed to retrieve booking after payment');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error processing payment:', error);
      throw new Error('Failed to process payment');
    }
  }

  // Get upcoming bookings
  static async getUpcomingBookings(userId: string): Promise<Booking[]> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      const bookings = await DatabaseService.query(
        'bookings',
        [
          { field: 'userId', operator: '==', value: userId },
          { field: 'status', operator: 'in', value: ['pending', 'confirmed'] },
          { field: 'scheduledDate', operator: '>=', value: today }
        ],
        'scheduledDate',
        'asc'
      );

      return bookings.map(booking => this.mapFirestoreBookingToBooking(booking));
    } catch (error) {
      console.error('Error getting upcoming bookings:', error);
      throw new Error('Failed to get upcoming bookings');
    }
  }

  // Helper method to map database booking to Booking interface (kept for backward compatibility)
  private static mapDatabaseBookingToBooking(dbBooking: any): Booking {
    return this.mapFirestoreBookingToBooking(dbBooking);
  }
}
