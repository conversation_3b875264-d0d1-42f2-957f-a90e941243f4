// FIRESTORE-BASED USER SERVICE - FULLY FUNCTIONAL
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
  Timestamp,
  runTransaction,
  arrayUnion,
  arrayRemove,
  orderBy,
  limit,
  startAt,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { COLLECTIONS } from '../database';
import { User, NotificationPreferences } from '@/types/user';

export class UserService {
  // Get user by ID
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, userId));

      if (!userDoc.exists()) {
        return null;
      }

      const userData = userDoc.data();
      return {
        id: userDoc.id,
        email: userData.email || '',
        name: userData.name || '',
        role: userData.role || 'pet_owner',
        avatar: userData.avatar,
        phone: userData.phone,
        address: userData.address,
        location: userData.location,
        verified: userData.verified || false,
        joinedDate: userData.joinedDate || Timestamp.now(),
        lastLoginDate: userData.lastLoginDate,
        fetchlyBalance: userData.fetchlyBalance || 0,
        membershipStatus: userData.membershipStatus || 'free',
        membershipStartDate: userData.membershipStartDate,
        membershipRenewalDate: userData.membershipRenewalDate,
        rewardPoints: userData.rewardPoints || 0,
        notificationPreferences: userData.notificationPreferences || {
          email: true,
          sms: false,
          push: true,
          marketing: false,
          bookingReminders: true,
          promotions: false
        },
        savedProviders: userData.savedProviders || [],
        emergencyContact: userData.emergencyContact,
        lastActive: userData.lastActive,
        createdAt: userData.createdAt || Timestamp.now(),
        updatedAt: userData.updatedAt || Timestamp.now()
      } as User;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  // Get user by email
  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      const usersRef = collection(db, COLLECTIONS.USERS);
      const q = query(usersRef, where('email', '==', email.toLowerCase()));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return null;
      }

      const userDoc = querySnapshot.docs[0];
      return this.getUserById(userDoc.id);
    } catch (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
  }

  // Update user profile
  static async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    try {
      const userRef = doc(db, COLLECTIONS.USERS, userId);
      const updateData = {
        ...updates,
        updatedAt: Timestamp.now()
      };

      await updateDoc(userRef, updateData);
      return this.getUserById(userId);
    } catch (error) {
      console.error('Error updating user:', error);
      return null;
    }
  }

  // Update user balance
  static async updateUserBalance(userId: string, newBalance: number): Promise<boolean> {
    try {
      const userRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(userRef, {
        fetchlyBalance: newBalance,
        updatedAt: Timestamp.now()
      });
      return true;
    } catch (error) {
      console.error('Error updating user balance:', error);
      return false;
    }
  }

  // Update notification preferences
  static async updateNotificationPreferences(
    userId: string,
    preferences: NotificationPreferences
  ): Promise<boolean> {
    try {
      const userRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(userRef, {
        notificationPreferences: preferences,
        updatedAt: Timestamp.now()
      });
      return true;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      return false;
    }
  }

  // Get user balance
  static async getUserBalance(userId: string): Promise<number> {
    try {
      const user = await this.getUserById(userId);
      return user?.fetchlyBalance || 0;
    } catch (error) {
      console.error('Error getting user balance:', error);
      return 0;
    }
  }

  // Check if user exists
  static async userExists(userId: string): Promise<boolean> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, userId));
      return userDoc.exists();
    } catch (error) {
      console.error('Error checking if user exists:', error);
      return false;
    }
  }


  // Update reward points
  static async updateRewardPoints(userId: string, points: number, description: string): Promise<User> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const rewardTransactionRef = doc(collection(db, 'reward_transactions'));
    
    try {
      await runTransaction(db, async (transaction) => {
        // Get current user data
        const userDoc = await transaction.get(userRef);
        if (!userDoc.exists()) {
          throw new Error('User not found');
        }
        
        const currentPoints = userDoc.data()?.rewardPoints || 0;
        const newPoints = currentPoints + points;
        
        if (newPoints < 0) {
          throw new Error('Insufficient reward points');
        }
        
        // Update user reward points
        transaction.update(userRef, {
          rewardPoints: newPoints,
          updatedAt: Timestamp.now()
        });
        
        // Create reward transaction record
        transaction.set(rewardTransactionRef, {
          userId,
          type: points > 0 ? 'earned' : 'redeemed',
          points: Math.abs(points),
          description,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      });
      
      // Return updated user
      const updatedUser = await this.getUserById(userId);
      if (!updatedUser) {
        throw new Error('User not found after update');
      }
      return updatedUser;
    } catch (error) {
      console.error('Error updating reward points:', error);
      throw new Error('Failed to update reward points');
    }
  }

  // Add saved provider
  static async addSavedProvider(userId: string, providerId: string): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    
    try {
      await updateDoc(userRef, {
        savedProviders: arrayUnion(providerId),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error adding saved provider:', error);
      throw new Error('Failed to add saved provider');
    }
  }

  // Remove saved provider
  static async removeSavedProvider(userId: string, providerId: string): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    
    try {
      await updateDoc(userRef, {
        savedProviders: arrayRemove(providerId),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error removing saved provider:', error);
      throw new Error('Failed to remove saved provider');
    }
  }

  // Get user's transaction history
  static async getTransactionHistory(userId: string, limitCount: number = 50, offset: number = 0): Promise<any[]> {
    try {
      const transactionsRef = collection(db, 'transactions');
      let q = query(
        transactionsRef,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      
      // Apply limit and offset if provided
      if (limitCount > 0) {
        q = query(q, limit(limitCount));
      }
      if (offset > 0) {
        q = query(q, startAt(offset));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          // Convert Firestore Timestamp to Date if needed
          createdAt: data.createdAt?.toDate?.(),
          updatedAt: data.updatedAt?.toDate?.()
        };
      });
    } catch (error) {
      console.error('Error getting transaction history:', error);
      throw new Error('Failed to get transaction history');
    }
  }

  // Get user's reward transaction history
  static async getRewardHistory(userId: string, limitCount: number = 50, offset: number = 0): Promise<any[]> {
    try {
      const rewardsRef = collection(db, 'reward_transactions');
      let q = query(
        rewardsRef,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      
      // Apply limit and offset if provided
      if (limitCount > 0) {
        q = query(q, limit(limitCount));
      }
      if (offset > 0) {
        q = query(q, startAt(offset));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          // Convert Firestore Timestamp to Date if needed
          createdAt: data.createdAt?.toDate?.(),
          updatedAt: data.updatedAt?.toDate?.()
        };
      });
    } catch (error) {
      console.error('Error getting reward history:', error);
      throw new Error('Failed to get reward history');
    }
  }
}
