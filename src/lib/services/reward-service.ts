// TEMPORARY STUB IMPLEMENTATION FOR REWARD SERVICE
// This prevents import errors while booking system works
// TODO: Implement proper Firestore-based reward system

export interface RewardItem {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: 'service_discount' | 'free_service' | 'merchandise' | 'cashback';
  discountAmount?: number;
  serviceId?: string;
  isActive: boolean;
  expirationDays?: number;
  image?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface RewardTransaction {
  id: string;
  userId: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  bookingId?: string;
  referralId?: string;
  rewardItemId?: string;
  rewardItemName?: string;
  createdAt: string;
  expiresAt?: string;
}

export class RewardService {
  // STUB METHODS - All return empty/default values to prevent errors

  static async getActiveRewardItems(): Promise<RewardItem[]> {
    return [];
  }

  static async getRewardItemsByCategory(category: string): Promise<RewardItem[]> {
    return [];
  }

  static async getRewardItemById(itemId: string): Promise<RewardItem | null> {
    return null;
  }

  static async redeemReward(userId: string, itemId: string): Promise<{
    success: boolean;
    message: string;
    newBalance?: number;
  }> {
    return { success: false, message: 'Reward system not implemented yet' };
  }

  static async awardPoints(userId: string, points: number, description: string, bookingId?: string): Promise<number> {
    console.log(`Would award ${points} points to user ${userId} for: ${description}`);
    return 0; // Return 0 points for now
  }

  static async getUserRewardHistory(userId: string, limit: number = 50, offset: number = 0): Promise<RewardTransaction[]> {
    return [];
  }

  static async getUserActiveRewards(userId: string): Promise<RewardTransaction[]> {
    return [];
  }

  static async processReferralReward(referrerId: string, referredUserId: string): Promise<number> {
    return 0;
  }
}
