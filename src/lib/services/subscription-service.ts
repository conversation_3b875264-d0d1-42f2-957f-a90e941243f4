import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp, query, where, getDocs } from 'firebase/firestore';

interface SubscribeRequest {
  email: string;
  source?: string; // 'newsletter', 'footer', 'popup', etc.
}

interface SubscriptionRecord {
  email: string;
  source: string;
  subscribedAt: Date;
  isActive: boolean;
  userAgent?: string;
  ipAddress?: string;
}

class SubscriptionService {
  private readonly COLLECTION = 'newsletter_subscriptions';

  /**
   * Subscribe user to newsletter
   */
  async subscribe(request: SubscribeRequest): Promise<{
    success: boolean;
    message: string;
    alreadySubscribed?: boolean;
  }> {
    try {
      const { email, source = 'unknown' } = request;

      // Validate email format
      if (!this.isValidEmail(email)) {
        return {
          success: false,
          message: 'Please enter a valid email address.'
        };
      }

      // Check if already subscribed
      const existingSubscription = await this.checkExistingSubscription(email);
      if (existingSubscription) {
        return {
          success: true,
          message: 'You are already subscribed to our newsletter!',
          alreadySubscribed: true
        };
      }

      // Get additional data for tracking
      const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent : undefined;
      
      // Create subscription record
      const subscriptionData: Omit<SubscriptionRecord, 'subscribedAt'> = {
        email: email.toLowerCase().trim(),
        source,
        isActive: true,
        userAgent
      };

      // Save to Firestore
      await addDoc(collection(db, this.COLLECTION), {
        ...subscriptionData,
        subscribedAt: serverTimestamp()
      });

      // Send notification email to admin (server-side only)
      if (typeof window === 'undefined') {
        try {
          const { emailNotificationService } = await import('./email-service');
          await emailNotificationService.notifySubscription({
            email: email.toLowerCase().trim(),
            source,
            userAgent,
            timestamp: new Date()
          });
        } catch (emailError) {
          console.error('Failed to send notification email:', emailError);
          // Don't fail the subscription if email fails
        }
      }

      console.log(`✅ New subscription: ${email} from ${source}`);

      return {
        success: true,
        message: 'Thank you for subscribing! You\'ll receive our latest updates and pet care tips.'
      };

    } catch (error) {
      console.error('Subscription error:', error);
      
      // Send error notification to admin (server-side only)
      if (typeof window === 'undefined') {
        try {
          const { emailNotificationService } = await import('./email-service');
          await emailNotificationService.notifyGeneral(
            '❌ Subscription Error',
            {
              error: error.message,
              email: request.email,
              source: request.source,
              timestamp: new Date()
            }
          );
        } catch (emailError) {
          console.error('Failed to send error notification email:', emailError);
        }
      }

      return {
        success: false,
        message: 'Something went wrong. Please try again later.'
      };
    }
  }

  /**
   * Unsubscribe user from newsletter
   */
  async unsubscribe(email: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // Find and deactivate subscription
      const subscriptionsQuery = query(
        collection(db, this.COLLECTION),
        where('email', '==', email.toLowerCase().trim()),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(subscriptionsQuery);
      
      if (snapshot.empty) {
        return {
          success: false,
          message: 'Email address not found in our subscription list.'
        };
      }

      // Deactivate all active subscriptions for this email
      const batch = db.batch ? db.batch() : null;
      snapshot.docs.forEach(doc => {
        if (batch) {
          batch.update(doc.ref, {
            isActive: false,
            unsubscribedAt: serverTimestamp()
          });
        }
      });

      if (batch) {
        await batch.commit();
      }

      // Notify admin (server-side only)
      if (typeof window === 'undefined') {
        try {
          const { emailNotificationService } = await import('./email-service');
          await emailNotificationService.notifyGeneral(
            '📧 Newsletter Unsubscription',
            {
              email: email.toLowerCase().trim(),
              timestamp: new Date()
            }
          );
        } catch (emailError) {
          console.error('Failed to send unsubscribe notification email:', emailError);
        }
      }

      return {
        success: true,
        message: 'You have been successfully unsubscribed from our newsletter.'
      };

    } catch (error) {
      console.error('Unsubscribe error:', error);
      return {
        success: false,
        message: 'Something went wrong. Please try again later.'
      };
    }
  }

  /**
   * Check if email is already subscribed
   */
  private async checkExistingSubscription(email: string): Promise<boolean> {
    try {
      const subscriptionsQuery = query(
        collection(db, this.COLLECTION),
        where('email', '==', email.toLowerCase().trim()),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(subscriptionsQuery);
      return !snapshot.empty;
    } catch (error) {
      console.error('Error checking existing subscription:', error);
      return false;
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get subscription statistics (for admin)
   */
  async getSubscriptionStats(): Promise<{
    total: number;
    active: number;
    bySource: { [source: string]: number };
  }> {
    try {
      const allSubscriptionsQuery = query(collection(db, this.COLLECTION));
      const snapshot = await getDocs(allSubscriptionsQuery);
      
      let total = 0;
      let active = 0;
      const bySource: { [source: string]: number } = {};

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        total++;
        
        if (data.isActive) {
          active++;
        }

        const source = data.source || 'unknown';
        bySource[source] = (bySource[source] || 0) + 1;
      });

      return { total, active, bySource };
    } catch (error) {
      console.error('Error getting subscription stats:', error);
      return { total: 0, active: 0, bySource: {} };
    }
  }

  /**
   * Test subscription system
   */
  async testSubscription(): Promise<void> {
    const testEmail = `test-${Date.now()}@example.com`;
    
    console.log('🧪 Testing subscription system...');
    
    const result = await this.subscribe({
      email: testEmail,
      source: 'test'
    });

    console.log('Test result:', result);
    
    if (result.success) {
      console.log('✅ Subscription system is working correctly!');
    } else {
      console.log('❌ Subscription system test failed:', result.message);
    }
  }
}

// Export singleton instance
export const subscriptionService = new SubscriptionService();
export default subscriptionService;

// Export types
export type { SubscribeRequest, SubscriptionRecord };
