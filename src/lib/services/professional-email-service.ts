import { httpsCallable } from 'firebase/functions';
import { functions } from '@/lib/firebase/config';

/**
 * Professional Email Service
 * Uses custom Firebase Functions to send professional emails without Firebase branding
 */
class ProfessionalEmailService {
  
  /**
   * Send professional password reset email
   */
  async sendPasswordReset(email: string): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const sendCustomPasswordReset = httpsCallable(functions, 'sendCustomPasswordReset');
      const result = await sendCustomPasswordReset({ email });
      
      return {
        success: true,
        message: 'Password reset email sent successfully'
      };
    } catch (error: any) {
      console.error('Failed to send password reset email:', error);
      return {
        success: false,
        error: error.message || 'Failed to send password reset email'
      };
    }
  }

  /**
   * Send professional email verification
   */
  async sendEmailVerification(email: string, displayName?: string): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const sendCustomEmailVerification = httpsCallable(functions, 'sendCustomEmailVerification');
      const result = await sendCustomEmailVerification({ 
        email, 
        displayName: displayName || 'User' 
      });
      
      return {
        success: true,
        message: 'Email verification sent successfully'
      };
    } catch (error: any) {
      console.error('Failed to send email verification:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email verification'
      };
    }
  }

  /**
   * Send email change notification
   */
  async sendEmailChangeNotification(
    oldEmail: string, 
    newEmail: string, 
    displayName?: string
  ): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const sendEmailChangeNotification = httpsCallable(functions, 'sendEmailChangeNotification');
      const result = await sendEmailChangeNotification({ 
        oldEmail, 
        newEmail, 
        displayName: displayName || 'User' 
      });
      
      return {
        success: true,
        message: 'Email change notifications sent successfully'
      };
    } catch (error: any) {
      console.error('Failed to send email change notifications:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email change notifications'
      };
    }
  }

  /**
   * Send MFA (Multi-Factor Authentication) notification
   */
  async sendMFANotification(
    email: string, 
    action: 'enabled' | 'disabled', 
    displayName?: string
  ): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const sendMFANotification = httpsCallable(functions, 'sendMFANotification');
      const result = await sendMFANotification({ 
        email, 
        action, 
        displayName: displayName || 'User' 
      });
      
      return {
        success: true,
        message: 'MFA notification sent successfully'
      };
    } catch (error: any) {
      console.error('Failed to send MFA notification:', error);
      return {
        success: false,
        error: error.message || 'Failed to send MFA notification'
      };
    }
  }

  /**
   * Send admin notification for password reset requests
   */
  async notifyAdminPasswordReset(userEmail: string): Promise<void> {
    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: '🔐 Password Reset Request',
          data: {
            type: 'password_reset_request',
            userEmail: userEmail,
            timestamp: new Date(),
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
            action: 'Password Reset Requested'
          }
        })
      });
    } catch (error) {
      console.error('Failed to send admin notification:', error);
      // Don't throw error - admin notification failure shouldn't break user flow
    }
  }

  /**
   * Send admin notification for email verifications
   */
  async notifyAdminEmailVerification(userEmail: string, displayName?: string): Promise<void> {
    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: '✅ Email Verification Request',
          data: {
            type: 'email_verification_request',
            userEmail: userEmail,
            userName: displayName || 'User',
            timestamp: new Date(),
            action: 'Email Verification Requested'
          }
        })
      });
    } catch (error) {
      console.error('Failed to send admin notification:', error);
    }
  }

  /**
   * Send admin notification for email changes
   */
  async notifyAdminEmailChange(oldEmail: string, newEmail: string, displayName?: string): Promise<void> {
    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: '📧 User Email Address Changed',
          data: {
            type: 'email_change',
            oldEmail: oldEmail,
            newEmail: newEmail,
            userName: displayName || 'User',
            timestamp: new Date(),
            action: 'Email Address Changed'
          }
        })
      });
    } catch (error) {
      console.error('Failed to send admin notification:', error);
    }
  }

  /**
   * Send admin notification for MFA changes
   */
  async notifyAdminMFAChange(
    userEmail: string, 
    action: 'enabled' | 'disabled', 
    displayName?: string
  ): Promise<void> {
    try {
      await fetch('/api/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: `🔐 Two-Factor Authentication ${action === 'enabled' ? 'Enabled' : 'Disabled'}`,
          data: {
            type: 'mfa_change',
            userEmail: userEmail,
            userName: displayName || 'User',
            action: `Two-Factor Authentication ${action === 'enabled' ? 'Enabled' : 'Disabled'}`,
            timestamp: new Date()
          }
        })
      });
    } catch (error) {
      console.error('Failed to send admin notification:', error);
    }
  }
}

// Export singleton instance
export const professionalEmailService = new ProfessionalEmailService();
export default professionalEmailService;
