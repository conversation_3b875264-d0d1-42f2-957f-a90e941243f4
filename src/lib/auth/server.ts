import { auth as adminAuth } from '@/lib/firebase/admin-config';

/**
 * Verify Firebase ID token on the server side
 */
export async function verifyIdToken(token: string) {
  try {
    if (!adminAuth) {
      throw new Error('Firebase Admin not initialized');
    }
    
    const decodedToken = await adminAuth.verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * Extract and verify token from Authorization header
 */
export async function verifyAuthHeader(authHeader: string | null) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  return await verifyIdToken(token);
}

/**
 * Middleware-style auth verification
 */
export async function requireAuth(request: Request) {
  const authHeader = request.headers.get('authorization');
  const decodedToken = await verifyAuthHeader(authHeader);
  
  if (!decodedToken) {
    throw new Error('Unauthorized');
  }
  
  return decodedToken;
}
