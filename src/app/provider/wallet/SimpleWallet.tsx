'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { doc, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { stripeService } from '@/lib/services/stripe';
import { PageLoader } from '@/components/LoadingSpinner';

interface StripeAccount {
  id: string;
  isConnected: boolean;
  payouts_enabled?: boolean;
}

interface Balance {
  available: number;
  pending: number;
  currency: string;
}

export default function SimpleWallet() {
  const { user } = useAuth();
  const [account, setAccount] = useState<StripeAccount | null>(null);
  const [balance, setBalance] = useState<Balance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  // Handle Stripe connection
  const connectStripe = async () => {
    if (!user?.id || !user?.email) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/stripe/onboard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          email: user.email,
          returnUrl: `${window.location.origin}/provider/wallet`,
        }),
      });
      
      const data = await response.json();
      if (data.url) window.location.href = data.url;
    } catch (err) {
      setError('Failed to connect to Stripe');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch account and balance
  useEffect(() => {
    if (!user?.id) return;

    const unsubscribe = onSnapshot(
      doc(db, 'stripeAccounts', user.id),
      async (doc) => {
        if (doc.exists()) {
          const accountData = doc.data() as StripeAccount;
          setAccount(accountData);
          
          if (accountData.isConnected) {
            try {
              const balanceData = await stripeService.getBalance();
              setBalance(balanceData);
            } catch (err) {
              console.error('Error fetching balance:', err);
            }
          }
        }
        setLoading(false);
      },
      (error) => {
        console.error('Error fetching account:', error);
        setError('Failed to load account');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user?.id]);

  if (loading) return <PageLoader />;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Your Wallet</h1>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {!account?.isConnected ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <h2 className="text-lg font-medium mb-2">Connect Stripe</h2>
          <p className="text-gray-600 mb-4">Start receiving payments</p>
          <button
            onClick={connectStripe}
            disabled={loading}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50"
          >
            {loading ? 'Connecting...' : 'Connect with Stripe'}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Balance</h2>
            {balance ? (
              <div className="space-y-2">
                <p className="text-2xl font-bold">
                  {formatCurrency(balance.available, balance.currency)}
                </p>
                <p className="text-sm text-gray-500">
                  Pending: {formatCurrency(balance.pending, balance.currency)}
                </p>
              </div>
            ) : (
              <p>Loading balance...</p>
            )}
          </div>
          
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium mb-4">Recent Activity</h2>
            <p className="text-gray-500">Transaction history will appear here</p>
          </div>
        </div>
      )}
    </div>
  );
}
