'use client';

import { useState } from 'react';
import { Search, HelpCircle, MessageCircle, Phone, Mail, Book, Users, Shield } from 'lucide-react';
import Link from 'next/link';

const faqCategories = [
  {
    title: "Getting Started",
    icon: Book,
    faqs: [
      {
        question: "How do I create an account?",
        answer: "Click 'Sign Up' in the top right corner, choose your account type (Pet Owner or Service Provider), and fill out the registration form."
      },
      {
        question: "Is Fetchly free to use?",
        answer: "Yes! Creating an account and browsing services is completely free. You only pay for the services you book."
      },
      {
        question: "How do I find services near me?",
        answer: "Use our search feature on the homepage or visit the 'Find Services' page to filter by location, service type, and availability."
      }
    ]
  },
  {
    title: "Booking & Payments",
    icon: Users,
    faqs: [
      {
        question: "How do I book a service?",
        answer: "Find a provider you like, click 'Book Now', select your preferred date and time, and complete the booking form with your pet's details."
      },
      {
        question: "What payment methods do you accept?",
        answer: "We accept all major credit cards, debit cards, and digital wallets like Apple Pay and Google Pay."
      },
      {
        question: "Can I cancel or reschedule my booking?",
        answer: "Yes, you can cancel or reschedule up to 24 hours before your appointment. Check the provider's specific cancellation policy for details."
      }
    ]
  },
  {
    title: "Safety & Trust",
    icon: Shield,
    faqs: [
      {
        question: "How do you verify service providers?",
        answer: "All providers undergo background checks, license verification, insurance confirmation, and reference checks before joining our platform."
      },
      {
        question: "What if something goes wrong during a service?",
        answer: "Contact our support team immediately. We have insurance coverage and will work to resolve any issues quickly and fairly."
      },
      {
        question: "How can I report a problem with a provider?",
        answer: "Use the 'Report Issue' button in your booking history or contact our support team directly through chat, email, or phone."
      }
    ]
  }
];

const supportOptions = [
  {
    icon: Mail,
    title: "Email Support",
    description: "Send us a detailed message for any questions or issues",
    action: "Send Email",
    available: "Response within 24hrs",
    link: "mailto:<EMAIL>"
  }
];

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(0);
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const filteredFaqs = faqCategories[selectedCategory].faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 pt-20">
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-blue-100/20 to-cyan-100/30"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl animate-bounce"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center shadow-xl">
                <HelpCircle className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-5xl md:text-6xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Help Center
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto mb-8 font-medium">
              Find answers to your questions and get the support you need
            </p>
            
            {/* Search Bar */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 max-w-2xl mx-auto border border-white/30">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-green-600" />
                <input
                  type="text"
                  placeholder="Search for help articles, FAQs, or topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-14 pr-4 py-4 rounded-2xl border-2 border-green-200 bg-white/90 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 text-lg font-medium"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Support Options */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Get Support
            </h2>
            <p className="text-xl text-cool-600">
              Choose the best way to reach our support team
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {supportOptions.map((option, index) => {
              const Icon = option.icon;
              return (
                <div key={index} className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-cool-800">{option.title}</h3>
                  <p className="text-cool-600 mb-4">{option.description}</p>
                  <div className="text-sm text-cool-500 mb-4">{option.available}</div>
                  <a
                    href={option.link}
                    target={option.link?.startsWith('http') ? '_blank' : '_self'}
                    rel={option.link?.startsWith('http') ? 'noopener noreferrer' : undefined}
                    className="btn-primary w-full inline-block"
                  >
                    {option.action}
                  </a>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-cool-600">
              Quick answers to common questions
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Category Sidebar */}
              <div className="lg:col-span-1">
                <div className="glass-card rounded-2xl p-6">
                  <h3 className="font-bold text-cool-800 mb-4">Categories</h3>
                  <div className="space-y-2">
                    {faqCategories.map((category, index) => {
                      const Icon = category.icon;
                      return (
                        <button
                          key={index}
                          onClick={() => setSelectedCategory(index)}
                          className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 flex items-center gap-3 ${
                            selectedCategory === index 
                              ? 'bg-primary-500 text-white' 
                              : 'text-cool-700 hover:bg-white/50'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          {category.title}
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* FAQ Content */}
              <div className="lg:col-span-3">
                <div className="space-y-4">
                  {filteredFaqs.map((faq, index) => (
                    <div key={index} className="glass-card rounded-2xl overflow-hidden">
                      <button
                        onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                        className="w-full px-6 py-4 text-left hover:bg-white/30 transition-all duration-300"
                      >
                        <div className="flex justify-between items-center">
                          <h3 className="font-bold text-cool-800">{faq.question}</h3>
                          <div className={`transform transition-transform duration-300 ${expandedFaq === index ? 'rotate-180' : ''}`}>
                            <HelpCircle className="w-5 h-5 text-primary-500" />
                          </div>
                        </div>
                      </button>
                      {expandedFaq === index && (
                        <div className="px-6 pb-4">
                          <p className="text-cool-600">{faq.answer}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {filteredFaqs.length === 0 && (
                  <div className="glass-card rounded-2xl p-8 text-center">
                    <p className="text-cool-600">No FAQs found matching your search.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-cool-800">
              Popular Help Topics
            </h2>
            <p className="text-xl text-cool-600">
              Quick access to commonly requested information
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <Link href="/help/booking-guide" className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
              <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                <Book className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-bold text-cool-800 mb-2">Booking Guide</h3>
              <p className="text-cool-600 text-sm">Step-by-step booking instructions</p>
            </Link>

            <Link href="/help/payment-info" className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
              <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-bold text-cool-800 mb-2">Payment Info</h3>
              <p className="text-cool-600 text-sm">Payment methods and billing</p>
            </Link>

            <Link href="/help/safety-guidelines" className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
              <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-bold text-cool-800 mb-2">Safety Guidelines</h3>
              <p className="text-cool-600 text-sm">Safety policies and procedures</p>
            </Link>

            <Link href="/help/provider-info" className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
              <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 flex items-center justify-center">
                <HelpCircle className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-bold text-cool-800 mb-2">Provider Info</h3>
              <p className="text-cool-600 text-sm">Information for service providers</p>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-3xl mx-auto text-center relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-secondary-500/5 to-accent-500/5"></div>
            <div className="relative z-10">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Still Need Help?
              </h2>
              <p className="text-xl text-cool-600 mb-8">
                Our support team is here to help you with any questions or concerns
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact" className="btn-primary">
                  Contact Support
                </Link>
                <Link href="/community" className="btn-secondary">
                  Ask the Community
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
