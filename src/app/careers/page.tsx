'use client';

import { Briefcase, MapPin, Clock, Users, Star, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const openPositions = [
  {
    title: "Pet Care Specialist",
    department: "Operations",
    location: "San Juan, PR / Remote",
    type: "Full-time",
    description: "Help connect pet owners with trusted service providers in Puerto Rico."
  },
  {
    title: "Customer Support Representative",
    department: "Customer Success",
    location: "San Juan, PR / Remote",
    type: "Full-time",
    description: "Provide exceptional support to pet owners and service providers on our platform."
  },
  {
    title: "Business Development Associate",
    department: "Business Development",
    location: "San Juan, PR / Remote",
    type: "Full-time",
    description: "Expand our network of trusted pet service providers across Puerto Rico."
  },
  {
    title: "Marketing Coordinator",
    department: "Marketing",
    location: "San Juan, PR / Remote",
    type: "Part-time",
    description: "Create engaging content and campaigns to grow our pet care community in Puerto Rico."
  }
];

const benefits = [
  {
    icon: () => (
      <Image
        src="/fetchlylogo.png"
        alt="Fetchly Logo"
        width={32}
        height={32}
        className="w-8 h-8"
      />
    ),
    title: "Pet-First Culture",
    description: "Work with a team that truly cares about pets and their wellbeing"
  },
  {
    icon: Users,
    title: "Growing Team",
    description: "Join a startup environment with opportunities for growth and impact"
  },
  {
    icon: Star,
    title: "Professional Development",
    description: "Learning opportunities and career advancement in the pet care industry"
  },
  {
    icon: Clock,
    title: "Remote Flexibility",
    description: "Work from home options with flexible scheduling to fit your lifestyle"
  }
];

export default function CareersPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 pt-20">
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-blue-100/20 to-cyan-100/30"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl animate-bounce"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center shadow-xl">
                <Briefcase className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-5xl md:text-6xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Join Our Team
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto mb-8 font-medium">
              Help us build the future of pet care and make a difference in pets' lives every day
            </p>
          </div>

          <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-12 max-w-4xl mx-auto text-center border border-white/30">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">Why Work at Fetchly?</h2>
            <p className="text-gray-700 text-lg leading-relaxed font-medium">
              At Fetchly, we're more than just a tech company – we're a community of pet lovers working
              to improve the lives of pets and their families. Join us in building innovative solutions
              that connect pet owners with trusted care providers while creating a positive impact in
              the pet care industry.
            </p>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
              Why You'll Love It Here
            </h2>
            <p className="text-xl text-gray-700 font-medium">
              We offer competitive benefits and a culture that puts pets and people first
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div key={index} className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl p-8 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group border border-white/30">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-800">{benefit.title}</h3>
                  <p className="text-gray-600 font-medium">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
              Open Positions
            </h2>
            <p className="text-xl text-gray-700 font-medium">
              Find your next career opportunity with us
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {openPositions.map((position, index) => (
              <div key={index} className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl p-8 hover:shadow-2xl hover:scale-105 transition-all duration-500 group border border-white/30">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-4 mb-4">
                      <h3 className="text-2xl font-bold text-gray-800 group-hover:bg-gradient-to-r group-hover:from-green-600 group-hover:to-blue-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                        {position.title}
                      </h3>
                      <span className="px-4 py-2 bg-gradient-to-r from-green-100 to-blue-100 text-green-700 rounded-full text-sm font-semibold border border-green-200">
                        {position.department}
                      </span>
                    </div>

                    <div className="flex flex-wrap items-center gap-6 mb-4 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-5 h-5 text-green-600" />
                        <span className="font-medium">{position.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5 text-blue-600" />
                        <span className="font-medium">{position.type}</span>
                      </div>
                    </div>

                    <p className="text-gray-700 font-medium text-lg">{position.description}</p>
                  </div>

                  <div className="mt-6 lg:mt-0 lg:ml-8">
                    <button className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-2xl shadow-lg hover:shadow-xl flex items-center gap-3 group-hover:scale-105 transition-all duration-300 font-semibold text-lg">
                      Apply Now
                      <ArrowRight className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-cool-600 mb-4">Don't see a position that fits?</p>
            <Link href="/contact" className="btn-secondary">
              Send Us Your Resume
            </Link>
          </div>
        </div>
      </section>

      {/* Company Culture */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                Our Culture
              </h2>
              <p className="text-xl text-cool-600">
                What makes Fetchly a great place to work
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Image
                    src="/fetchlylogo.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Pet-First Mindset</h3>
                <p className="text-cool-600 text-sm">Every decision considers the impact on pets and their wellbeing</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Collaborative Team</h3>
                <p className="text-cool-600 text-sm">We work together to solve problems and celebrate successes</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Innovation Focus</h3>
                <p className="text-cool-600 text-sm">We encourage creative thinking and new approaches</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Application Process
              </h2>
              <p className="text-xl text-cool-600">
                Here's what to expect when you apply
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white flex items-center justify-center font-bold text-lg">
                  1
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Apply Online</h3>
                <p className="text-cool-600 text-sm">Submit your application and resume</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 text-white flex items-center justify-center font-bold text-lg">
                  2
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Phone Screen</h3>
                <p className="text-cool-600 text-sm">Brief conversation with our team</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 text-white flex items-center justify-center font-bold text-lg">
                  3
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Interview</h3>
                <p className="text-cool-600 text-sm">Meet the team and discuss the role</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 text-white flex items-center justify-center font-bold text-lg">
                  4
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Welcome!</h3>
                <p className="text-cool-600 text-sm">Join the Fetchly family</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
