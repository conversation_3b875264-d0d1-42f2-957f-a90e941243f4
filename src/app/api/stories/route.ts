import { NextRequest, NextResponse } from 'next/server';
import { StoriesService } from '@/lib/services/stories-service';

/**
 * GET /api/stories - Fetch active stories
 * Query parameters:
 * - userId: Filter stories for specific user (optional)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (userId) {
      // Get stories for specific user
      const stories = await StoriesService.getUserStories(userId);
      return NextResponse.json({
        success: true,
        data: stories,
        count: stories.length
      });
    } else {
      // Get all active stories grouped by user
      const userStories = await StoriesService.getActiveStories();
      return NextResponse.json({
        success: true,
        data: userStories,
        count: userStories.length
      });
    }
  } catch (error: any) {
    console.error('Error fetching stories:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to fetch stories'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/stories - Create a new story
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, userName, userAvatar, mediaUrl, type, content, isPublic } = body;

    // Validate required fields
    if (!userId || !userName || !userAvatar || !mediaUrl || !type) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: userId, userName, userAvatar, mediaUrl, type'
        },
        { status: 400 }
      );
    }

    // Validate type
    if (!['image', 'video'].includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid type. Must be "image" or "video"'
        },
        { status: 400 }
      );
    }

    // Create the story
    const storyId = await StoriesService.createStory({
      userId,
      userName,
      userAvatar,
      mediaUrl,
      type,
      content,
      isPublic
    });

    return NextResponse.json({
      success: true,
      data: {
        id: storyId,
        message: 'Story created successfully'
      }
    });
  } catch (error: any) {
    console.error('Error creating story:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create story'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/stories - Delete expired stories (cleanup)
 */
export async function DELETE(request: NextRequest) {
  try {
    const deletedCount = await StoriesService.deleteExpiredStories();
    
    return NextResponse.json({
      success: true,
      data: {
        deletedCount,
        message: `Deleted ${deletedCount} expired stories`
      }
    });
  } catch (error: any) {
    console.error('Error deleting expired stories:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to delete expired stories'
      },
      { status: 500 }
    );
  }
}
