import { NextRequest, NextResponse } from 'next/server';
import { StoriesService } from '@/lib/services/stories-service';

/**
 * POST /api/stories/[storyId]/view - Mark story as viewed
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { storyId: string } }
) {
  try {
    const { storyId } = params;
    const body = await request.json();
    const { viewerId } = body;

    if (!viewerId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required field: viewerId'
        },
        { status: 400 }
      );
    }

    await StoriesService.markStoryAsViewed(storyId, viewerId);

    return NextResponse.json({
      success: true,
      data: {
        message: 'Story marked as viewed'
      }
    });
  } catch (error: any) {
    console.error('Error marking story as viewed:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to mark story as viewed'
      },
      { status: 500 }
    );
  }
}
