import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { CommissionService } from '@/lib/monetization/commission-service';

// Calculate booking pricing
async function calculatePricingHandler(request: NextRequest) {
  try {
    const { 
      servicePrice, 
      addOns = [], 
      tip = 0, 
      scheduledDateTime,
      cancellationFee = 0 
    } = await request.json();

    if (!servicePrice || servicePrice <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Valid service price is required'
      }, { status: 400 });
    }

    // Calculate if it's an express booking
    const scheduledDate = new Date(scheduledDateTime);
    const now = new Date();
    const hoursUntilBooking = (scheduledDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    const isExpressBooking = CommissionService.isExpressBooking(hoursUntilBooking);

    // Calculate pricing breakdown
    const pricing = CommissionService.calculateBookingPricing(
      servicePrice,
      addOns,
      tip,
      isExpressBooking,
      cancellationFee
    );

    // Get display breakdown
    const breakdown = CommissionService.getPricingBreakdown(
      servicePrice,
      addOns,
      tip,
      isExpressBooking,
      cancellationFee
    );

    return NextResponse.json({
      success: true,
      pricing,
      breakdown,
      isExpressBooking,
      hoursUntilBooking: Math.round(hoursUntilBooking * 10) / 10
    });
  } catch (error) {
    console.error('Calculate pricing error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to calculate pricing'
    }, { status: 500 });
  }
}

// Calculate cancellation fee
async function calculateCancellationFeeHandler(request: NextRequest) {
  try {
    const { bookingAmount, scheduledDateTime } = await request.json();

    if (!bookingAmount || bookingAmount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Valid booking amount is required'
      }, { status: 400 });
    }

    const scheduledDate = new Date(scheduledDateTime);
    const now = new Date();
    const hoursUntilBooking = (scheduledDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    const cancellationFee = CommissionService.calculateCancellationFee(
      bookingAmount,
      hoursUntilBooking
    );

    return NextResponse.json({
      success: true,
      cancellationFee,
      hoursUntilBooking: Math.round(hoursUntilBooking * 10) / 10,
      feeApplies: cancellationFee > 0
    });
  } catch (error) {
    console.error('Calculate cancellation fee error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to calculate cancellation fee'
    }, { status: 500 });
  }
}

export const POST = withMiddleware(calculatePricingHandler, {
  auth: false // Allow unauthenticated pricing calculations
});

// Separate endpoint for cancellation fee calculation
export async function PUT(request: NextRequest) {
  return withMiddleware(calculateCancellationFeeHandler, {
    auth: true,
    requireVerified: true
  })(request);
}
