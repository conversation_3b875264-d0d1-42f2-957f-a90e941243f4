import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { BookingService } from '@/lib/services';
import { notifyBookingConfirmed } from '@/lib/notifications/notification-helpers';

// Provider confirms booking and sends invoice
async function confirmBooking<PERSON>and<PERSON>(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { bookingId, finalAmount, invoiceDetails } = await request.json();

    // Validate input
    if (!bookingId || !finalAmount || !invoiceDetails) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: bookingId, finalAmount, invoiceDetails'
      }, { status: 400 });
    }

    if (finalAmount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Final amount must be greater than 0'
      }, { status: 400 });
    }

    // Confirm booking and create invoice
    const booking = await BookingService.confirmBookingAndCreateInvoice(
      bookingId,
      user.id, // providerId
      finalAmount,
      invoiceDetails
    );

    // Send notification to customer about invoice
    await notifyBookingConfirmed(
      booking.userId,
      user.id,
      user.name || 'Provider',
      booking.serviceName,
      bookingId,
      booking.scheduledDate.toDate()
    );

    return NextResponse.json({
      success: true,
      booking,
      message: 'Booking confirmed and invoice sent to customer'
    });
  } catch (error: any) {
    console.error('Confirm booking error:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to confirm booking'
    }, { status: 500 });
  }
}

export const POST = withMiddleware(confirmBookingHandler);
