import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { BoostService } from '@/lib/monetization/boost-service';

// Get provider boosts
async function getBoostsHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    
    if (user.role !== 'provider') {
      return NextResponse.json({
        success: false,
        error: 'Only providers can access boosts'
      }, { status: 403 });
    }

    const activeBoosts = await BoostService.getActiveBoosts(user.id);
    const analytics = await BoostService.getBoostAnalytics(user.id);
    
    return NextResponse.json({
      success: true,
      activeBoosts,
      analytics: analytics.success ? analytics.analytics : null
    });
  } catch (error) {
    console.error('Get boosts error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get boosts'
    }, { status: 500 });
  }
}

// Purchase boost
async function purchaseBoostHand<PERSON>(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { boostType, customerId } = await request.json();
    
    if (user.role !== 'provider') {
      return NextResponse.json({
        success: false,
        error: 'Only providers can purchase boosts'
      }, { status: 403 });
    }

    if (!['topInSearch', 'featuredToday'].includes(boostType)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid boost type'
      }, { status: 400 });
    }

    const result = await BoostService.createBoostPaymentIntent(
      user.id,
      boostType,
      customerId
    );

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      paymentIntent: result.paymentIntent,
      clientSecret: result.clientSecret,
      boostId: result.boostId
    });
  } catch (error) {
    console.error('Purchase boost error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to purchase boost'
    }, { status: 500 });
  }
}

export const GET = withMiddleware(getBoostsHandler, {
  auth: true,
  requireVerified: true
});

export const POST = withMiddleware(purchaseBoostHandler, {
  auth: true,
  requireVerified: true
});
