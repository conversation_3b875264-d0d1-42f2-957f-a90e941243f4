import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { userId, email, returnUrl } = await req.json();
    
    if (!userId || !email) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    // Check if user already has a Stripe account
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    const userData = userDoc.data();

    let accountId = userData?.stripeAccountId;

    if (!accountId) {
      // Create a new Stripe account
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: 'individual',
        business_profile: {
          product_description: 'Pet services on Fetchly',
        },
      });

      accountId = account.id;

      // Save the Stripe account ID to Firestore
      await setDoc(
        userRef,
        {
          stripeAccountId: accountId,
          isStripeConnected: false,
          updatedAt: new Date().toISOString(),
        },
        { merge: true }
      );
    }

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: returnUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return NextResponse.json({ url: accountLink.url });
  } catch (error) {
    console.error('Error creating Stripe account link:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
