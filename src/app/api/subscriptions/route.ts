import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { SubscriptionService } from '@/lib/stripe/subscription-service';
import { ServerSubscriptionService } from '@/lib/stripe/server-subscription-service';

// Get provider subscription
async function getSubscriptionHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    
    if (user.role !== 'provider') {
      return NextResponse.json({
        success: false,
        error: 'Only providers can access subscriptions'
      }, { status: 403 });
    }

    const subscription = await SubscriptionService.getSubscription(user.id);
    
    return NextResponse.json({
      success: true,
      subscription
    });
  } catch (error) {
    console.error('Get subscription error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get subscription'
    }, { status: 500 });
  }
}

// Create subscription checkout
async function createSubscriptionHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { tier, successUrl, cancelUrl } = await request.json();
    
    if (user.role !== 'provider') {
      return NextResponse.json({
        success: false,
        error: 'Only providers can create subscriptions'
      }, { status: 403 });
    }

    if (!['pro', 'premium'].includes(tier)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid subscription tier'
      }, { status: 400 });
    }

    const result = await ServerSubscriptionService.createSubscriptionCheckout(
      user.id,
      tier,
      successUrl || `${process.env.NEXT_PUBLIC_APP_URL}/provider/dashboard?tab=subscription&success=true`,
      cancelUrl || `${process.env.NEXT_PUBLIC_APP_URL}/provider/dashboard?tab=subscription&cancelled=true`
    );

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      sessionId: result.sessionId,
      url: result.url
    });
  } catch (error) {
    console.error('Create subscription error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create subscription'
    }, { status: 500 });
  }
}

export const GET = withMiddleware(getSubscriptionHandler, {
  auth: true,
  requireVerified: true
});

export const POST = withMiddleware(createSubscriptionHandler, {
  auth: true,
  requireVerified: true
});
