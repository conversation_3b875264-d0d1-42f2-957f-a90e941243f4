import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Test basic API functionality
    return NextResponse.json({
      success: true,
      message: 'Wallet API is working',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      hasStripeKey: !!process.env.STRIPE_SECRET_KEY,
      hasFirebaseAdmin: !!process.env.FIREBASE_ADMIN_PROJECT_ID,
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    return NextResponse.json({
      success: true,
      message: 'POST request received',
      body,
      headers: {
        authorization: request.headers.get('authorization') ? 'Present' : 'Missing',
        contentType: request.headers.get('content-type'),
      }
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
