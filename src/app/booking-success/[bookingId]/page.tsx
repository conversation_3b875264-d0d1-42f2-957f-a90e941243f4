'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { CheckCircle, Calendar, Clock, User, PawPrint, DollarSign, MessageCircle, Star } from 'lucide-react';
import Link from 'next/link';

interface BookingData {
  id: string;
  providerName: string;
  serviceName: string;
  petName: string;
  scheduledDate: string;
  scheduledTime: string;
  finalPrice?: number;
  totalPrice: number;
  status: string;
  paymentStatus: string;
  userId: string;
  providerId: string;
  paymentAmount?: number;
}

export default function BookingSuccessPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [booking, setBooking] = useState<BookingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const bookingId = params.bookingId as string;

  useEffect(() => {
    const fetchBooking = async () => {
      if (!isAuthenticated || !user) {
        router.push('/auth/signin');
        return;
      }

      try {
        const bookingRef = doc(db, 'bookings', bookingId);
        const bookingDoc = await getDoc(bookingRef);

        if (!bookingDoc.exists()) {
          setError('Booking not found');
          return;
        }

        const bookingData = { id: bookingDoc.id, ...bookingDoc.data() } as BookingData;

        // Verify user owns this booking
        if (bookingData.userId !== user.id) {
          setError('Unauthorized access');
          return;
        }

        setBooking(bookingData);
      } catch (error) {
        console.error('Error fetching booking:', error);
        setError('Failed to load booking details');
      } finally {
        setLoading(false);
      }
    };

    fetchBooking();
  }, [bookingId, isAuthenticated, user, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading booking details...</p>
        </div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Booking not found'}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  const amount = booking.paymentAmount || booking.finalPrice || booking.totalPrice;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-800 mb-2">Booking Confirmed! 🎉</h1>
          <p className="text-xl text-gray-600">Your payment was successful</p>
        </div>

        {/* Booking Details Card */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Booking Details</h2>
          
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <User className="w-6 h-6 text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">Provider</p>
                <p className="text-gray-600">{booking.providerName}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <PawPrint className="w-6 h-6 text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">Service</p>
                <p className="text-gray-600">{booking.serviceName} for {booking.petName}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <Calendar className="w-6 h-6 text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">Date</p>
                <p className="text-gray-600">{booking.scheduledDate}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <Clock className="w-6 h-6 text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">Time</p>
                <p className="text-gray-600">{booking.scheduledTime}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg border border-green-200">
              <DollarSign className="w-6 h-6 text-green-600" />
              <div>
                <p className="font-medium text-gray-800">Amount Paid</p>
                <p className="text-2xl font-bold text-green-600">${amount.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">What's Next?</h2>
          
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">Confirmation Email</h3>
                <p className="text-gray-600 text-sm">You'll receive a confirmation email with all the details.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">Provider Contact</h3>
                <p className="text-gray-600 text-sm">{booking.providerName} may contact you to confirm details.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">Enjoy the Service</h3>
                <p className="text-gray-600 text-sm">Show up on time and enjoy the professional pet care!</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            href="/dashboard"
            className="bg-blue-600 text-white py-3 px-6 rounded-xl font-semibold text-center hover:bg-blue-700 transition-colors"
          >
            Go to Dashboard
          </Link>
          
          <Link
            href={`/chat/${booking.providerId}`}
            className="bg-green-600 text-white py-3 px-6 rounded-xl font-semibold text-center hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
          >
            <MessageCircle className="w-5 h-5" />
            Message Provider
          </Link>
          
          <button
            onClick={() => {
              // TODO: Implement calendar integration
              const event = {
                title: `${booking.serviceName} for ${booking.petName}`,
                start: new Date(`${booking.scheduledDate} ${booking.scheduledTime}`),
                description: `Pet service with ${booking.providerName}`
              };
              
              // For now, just show a message
              alert('Calendar integration coming soon! Please add this appointment to your calendar manually.');
            }}
            className="bg-purple-600 text-white py-3 px-6 rounded-xl font-semibold text-center hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
          >
            <Calendar className="w-5 h-5" />
            Add to Calendar
          </button>
        </div>

        {/* Review Prompt */}
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 mt-6 border border-yellow-200">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-800">Leave a Review</h3>
              <p className="text-gray-600 text-sm">After your service, please consider leaving a review to help other pet owners.</p>
            </div>
            <button className="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors text-sm font-medium">
              Remind Me Later
            </button>
          </div>
        </div>

        {/* Support */}
        <div className="text-center mt-8">
          <p className="text-gray-600 text-sm">
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
