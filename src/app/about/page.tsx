'use client';

import { Users, Shield, Award, Target, Zap } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';



const values = [
  {
    icon: () => (
      <Image
        src="/fetchlylogo.png"
        alt="Fetchly Logo"
        width={32}
        height={32}
        className="w-8 h-8"
      />
    ),
    title: "Pet-First Approach",
    description: "Every decision we make prioritizes the health, safety, and happiness of pets"
  },
  {
    icon: Shield,
    title: "Trust & Safety",
    description: "Rigorous vetting processes and insurance coverage for complete peace of mind"
  },
  {
    icon: Users,
    title: "Community Focus",
    description: "Building connections between pet owners and trusted service providers"
  },
  {
    icon: Award,
    title: "Quality Excellence",
    description: "Maintaining the highest standards in all services and partnerships"
  }
];



export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 pt-20">
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-blue-100/20 to-cyan-100/30"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl animate-bounce"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-black text-gray-800 mb-6">
              About <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Fetchly</span>
            </h1>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              We're on a mission to make pet care accessible, reliable, and stress-free for every pet parent
            </p>
          </div>

          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto text-center">
            <h2 className="text-2xl font-bold text-cool-800 mb-4">Our Story</h2>
            <p className="text-cool-700 text-lg leading-relaxed">
              Fetchly was founded by pet lovers who experienced firsthand the challenges of finding trustworthy, 
              quality pet care services. We believe every pet deserves the best care possible, and every pet parent 
              deserves peace of mind. Our platform connects you with verified, professional pet care providers 
              in your area, making it easier than ever to give your furry family members the care they need.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <div className="glass-card rounded-2xl p-8">
              <div className="flex items-center gap-3 mb-6">
                <Target className="w-8 h-8 text-primary-500" />
                <h2 className="text-3xl font-bold text-cool-800">Our Mission</h2>
              </div>
              <p className="text-cool-700 text-lg leading-relaxed">
                To revolutionize pet care by creating a trusted ecosystem where pet owners can easily find, 
                book, and manage all their pet's needs through one comprehensive platform. We're committed 
                to ensuring every pet receives professional, loving care while giving owners complete peace of mind.
              </p>
            </div>

            <div className="glass-card rounded-2xl p-8">
              <div className="flex items-center gap-3 mb-6">
                <Zap className="w-8 h-8 text-secondary-500" />
                <h2 className="text-3xl font-bold text-cool-800">Our Vision</h2>
              </div>
              <p className="text-cool-700 text-lg leading-relaxed">
                To become the world's most trusted pet care platform, where every pet owner has access to 
                high-quality, professional services in their community. We envision a future where pet care 
                is seamless, transparent, and tailored to each pet's unique needs.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-700 font-medium">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <div key={index} className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl p-8 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group border border-white/30">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-800">{value.title}</h3>
                  <p className="text-gray-600 font-medium">{value.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-12 max-w-4xl mx-auto text-center relative overflow-hidden border border-white/30">
            <div className="absolute inset-0 bg-gradient-to-r from-green-100/30 via-blue-100/20 to-cyan-100/30"></div>
            <div className="relative z-10">
              <h2 className="text-4xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
                Join the Fetchly Family
              </h2>
              <p className="text-xl text-gray-700 mb-8 font-medium">
                Whether you're a pet parent or service provider, we'd love to have you as part of our community
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/signup" className="btn-primary">
                  Get Started as Pet Owner
                </Link>
                <Link href="/providers" className="btn-secondary">
                  Join as Service Provider
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
