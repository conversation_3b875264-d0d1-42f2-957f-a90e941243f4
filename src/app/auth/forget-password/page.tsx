'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ForgetPasswordRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the correct spelling
    router.replace('/auth/forgot-password');
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
      <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md text-center border border-white/30">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-800">Redirecting...</h2>
        <p className="text-gray-600 mt-2">Taking you to the password reset page.</p>
      </div>
    </div>
  );
}
