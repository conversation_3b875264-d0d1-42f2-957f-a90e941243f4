export interface ChatUser {
  uid: string;
  displayName: string;
  userType: 'pet_owner' | 'provider' | 'support' | 'admin';
  avatarUrl?: string;
  email?: string;
  isOnline?: boolean;
  lastSeen?: Date;
}

export interface Chat {
  id: string;
  participantIds: string[]; // Array of user UIDs
  participantDetails: ChatUser[]; // Full user details for easy access
  lastMessage: string;
  lastMessageSender: string;
  lastMessageTime: Date;
  createdAt: Date;
  updatedAt: Date;
  isSupportChat: boolean;
  isGroupChat: boolean;
  chatName?: string; // For group chats
  chatAvatar?: string; // For group chats
  unreadCount: { [uid: string]: number }; // Unread count per user
  typingUsers: string[]; // UIDs of users currently typing
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  text?: string;
  mediaUrl?: string;
  mediaType?: 'image' | 'file' | 'video';
  fileName?: string;
  fileSize?: number;
  timestamp: Date;
  readBy: string[]; // Array of UIDs who have read this message
  isSystemMessage?: boolean;
  replyTo?: string; // Message ID being replied to
  reactions?: { [emoji: string]: string[] }; // emoji -> array of user UIDs
  isDeleted?: boolean;
  editedAt?: Date;
}

export interface TypingIndicator {
  chatId: string;
  userId: string;
  userName: string;
  timestamp: Date;
}

export interface ChatNotification {
  id: string;
  userId: string;
  chatId: string;
  messageId: string;
  type: 'new_message' | 'new_chat' | 'mention';
  title: string;
  body: string;
  isRead: boolean;
  createdAt: Date;
}

// For content moderation
export interface ContentFlag {
  id: string;
  userId: string;
  content: string;
  contentType: 'message' | 'post' | 'comment';
  contentId: string;
  flaggedWords: string[];
  flagCount: number;
  timestamp: Date;
  action: 'warning' | 'deleted' | 'suspended';
}

export interface UserSuspension {
  id: string;
  userId: string;
  reason: string;
  suspendedAt: Date;
  suspendedUntil: Date;
  flagCount: number;
  isActive: boolean;
}

// Chat creation types
export interface CreateChatRequest {
  participantIds: string[];
  isGroupChat?: boolean;
  chatName?: string;
  initialMessage?: string;
}

export interface SendMessageRequest {
  chatId: string;
  text?: string;
  mediaFile?: File;
  replyToMessageId?: string;
}

// Chat store state
export interface ChatState {
  chats: Chat[];
  activeChat: Chat | null;
  messages: { [chatId: string]: Message[] };
  isLoading: boolean;
  isTyping: { [chatId: string]: string[] };
  unreadCounts: { [chatId: string]: number };
  notifications: ChatNotification[];
  onlineUsers: string[];
}

// Support chat types
export interface SupportTicket {
  id: string;
  userId: string;
  chatId: string;
  subject: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  category: 'technical' | 'billing' | 'general' | 'complaint';
  createdAt: Date;
  resolvedAt?: Date;
  assignedTo?: string; // Support agent UID
}

export interface SupportAgent {
  uid: string;
  name: string;
  email: string;
  isOnline: boolean;
  activeChats: string[];
  maxChats: number;
  specialties: string[];
}
