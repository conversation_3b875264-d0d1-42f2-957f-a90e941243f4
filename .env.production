# Production Environment Variables for Fetchly
# These will be set in Heroku Config Vars

# App Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://fetchlyapp-6d7e81a51327.herokuapp.com
NEXT_PUBLIC_API_URL=https://fetchlyapp-6d7e81a51327.herokuapp.com/api
NEXTAUTH_URL=https://fetchlyapp-6d7e81a51327.herokuapp.com

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC5Fy9h_pQbrJCDCUfyR25_Deswb50HJwo
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=fetchly-724b6.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=fetchly-724b6
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=fetchly-724b6.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=192530321990
NEXT_PUBLIC_FIREBASE_APP_ID=1:192530321990:web:c1bb50473a7e1060a77047
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-EJ47KTH83Z

# Firebase Admin SDK
FIREBASE_ADMIN_PROJECT_ID=fetchly-724b6
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Stripe Configuration
STRIPE_SECRET_KEY=***********************************************************************************************************
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51OEvzDAGmZKDxTSzIcokuaySFNhAhSIfpp7Kqqwo17SE6HNztMebCxeX0jqSrLvt96Yp72GtRfcva1GKhC4vrxvP00SrWUgCub
STRIPE_WEBHOOK_SECRET=whsec_e654f95edec840061144f483f1f780cc85dc86a5af77e5dd3bed51c83cd0f448

# PLAID Configuration (Set these in Heroku Config Vars)
PLAID_CLIENT_ID=
PLAID_SECRET=
PLAID_ENV=production

# Email Configuration (Set these in Heroku Config Vars)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=

# JWT Secret (Set this in Heroku Config Vars)
JWT_SECRET=

# Database URLs (if using external databases)
DATABASE_URL=
REDIS_URL=

# API Keys (Set these in Heroku Config Vars)
GOOGLE_MAPS_API_KEY=
SENDGRID_API_KEY=
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=P0l@r!$2025

# Monitoring and Analytics
SENTRY_DSN=
GOOGLE_ANALYTICS_ID=

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_PAYMENTS=true
ENABLE_CHAT=true
ENABLE_STORIES=true
