# 📧 Fetchly Email Notification Setup Guide

## Overview
This guide will help you set up email notifications for your Fetchly platform using your GoDaddy email (<EMAIL>) with Outlook 365.

## 🎯 What You'll Receive Notifications For
- ✅ New user signups (both providers and pet owners)
- ✅ New purchases and payments
- ✅ New bookings
- ✅ Provider registrations
- ✅ Platform errors and issues
- ✅ General platform activities

## 🔧 Setup Options

### Option 1: Nodemailer with Outlook 365 (Recommended for GoDaddy)

#### Step 1: Install Required Packages
```bash
npm install nodemailer @types/nodemailer
```

#### Step 2: Environment Variables
Add these to your `.env.local` file:
```env
# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password_here
EMAIL_HOST=smtp.office365.com
EMAIL_PORT=587
```

#### Step 3: Get Your Email Password
1. Log into your GoDaddy account
2. Go to Email & Office → Manage
3. If using Outlook 365, you may need to create an "App Password":
   - Go to Microsoft 365 admin center
   - Security → Authentication methods → App passwords
   - Create a new app password for "Fetchly Platform"
   - Use this app password instead of your regular password

#### Step 4: Update Email Service
Uncomment the Nodemailer section in `src/lib/services/email-service.ts`:

```typescript
// Uncomment this method in email-service.ts
private async sendWithNodemailer(emailData: EmailNotification): Promise<void> {
  const nodemailer = require('nodemailer');

  const transporter = nodemailer.createTransporter({
    host: process.env.EMAIL_HOST || 'smtp.office365.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  });

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: this.adminEmail,
    subject: emailData.subject,
    html: this.formatEmailHTML(emailData),
  };

  await transporter.sendMail(mailOptions);
}
```

Then update the `sendNotification` method to use Nodemailer:
```typescript
// Replace the TODO comment with:
await this.sendWithNodemailer(emailData);
```

### Option 2: SendGrid (Alternative - More Reliable)

#### Step 1: Create SendGrid Account
1. Go to https://sendgrid.com
2. Create a free account (100 emails/day free)
3. Verify your sender email (<EMAIL>)

#### Step 2: Get API Key
1. Go to Settings → API Keys
2. Create a new API key with "Full Access"
3. Copy the API key

#### Step 3: Environment Variables
```env
SENDGRID_API_KEY=your_sendgrid_api_key_here
```

#### Step 4: Install SendGrid
```bash
npm install @sendgrid/mail
```

#### Step 5: Update Email Service
Uncomment the SendGrid section in `src/lib/services/email-service.ts`

### Option 3: AWS SES (Enterprise Option)

For high-volume email sending, consider AWS Simple Email Service.

## 🚀 Testing Your Setup

### Test Email Function
Add this to your email service for testing:

```typescript
// Add this method to EmailNotificationService class
async testEmail(): Promise<void> {
  await this.notifyGeneral('🧪 Test Email from Fetchly', {
    message: 'If you receive this, your email setup is working correctly!',
    timestamp: new Date(),
    platform: 'Fetchly',
    environment: process.env.NODE_ENV
  });
}
```

### Test in Development
```typescript
// In your component or API route
import { emailNotificationService } from '@/lib/services/email-service';

// Test the email
await emailNotificationService.testEmail();
```

## 📋 Current Integration Points

The email service is already integrated into:

### 1. User Registration
- Location: `src/contexts/AuthContext.tsx`
- Triggers: When new users sign up
- Data: User name, email, role, timestamp

### 2. Ready for Integration
The service is ready to be integrated into:
- Payment processing
- Booking system
- Provider registration
- Error handling

## 🔍 Monitoring & Debugging

### Check Email Logs
All email attempts are logged to:
- Console (development)
- Firestore collection: `email_notifications`
- Failed emails: `failed_notifications`

### Troubleshooting Common Issues

#### 1. Authentication Failed
- Verify email/password in environment variables
- Check if 2FA is enabled (use app password)
- Ensure SMTP settings are correct

#### 2. Emails Not Sending
- Check console logs for errors
- Verify Firestore collections for failed notifications
- Test with a simple email first

#### 3. GoDaddy/Outlook 365 Issues
- Enable "Less secure app access" if needed
- Use app-specific password instead of main password
- Check if account is locked or suspended

## 🔐 Security Best Practices

1. **Never commit email passwords to git**
2. **Use app passwords instead of main passwords**
3. **Rotate passwords regularly**
4. **Monitor failed login attempts**
5. **Use environment variables for all credentials**

## 📊 Email Templates

The service includes beautiful HTML email templates with:
- Fetchly branding (green to blue gradients)
- Professional formatting
- Responsive design
- Clear data presentation

## 🎯 Next Steps

1. Choose your email service (Nodemailer recommended for GoDaddy)
2. Set up environment variables
3. Test with the test email function
4. Integrate into payment and booking systems
5. Monitor email delivery and logs

## 📞 Support

If you need help with setup:
1. Check the console logs for specific errors
2. Verify your GoDaddy email settings
3. Test with a simple email client first
4. Consider using SendGrid for better reliability

## 🚀 Production Deployment

Before going live:
1. Test all email types
2. Set up email monitoring
3. Configure retry logic for failed emails
4. Set up email delivery tracking
5. Monitor spam folder delivery

---

**Note**: The email service is already integrated into user registration. Once you configure your email provider, you'll start receiving signup notifications immediately!
