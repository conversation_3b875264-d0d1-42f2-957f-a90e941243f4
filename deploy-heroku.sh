#!/bin/bash

echo "🚀 Setting up Heroku environment variables for Fetchly..."

# Set Node.js version
heroku config:set NODE_ENV=production --app fetchly-production

# JWT Configuration
heroku config:set JWT_SECRET="your_super_secret_jwt_key_change_this_in_production_2024_fetchly" --app fetchly-production
heroku config:set REFRESH_TOKEN_SECRET="your_super_secret_refresh_token_key_change_this_in_production_2024_fetchly" --app fetchly-production
heroku config:set JWT_EXPIRES_IN="7d" --app fetchly-production

# Security Configuration
heroku config:set BCRYPT_ROUNDS=12 --app fetchly-production
heroku config:set RATE_LIMIT_WINDOW_MS=900000 --app fetchly-production
heroku config:set RATE_LIMIT_MAX_REQUESTS=100 --app fetchly-production

# Stripe Configuration (Production)
heroku config:set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_51OEvzDAGmZKDxTSzIcokuaySFNhAhSIfpp7Kqqwo17SE6HNztMebCxeX0jqSrLvt96Yp72GtRfcva1GKhC4vrxvP00SrWUgCub" --app fetchly-production
heroku config:set STRIPE_SECRET_KEY="***********************************************************************************************************" --app fetchly-production
heroku config:set STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret_here" --app fetchly-production

# App URLs
heroku config:set NEXT_PUBLIC_APP_URL="https://fetchly-production-6a7f4a64729e.herokuapp.com" --app fetchly-production
heroku config:set NEXT_PUBLIC_DOMAIN="fetchlypr.com" --app fetchly-production
heroku config:set NEXT_PUBLIC_PRODUCTION_URL="https://fetchlypr.com" --app fetchly-production

# Firebase Admin SDK
heroku config:set FIREBASE_ADMIN_PROJECT_ID="fetchly-724b6" --app fetchly-production
heroku config:set FIREBASE_ADMIN_CLIENT_EMAIL="<EMAIL>" --app fetchly-production
heroku config:set FIREBASE_ADMIN_PRIVATE_KEY="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" --app fetchly-production

# Firebase Client Configuration
heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyC5Fy9h_pQbrJCDCUfyR25_Deswb50HJwo" --app fetchly-production
heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="fetchly-724b6.firebaseapp.com" --app fetchly-production
heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID="fetchly-724b6" --app fetchly-production
heroku config:set NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="fetchly-724b6.firebasestorage.app" --app fetchly-production
heroku config:set NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="192530321990" --app fetchly-production
heroku config:set NEXT_PUBLIC_FIREBASE_APP_ID="1:192530321990:web:c1bb50473a7e1060a77047" --app fetchly-production
heroku config:set NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID="G-EJ47KTH83Z" --app fetchly-production

# Plaid Configuration (you'll need to update these with real values)
heroku config:set PLAID_CLIENT_ID="your_plaid_client_id_here" --app fetchly-production
heroku config:set PLAID_SECRET="your_plaid_secret_here" --app fetchly-production
heroku config:set NEXT_PUBLIC_PLAID_CLIENT_ID="your_plaid_client_id_here" --app fetchly-production
heroku config:set PLAID_ENV="production" --app fetchly-production

echo "✅ Environment variables set successfully!"
echo "🚀 Now deploying to Heroku..."

# Deploy to Heroku
git push heroku main

echo "🎉 Deployment complete!"
echo "🌐 Your app is available at: https://fetchly-production-6a7f4a64729e.herokuapp.com/"
