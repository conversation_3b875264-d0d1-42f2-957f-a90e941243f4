{"version": 3, "file": "stripe.js", "sourceRoot": "", "sources": ["../src/stripe.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AACxC,mCAA4B;AAE5B,MAAM,MAAM,GAAG,IAAI,gBAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE;IAC9D,UAAU,EAAE,YAAY;CACzB,CAAC,CAAC;AAEH,iDAAiD;AACjD,KAAK,UAAU,kBAAkB,CAAC,MAAc;;IAC9C,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;IAC9E,OAAO,CAAA,MAAA,OAAO,CAAC,IAAI,EAAE,0CAAE,eAAe,KAAI,IAAI,CAAC;AACjD,CAAC;AAED,8DAA8D;AACjD,QAAA,sBAAsB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IACnF,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,wBAAwB,CAAC,CAAC;IACpF,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAChC,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACnE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,wCAAwC,CAAC,CAAC;IAC7F,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kDAAkD;AACrC,QAAA,gBAAgB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;;IAC7E,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,wBAAwB,CAAC,CAAC;IACpF,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAChC,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC5C,aAAa,EAAE,SAAS;SACzB,CAAC,CAAC;QAEH,OAAO;YACL,SAAS,EAAE,CAAA,MAAA,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,0CAAE,MAAM,KAAI,CAAC;YAC5C,OAAO,EAAE,CAAA,MAAA,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,MAAM,KAAI,CAAC;YACxC,QAAQ,EAAE,CAAA,MAAA,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,0CAAE,QAAQ,KAAI,KAAK;SAClD,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,gCAAgC,CAAC,CAAC;IACrF,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACZ,QAAA,mBAAmB,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9E,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAW,CAAC;IACtD,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC;IAEhE,IAAI,KAAmB,CAAC;IAExB,IAAI,CAAC;QACH,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CACpC,GAAG,CAAC,OAAO,EACX,GAAG,EACH,cAAc,CACf,CAAC;IACJ,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,0CAA0C,GAAG,EAAE,CAAC,CAAC;QAC/D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC;QAC9C,OAAO;IACT,CAAC;IAED,mBAAmB;IACnB,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,iBAAiB;YACpB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAwB,CAAC;YACpD,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM;QACR,iCAAiC;QACjC;YACE,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,4DAA4D;IAC5D,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC;AAEH,KAAK,UAAU,oBAAoB,CAAC,OAAuB;IACzD,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;IACvC,IAAI,CAAC,MAAM;QAAE,OAAO;IAEpB,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAElE,MAAM,OAAO,CAAC,MAAM,CAAC;QACnB,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;QAC5C,mBAAmB,EAAE,iDAAiD,OAAO,CAAC,EAAE,EAAE;QAClF,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;KACxD,CAAC,CAAC;AACL,CAAC"}