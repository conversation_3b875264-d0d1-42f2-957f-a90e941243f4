"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processEmailNotifications = exports.sendEmailVerification = exports.sendPasswordResetEmail = exports.sendCustomAuthEmails = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}
/**
 * Firebase Auth trigger for sending custom email templates
 */
exports.sendCustomAuthEmails = functions.auth.user().onCreate(async (user) => {
    const { email, displayName, uid } = user;
    if (!email) {
        console.log('User has no email, skipping welcome email');
        return;
    }
    try {
        // Get user document to determine role
        const userDoc = await admin.firestore().collection('users').doc(uid).get();
        const userData = userDoc.data();
        const isProvider = (userData === null || userData === void 0 ? void 0 : userData.role) === 'provider';
        // Send welcome email notification to admin
        await admin.firestore().collection('email_notifications').add({
            type: 'signup',
            subject: `🎉 New ${isProvider ? 'Provider' : 'Pet Owner'} Signup - ${displayName || 'Unknown'}`,
            data: {
                userId: uid,
                email: email,
                name: displayName || 'Unknown',
                role: isProvider ? 'provider' : 'petowner',
                timestamp: admin.firestore.FieldValue.serverTimestamp()
            },
            status: 'pending',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log(`✅ Welcome email notification queued for ${email}`);
    }
    catch (error) {
        console.error('Error sending welcome email:', error);
    }
});
/**
 * HTTP function to handle password reset emails
 */
exports.sendPasswordResetEmail = functions.https.onCall(async (data, context) => {
    const { email } = data;
    if (!email) {
        throw new functions.https.HttpsError('invalid-argument', 'Email is required');
    }
    try {
        // Generate password reset link
        const link = await admin.auth().generatePasswordResetLink(email, {
            url: 'https://fetchlyapp.herokuapp.com/auth/signin',
            handleCodeInApp: false,
        });
        // Store email notification
        await admin.firestore().collection('email_notifications').add({
            type: 'password_reset',
            subject: '🔐 Reset Your Fetchly Password',
            data: {
                email: email,
                resetLink: link,
                timestamp: admin.firestore.FieldValue.serverTimestamp()
            },
            status: 'pending',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
        return { success: true, message: 'Password reset email queued' };
    }
    catch (error) {
        console.error('Error generating password reset link:', error);
        throw new functions.https.HttpsError('internal', 'Failed to send password reset email');
    }
});
/**
 * HTTP function to handle email verification
 */
exports.sendEmailVerification = functions.https.onCall(async (data, context) => {
    const { email } = data;
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    try {
        // Generate email verification link
        const link = await admin.auth().generateEmailVerificationLink(email, {
            url: 'https://fetchlyapp.herokuapp.com/auth/signin',
            handleCodeInApp: false,
        });
        // Store email notification
        await admin.firestore().collection('email_notifications').add({
            type: 'email_verification',
            subject: '✅ Verify Your Fetchly Email',
            data: {
                email: email,
                verificationLink: link,
                timestamp: admin.firestore.FieldValue.serverTimestamp()
            },
            status: 'pending',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
        return { success: true, message: 'Email verification queued' };
    }
    catch (error) {
        console.error('Error generating email verification link:', error);
        throw new functions.https.HttpsError('internal', 'Failed to send email verification');
    }
});
/**
 * Firestore trigger to process email notifications
 */
exports.processEmailNotifications = functions.firestore
    .document('email_notifications/{notificationId}')
    .onCreate(async (snap, context) => {
    const notification = snap.data();
    if (notification.status !== 'pending') {
        return;
    }
    try {
        // Here you would integrate with your email service
        // For now, we'll just log and mark as processed
        console.log('Processing email notification:', notification);
        // Update status to processed
        await snap.ref.update({
            status: 'processed',
            processedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log(`✅ Email notification processed: ${notification.subject}`);
    }
    catch (error) {
        console.error('Error processing email notification:', error);
        // Mark as failed
        await snap.ref.update({
            status: 'failed',
            error: error.message,
            failedAt: admin.firestore.FieldValue.serverTimestamp()
        });
    }
});
//# sourceMappingURL=email-templates.js.map