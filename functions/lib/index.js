"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheck = exports.handleStripeWebhook = exports.getStripeBalance = exports.getStripeDashboardLink = exports.sendMFANotification = exports.sendEmailChangeNotification = exports.sendCustomEmailVerification = exports.sendCustomPasswordReset = exports.processEmailNotifications = exports.sendEmailVerification = exports.sendPasswordResetEmail = exports.sendCustomAuthEmails = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
// Initialize Firebase Admin
admin.initializeApp();
// Export email template functions
var email_templates_1 = require("./email-templates");
Object.defineProperty(exports, "sendCustomAuthEmails", { enumerable: true, get: function () { return email_templates_1.sendCustomAuthEmails; } });
Object.defineProperty(exports, "sendPasswordResetEmail", { enumerable: true, get: function () { return email_templates_1.sendPasswordResetEmail; } });
Object.defineProperty(exports, "sendEmailVerification", { enumerable: true, get: function () { return email_templates_1.sendEmailVerification; } });
Object.defineProperty(exports, "processEmailNotifications", { enumerable: true, get: function () { return email_templates_1.processEmailNotifications; } });
// Export custom professional email handlers
var custom_email_handlers_1 = require("./custom-email-handlers");
Object.defineProperty(exports, "sendCustomPasswordReset", { enumerable: true, get: function () { return custom_email_handlers_1.sendCustomPasswordReset; } });
Object.defineProperty(exports, "sendCustomEmailVerification", { enumerable: true, get: function () { return custom_email_handlers_1.sendCustomEmailVerification; } });
Object.defineProperty(exports, "sendEmailChangeNotification", { enumerable: true, get: function () { return custom_email_handlers_1.sendEmailChangeNotification; } });
Object.defineProperty(exports, "sendMFANotification", { enumerable: true, get: function () { return custom_email_handlers_1.sendMFANotification; } });
// Export Stripe functions
var stripe_1 = require("./stripe");
Object.defineProperty(exports, "getStripeDashboardLink", { enumerable: true, get: function () { return stripe_1.getStripeDashboardLink; } });
Object.defineProperty(exports, "getStripeBalance", { enumerable: true, get: function () { return stripe_1.getStripeBalance; } });
Object.defineProperty(exports, "handleStripeWebhook", { enumerable: true, get: function () { return stripe_1.handleStripeWebhook; } });
// Health check function
exports.healthCheck = functions.https.onRequest((req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        message: 'Fetchly Firebase Functions are running'
    });
});
//# sourceMappingURL=index.js.map