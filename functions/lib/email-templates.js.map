{"version": 3, "file": "email-templates.js", "sourceRoot": "", "sources": ["../src/email-templates.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AAExC,uDAAuD;AACvD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,KAAK,CAAC,aAAa,EAAE,CAAC;AACxB,CAAC;AAED;;GAEG;AACU,QAAA,oBAAoB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IAChF,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAEzC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,sCAAsC;QACtC,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAC3E,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,UAAU,CAAC;QAEjD,2CAA2C;QAC3C,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC;YAC5D,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,aAAa,WAAW,IAAI,SAAS,EAAE;YAC/F,IAAI,EAAE;gBACJ,MAAM,EAAE,GAAG;gBACX,KAAK,EAAE,KAAK;gBACZ,IAAI,EAAE,WAAW,IAAI,SAAS;gBAC9B,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU;gBAC1C,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;aACxD;YACD,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;IAClE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IACnF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;IAEvB,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IAChF,CAAC;IAED,IAAI,CAAC;QACH,+BAA+B;QAC/B,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,KAAK,EAAE;YAC/D,GAAG,EAAE,8CAA8C;YACnD,eAAe,EAAE,KAAK;SACvB,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC;YAC5D,IAAI,EAAE,gBAAgB;YACtB,OAAO,EAAE,gCAAgC;YACzC,IAAI,EAAE;gBACJ,KAAK,EAAE,KAAK;gBACZ,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;aACxD;YACD,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,qCAAqC,CAAC,CAAC;IAC1F,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAClF,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;IAEvB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;IACxF,CAAC;IAED,IAAI,CAAC;QACH,mCAAmC;QACnC,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,6BAA6B,CAAC,KAAK,EAAE;YACnE,GAAG,EAAE,8CAA8C;YACnD,eAAe,EAAE,KAAK;SACvB,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC;YAC5D,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE,6BAA6B;YACtC,IAAI,EAAE;gBACJ,KAAK,EAAE,KAAK;gBACZ,gBAAgB,EAAE,IAAI;gBACtB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;aACxD;YACD,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;IACjE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAClE,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,mCAAmC,CAAC,CAAC;IACxF,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,yBAAyB,GAAG,SAAS,CAAC,SAAS;KACzD,QAAQ,CAAC,sCAAsC,CAAC;KAChD,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAChC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IAEjC,IAAI,YAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACtC,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,mDAAmD;QACnD,gDAAgD;QAChD,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,YAAY,CAAC,CAAC;QAE5D,6BAA6B;QAC7B,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YACpB,MAAM,EAAE,WAAW;YACnB,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SAC1D,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,mCAAmC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;IACzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAE7D,iBAAiB;QACjB,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YACpB,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACvD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC"}