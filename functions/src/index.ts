import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

// Export email template functions
export {
  sendCustomAuthEmails,
  sendPasswordResetEmail,
  sendEmailVerification,
  processEmailNotifications
} from './email-templates';

// Export custom professional email handlers
export {
  sendCustomPasswordReset,
  sendCustomEmailVerification,
  sendEmailChangeNotification,
  sendMFANotification
} from './custom-email-handlers';

// Export Stripe functions
export {
  getStripeDashboardLink,
  getStripeBalance,
  handleStripeWebhook
} from './stripe';

// Health check function
export const healthCheck = functions.https.onRequest((req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Fetchly Firebase Functions are running'
  });
});
