import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as nodemailer from 'nodemailer';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Email transporter configuration
const createTransporter = () => {
  return nodemailer.createTransport({
    host: functions.config().email?.host || 'smtp.office365.com',
    port: parseInt(functions.config().email?.port || '587'),
    secure: false,
    auth: {
      user: functions.config().email?.user,
      pass: functions.config().email?.password,
    },
    tls: {
      ciphers: 'SSLv3'
    }
  });
};

/**
 * Custom Password Reset Email Handler
 * Overrides Firebase's default password reset email
 */
export const sendCustomPasswordReset = functions.https.onCall(async (data, context) => {
  const { email } = data;
  
  if (!email) {
    throw new functions.https.HttpsError('invalid-argument', 'Email is required');
  }

  try {
    // Generate custom password reset link
    const resetLink = await admin.auth().generatePasswordResetLink(email, {
      url: 'https://fetchlypr.com/auth/signin',
      handleCodeInApp: false,
    });

    const transporter = createTransporter();
    
    const mailOptions = {
      from: `"Fetchly Support" <${functions.config().email?.user}>`,
      to: email,
      subject: 'Reset Your Fetchly Password',
      html: generatePasswordResetHTML(resetLink)
    };

    await transporter.sendMail(mailOptions);
    
    return { success: true, message: 'Password reset email sent successfully' };
  } catch (error) {
    console.error('Error sending custom password reset:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send password reset email');
  }
});

/**
 * Custom Email Verification Handler
 * Overrides Firebase's default email verification
 */
export const sendCustomEmailVerification = functions.https.onCall(async (data, context) => {
  const { email, displayName } = data;
  
  if (!email) {
    throw new functions.https.HttpsError('invalid-argument', 'Email is required');
  }

  try {
    // Generate custom email verification link
    const verificationLink = await admin.auth().generateEmailVerificationLink(email, {
      url: 'https://fetchlypr.com/auth/signin',
      handleCodeInApp: false,
    });

    const transporter = createTransporter();
    
    const mailOptions = {
      from: `"Fetchly Support" <${functions.config().email?.user}>`,
      to: email,
      subject: 'Verify Your Fetchly Email Address',
      html: generateEmailVerificationHTML(verificationLink, displayName || 'User')
    };

    await transporter.sendMail(mailOptions);
    
    return { success: true, message: 'Email verification sent successfully' };
  } catch (error) {
    console.error('Error sending custom email verification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send email verification');
  }
});

/**
 * Custom Email Change Notification
 */
export const sendEmailChangeNotification = functions.https.onCall(async (data, context) => {
  const { oldEmail, newEmail, displayName } = data;
  
  if (!oldEmail || !newEmail) {
    throw new functions.https.HttpsError('invalid-argument', 'Both old and new email are required');
  }

  try {
    const transporter = createTransporter();
    
    // Send notification to old email
    const oldEmailOptions = {
      from: `"Fetchly Security" <${functions.config().email?.user}>`,
      to: oldEmail,
      subject: 'Your Fetchly Email Address Has Been Changed',
      html: generateEmailChangeNotificationHTML(displayName || 'User', newEmail, 'old')
    };

    // Send confirmation to new email
    const newEmailOptions = {
      from: `"Fetchly Security" <${functions.config().email?.user}>`,
      to: newEmail,
      subject: 'Your Fetchly Email Address Has Been Updated',
      html: generateEmailChangeNotificationHTML(displayName || 'User', newEmail, 'new')
    };

    await Promise.all([
      transporter.sendMail(oldEmailOptions),
      transporter.sendMail(newEmailOptions)
    ]);
    
    return { success: true, message: 'Email change notifications sent successfully' };
  } catch (error) {
    console.error('Error sending email change notifications:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send email change notifications');
  }
});

/**
 * Multi-Factor Authentication Setup Notification
 */
export const sendMFANotification = functions.https.onCall(async (data, context) => {
  const { email, displayName, action } = data; // action: 'enabled' | 'disabled'
  
  if (!email || !action) {
    throw new functions.https.HttpsError('invalid-argument', 'Email and action are required');
  }

  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: `"Fetchly Security" <${functions.config().email?.user}>`,
      to: email,
      subject: `Two-Factor Authentication ${action === 'enabled' ? 'Enabled' : 'Disabled'} on Your Fetchly Account`,
      html: generateMFANotificationHTML(displayName || 'User', action)
    };

    await transporter.sendMail(mailOptions);
    
    return { success: true, message: 'MFA notification sent successfully' };
  } catch (error) {
    console.error('Error sending MFA notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send MFA notification');
  }
});

// HTML Template Functions
function generatePasswordResetHTML(resetLink: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Fetchly Password</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">Reset Your Password</h1>
          <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 16px;">Secure access to your Fetchly account</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
          <h2 style="color: #1e293b; margin: 0 0 20px 0; font-size: 24px;">Hello!</h2>
          
          <p style="color: #475569; line-height: 1.6; margin: 0 0 25px 0; font-size: 16px;">
            We received a request to reset the password for your Fetchly account. Click the button below to create a new password:
          </p>
          
          <!-- Reset Button -->
          <div style="text-align: center; margin: 35px 0;">
            <a href="${resetLink}" style="background: linear-gradient(135deg, #10b981, #3b82f6); color: #ffffff; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);">
              Reset My Password
            </a>
          </div>
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0;">
            This link will expire in 1 hour for security reasons. If you didn't request this password reset, you can safely ignore this email.
          </p>
          
          <!-- Security Notice -->
          <div style="background: #fef3c7; border: 1px solid #fcd34d; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #92400e; margin: 0 0 10px 0; font-size: 16px;">🛡️ Security Reminder</h3>
            <p style="color: #92400e; margin: 0; font-size: 14px; line-height: 1.5;">
              For your security, never share your password with anyone. Fetchly will never ask for your password via email or phone.
            </p>
          </div>
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0 0 0;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${resetLink}" style="color: #3b82f6; word-break: break-all;">${resetLink}</a>
          </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">
            Need help? Contact our support team at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
          <p style="color: #94a3b8; margin: 0; font-size: 12px;">
            © 2025 Fetchly. All rights reserved.<br>
            This email was sent from a secure Fetchly server.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function generateEmailVerificationHTML(verificationLink: string, displayName: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Fetchly Email</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">Verify Your Email</h1>
          <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 16px;">Complete your Fetchly account setup</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
          <h2 style="color: #1e293b; margin: 0 0 20px 0; font-size: 24px;">Hello ${displayName}!</h2>
          
          <p style="color: #475569; line-height: 1.6; margin: 0 0 25px 0; font-size: 16px;">
            Welcome to Fetchly! To complete your account setup and ensure the security of your account, please verify your email address by clicking the button below:
          </p>
          
          <!-- Verification Button -->
          <div style="text-align: center; margin: 35px 0;">
            <a href="${verificationLink}" style="background: linear-gradient(135deg, #10b981, #3b82f6); color: #ffffff; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);">
              Verify My Email
            </a>
          </div>
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0;">
            This verification link will expire in 24 hours. Once verified, you'll have full access to all Fetchly features.
          </p>
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0 0 0;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${verificationLink}" style="color: #3b82f6; word-break: break-all;">${verificationLink}</a>
          </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">
            Questions? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
          <p style="color: #94a3b8; margin: 0; font-size: 12px;">
            © 2025 Fetchly. All rights reserved.<br>
            Trusted pet care services in Puerto Rico.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function generateEmailChangeNotificationHTML(displayName: string, newEmail: string, type: 'old' | 'new'): string {
  const isOldEmail = type === 'old';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Address ${isOldEmail ? 'Changed' : 'Updated'}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">Email Address ${isOldEmail ? 'Changed' : 'Updated'}</h1>
          <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 16px;">Account security notification</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
          <h2 style="color: #1e293b; margin: 0 0 20px 0; font-size: 24px;">Hello ${displayName}!</h2>
          
          <p style="color: #475569; line-height: 1.6; margin: 0 0 25px 0; font-size: 16px;">
            ${isOldEmail 
              ? `Your Fetchly account email address has been changed to <strong>${newEmail}</strong>.`
              : `Your Fetchly account email address has been successfully updated to this email address.`
            }
          </p>
          
          ${isOldEmail ? `
            <!-- Security Alert -->
            <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #dc2626; margin: 0 0 10px 0; font-size: 16px;">🚨 Security Alert</h3>
              <p style="color: #dc2626; margin: 0; font-size: 14px; line-height: 1.5;">
                If you didn't make this change, please contact our support team immediately at 
                <a href="mailto:<EMAIL>" style="color: #dc2626;"><EMAIL></a>
              </p>
            </div>
          ` : `
            <!-- Confirmation -->
            <div style="background: #dcfce7; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #166534; margin: 0 0 10px 0; font-size: 16px;">✅ Email Updated Successfully</h3>
              <p style="color: #166534; margin: 0; font-size: 14px; line-height: 1.5;">
                You can now use this email address to sign in to your Fetchly account.
              </p>
            </div>
          `}
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0 0 0;">
            This change was made on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}.
          </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">
            Need help? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
          <p style="color: #94a3b8; margin: 0; font-size: 12px;">
            © 2025 Fetchly. All rights reserved.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function generateMFANotificationHTML(displayName: string, action: string): string {
  const isEnabled = action === 'enabled';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Two-Factor Authentication ${isEnabled ? 'Enabled' : 'Disabled'}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">Two-Factor Authentication ${isEnabled ? 'Enabled' : 'Disabled'}</h1>
          <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 16px;">Account security update</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
          <h2 style="color: #1e293b; margin: 0 0 20px 0; font-size: 24px;">Hello ${displayName}!</h2>
          
          <p style="color: #475569; line-height: 1.6; margin: 0 0 25px 0; font-size: 16px;">
            Two-factor authentication has been <strong>${isEnabled ? 'enabled' : 'disabled'}</strong> on your Fetchly account.
          </p>
          
          ${isEnabled ? `
            <!-- Security Enhancement -->
            <div style="background: #dcfce7; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #166534; margin: 0 0 10px 0; font-size: 16px;">🔒 Enhanced Security</h3>
              <p style="color: #166534; margin: 0; font-size: 14px; line-height: 1.5;">
                Your account is now more secure with two-factor authentication. You'll need both your password and a verification code to sign in.
              </p>
            </div>
          ` : `
            <!-- Security Warning -->
            <div style="background: #fef3c7; border: 1px solid #fcd34d; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #92400e; margin: 0 0 10px 0; font-size: 16px;">⚠️ Security Notice</h3>
              <p style="color: #92400e; margin: 0; font-size: 14px; line-height: 1.5;">
                Two-factor authentication has been disabled. We recommend keeping it enabled for better account security.
              </p>
            </div>
          `}
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0;">
            If you didn't make this change, please contact our support team immediately.
          </p>
          
          <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 25px 0 0 0;">
            This change was made on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}.
          </p>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; margin: 0 0 10px 0; font-size: 14px;">
            Need help? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
          <p style="color: #94a3b8; margin: 0; font-size: 12px;">
            © 2025 Fetchly. All rights reserved.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}
