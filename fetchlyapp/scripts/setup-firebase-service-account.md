# Firebase Service Account Setup

To initialize the Firestore database with proper user types, you need to set up a Firebase service account.

## Steps:

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com/project/fetchly-724b6/settings/serviceaccounts/adminsdk

2. **Generate New Private Key**
   - Click "Generate new private key"
   - Download the JSON file

3. **Replace the Service Account File**
   - Replace the content of `firebase-service-account.json` with the downloaded JSON
   - Make sure the file is in the project root directory

4. **Run Database Initialization**
   ```bash
   npm run firebase:init-db
   ```

## What the initialization script does:

- ✅ Creates sample Pet Owner user with pets
- ✅ Creates sample Provider user with business profile
- ✅ Sets up Provider services and Pro subscription
- ✅ Creates reward items catalog
- ✅ Configures system settings
- ✅ Establishes proper collection structure

## Security Note:

- Never commit the `firebase-service-account.json` file to version control
- Add it to `.gitignore` if not already present
- This file contains sensitive credentials

## After Setup:

1. **Test User Roles**
   - Visit: http://localhost:3001/debug
   - Switch between Pet Owner and Provider roles
   - Test dashboard routing

2. **Verify Database Structure**
   - Check Firebase Console: https://console.firebase.google.com/project/fetchly-724b6/firestore
   - Confirm all collections are created
   - Verify sample data is present

3. **Test Authentication**
   - Sign up as both user types
   - Verify proper role assignment
   - Test dashboard redirection
