// Script to fix provider data in Firestore
// Run this in your browser console or as a Node.js script

// This script will:
// 1. Add 'status: approved' to all providers that don't have it
// 2. Create sample services for providers that don't have any

const fixProviderData = async () => {
  console.log('🔧 Starting provider data fix...');
  
  try {
    // Get all providers
    const providersRef = collection(db, 'providers');
    const providersSnapshot = await getDocs(providersRef);
    
    console.log(`📊 Found ${providersSnapshot.size} providers to check`);
    
    const batch = writeBatch(db);
    let updatedCount = 0;
    
    // Fix each provider
    for (const doc of providersSnapshot.docs) {
      const provider = doc.data();
      const providerId = doc.id;
      
      console.log(`🔍 Checking provider: ${provider.businessName || 'Unnamed'}`);
      
      // Check if provider needs status update
      if (!provider.status) {
        console.log(`✅ Adding 'approved' status to ${provider.businessName}`);
        batch.update(doc.ref, { status: 'approved' });
        updatedCount++;
      }
      
      // Check if provider has services
      const servicesRef = collection(db, 'services');
      const servicesQuery = query(servicesRef, where('providerId', '==', providerId));
      const servicesSnapshot = await getDocs(servicesQuery);
      
      if (servicesSnapshot.size === 0) {
        console.log(`📝 Creating sample service for ${provider.businessName}`);
        
        // Create a sample service based on provider's serviceType
        const sampleService = {
          providerId: providerId,
          name: provider.serviceType || 'Pet Care Service',
          description: `Professional ${provider.serviceType || 'pet care'} service`,
          category: provider.serviceType || 'Pet Sitting',
          price: 50,
          duration: 60,
          petTypes: ['dog', 'cat'],
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const serviceRef = doc(collection(db, 'services'));
        batch.set(serviceRef, sampleService);
        updatedCount++;
      }
    }
    
    // Commit all updates
    if (updatedCount > 0) {
      await batch.commit();
      console.log(`✅ Successfully updated ${updatedCount} items`);
    } else {
      console.log('ℹ️ No updates needed - all providers already have correct data');
    }
    
    console.log('🎉 Provider data fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing provider data:', error);
  }
};

// Manual fix instructions for Firebase Console
console.log(`
🔧 MANUAL FIX INSTRUCTIONS:

If you prefer to fix this manually in Firebase Console:

1. Go to Firebase Console > Firestore Database
2. Open the 'providers' collection
3. For each provider document:
   - Add field: status = "approved" (string)
   - Make sure businessName, email, serviceType fields exist

4. Go to 'services' collection (create if it doesn't exist)
5. For each provider, create a service document with:
   - providerId: [provider document ID]
   - name: "Sample Service" 
   - category: [same as provider's serviceType]
   - active: true (boolean)
   - petTypes: ["dog", "cat"] (array)
   - price: 50 (number)

Example provider document structure:
{
  businessName: "Happy Paws Pet Care",
  email: "<EMAIL>", 
  phone: "(*************",
  address: "123 Main St, Austin, TX 78701",
  city: "Austin",
  state: "TX",
  serviceType: "Pet Sitting",
  status: "approved",  // ← This is required!
  rating: 4.8,
  verified: true
}

Example service document structure:
{
  providerId: "provider-doc-id",
  name: "Pet Sitting Service",
  category: "Pet Sitting", 
  active: true,  // ← This is required!
  petTypes: ["dog", "cat"],
  price: 50,
  duration: 60
}
`);

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { fixProviderData };
}
