#!/usr/bin/env node

/**
 * Firestore Database Initialization Script
 * Sets up proper collections and data structure for Pet Owners and Providers
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = require('../firebase-service-account.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'fetchly-724b6'
});

const db = admin.firestore();

// Collection names
const COLLECTIONS = {
  USERS: 'users',
  PETS: 'pets',
  PROVIDER_PROFILES: 'providerProfiles',
  SERVICES: 'services',
  BOOKINGS: 'bookings',
  REVIEWS: 'reviews',
  POSTS: 'posts',
  BLOG_POSTS: 'blogPosts',
  PROVIDER_EARNINGS: 'providerEarnings',
  PROVIDER_CLIENTS: 'providerClients',
  PROVIDER_CALENDAR: 'providerCalendar',
  PROVIDER_SUBSCRIPTIONS: 'providerSubscriptions',
  PET_OWNER_TRANSACTIONS: 'petOwnerTransactions',
  REWARD_TRANSACTIONS: 'rewardTransactions',
  REWARD_ITEMS: 'rewardItems',
  CHAT_ROOMS: 'chatRooms',
  MESSAGES: 'messages',
  SYSTEM_SETTINGS: 'systemSettings'
};

// Sample data for Pet Owner
const samplePetOwner = {
  id: 'sample-pet-owner-123',
  email: '<EMAIL>',
  name: 'Maria Rodriguez',
  role: 'pet_owner',
  verified: true,
  joinedDate: new Date().toISOString(),
  fetchlyBalance: 75.50,
  rewardPoints: 250,
  profilePicture: '',
  bannerImage: '',
  bio: 'Loving pet parent to two amazing dogs!',
  location: 'San Juan, Puerto Rico',
  phone: '******-555-0123',
  isProfilePrivate: false,
  showEmail: false,
  showPhone: false,
  allowMessages: true,
  emailNotifications: true,
  pushNotifications: true
};

// Sample pets for the pet owner
const samplePets = [
  {
    id: 'pet-1',
    userId: 'sample-pet-owner-123',
    name: 'Luna',
    type: 'Dog',
    breed: 'Golden Retriever',
    age: 3,
    weight: 65,
    color: 'Golden',
    isActive: true,
    medicalNotes: 'Up to date on all vaccinations',
    createdAt: new Date().toISOString()
  },
  {
    id: 'pet-2',
    userId: 'sample-pet-owner-123',
    name: 'Max',
    type: 'Dog',
    breed: 'Labrador Mix',
    age: 5,
    weight: 70,
    color: 'Black',
    isActive: true,
    medicalNotes: 'Allergic to chicken',
    createdAt: new Date().toISOString()
  }
];

// Sample data for Provider
const sampleProvider = {
  id: 'sample-provider-456',
  email: '<EMAIL>',
  name: 'Dr. Carlos Mendez',
  role: 'provider',
  verified: true,
  joinedDate: new Date().toISOString(),
  fetchlyBalance: 0,
  rewardPoints: 0,
  profilePicture: '',
  bannerImage: '',
  bio: 'Licensed veterinarian with 10+ years experience',
  location: 'Bayamón, Puerto Rico',
  phone: '******-555-0456',
  isProfilePrivate: false,
  showEmail: true,
  showPhone: true,
  allowMessages: true,
  emailNotifications: true,
  pushNotifications: true
};

// Sample provider profile
const sampleProviderProfile = {
  id: 'sample-provider-456',
  userId: 'sample-provider-456',
  businessName: 'Mendez Veterinary Clinic',
  profession: 'Veterinarian',
  specialties: ['General Care', 'Surgery', 'Emergency Care'],
  yearsExperience: 10,
  rating: 4.8,
  totalReviews: 127,
  isActive: true,
  isVerified: true,
  location: {
    address: '123 Main Street',
    city: 'Bayamón',
    state: 'Puerto Rico',
    zipCode: '00961',
    coordinates: {
      lat: 18.3985,
      lng: -66.1556
    }
  },
  contact: {
    phone: '******-555-0456',
    email: '<EMAIL>',
    website: 'https://mendezveterinary.com'
  },
  businessHours: {
    monday: { open: '08:00', close: '18:00', isOpen: true },
    tuesday: { open: '08:00', close: '18:00', isOpen: true },
    wednesday: { open: '08:00', close: '18:00', isOpen: true },
    thursday: { open: '08:00', close: '18:00', isOpen: true },
    friday: { open: '08:00', close: '18:00', isOpen: true },
    saturday: { open: '09:00', close: '15:00', isOpen: true },
    sunday: { open: '10:00', close: '14:00', isOpen: false }
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Sample services
const sampleServices = [
  {
    id: 'service-1',
    providerId: 'sample-provider-456',
    name: 'General Checkup',
    description: 'Comprehensive health examination for your pet',
    category: 'Veterinary',
    price: 75.00,
    duration: 60,
    isActive: true,
    createdAt: new Date().toISOString()
  },
  {
    id: 'service-2',
    providerId: 'sample-provider-456',
    name: 'Vaccination',
    description: 'Annual vaccination package',
    category: 'Veterinary',
    price: 120.00,
    duration: 30,
    isActive: true,
    createdAt: new Date().toISOString()
  }
];

// Sample provider subscription
const sampleProviderSubscription = {
  id: 'sample-provider-456',
  providerId: 'sample-provider-456',
  tier: 'pro',
  status: 'active',
  startDate: new Date().toISOString(),
  nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
  features: {
    aiAssistant: true,
    calendarSync: true,
    customBranding: true,
    bulkMessaging: true,
    advancedAnalytics: true,
    reducedCommission: true
  },
  monthlyPrice: 20.00,
  commissionRate: 0.03
};

// Sample reward items
const sampleRewardItems = [
  {
    id: 'reward-1',
    name: 'Free Pet Grooming',
    description: 'Complimentary grooming session for your pet',
    pointsCost: 500,
    category: 'Services',
    isActive: true,
    createdAt: new Date().toISOString()
  },
  {
    id: 'reward-2',
    name: '$10 Fetchly Credit',
    description: 'Add $10 to your Fetchly wallet',
    pointsCost: 200,
    category: 'Credits',
    isActive: true,
    createdAt: new Date().toISOString()
  }
];

// System settings
const systemSettings = {
  id: 'general',
  platform: {
    name: 'Fetchly',
    version: '1.0.0',
    timezone: 'America/Puerto_Rico'
  },
  features: {
    rewardsEnabled: true,
    blogEnabled: true,
    chatEnabled: true,
    bookingEnabled: true
  },
  commission: {
    freeProviderRate: 0.05,
    proProviderRate: 0.03
  },
  wallet: {
    minimumBalance: 0,
    maximumBalance: 10000,
    chargingAmounts: [25, 50, 75, 100]
  },
  rewards: {
    pointsPerDollar: 10,
    welcomeBonus: 100
  }
};

async function initializeDatabase() {
  console.log('🚀 Initializing Fetchly Firestore Database...');
  
  try {
    // Create sample pet owner
    console.log('📝 Creating sample pet owner...');
    await db.collection(COLLECTIONS.USERS).doc(samplePetOwner.id).set(samplePetOwner);
    
    // Create sample pets
    console.log('🐕 Creating sample pets...');
    for (const pet of samplePets) {
      await db.collection(COLLECTIONS.PETS).doc(pet.id).set(pet);
    }
    
    // Create sample provider
    console.log('👨‍⚕️ Creating sample provider...');
    await db.collection(COLLECTIONS.USERS).doc(sampleProvider.id).set(sampleProvider);
    
    // Create provider profile
    console.log('🏥 Creating provider profile...');
    await db.collection(COLLECTIONS.PROVIDER_PROFILES).doc(sampleProviderProfile.id).set(sampleProviderProfile);
    
    // Create sample services
    console.log('🛍️ Creating sample services...');
    for (const service of sampleServices) {
      await db.collection(COLLECTIONS.SERVICES).doc(service.id).set(service);
    }
    
    // Create provider subscription
    console.log('👑 Creating provider subscription...');
    await db.collection(COLLECTIONS.PROVIDER_SUBSCRIPTIONS).doc(sampleProviderSubscription.id).set(sampleProviderSubscription);
    
    // Create reward items
    console.log('🎁 Creating reward items...');
    for (const item of sampleRewardItems) {
      await db.collection(COLLECTIONS.REWARD_ITEMS).doc(item.id).set(item);
    }
    
    // Create system settings
    console.log('⚙️ Creating system settings...');
    await db.collection(COLLECTIONS.SYSTEM_SETTINGS).doc(systemSettings.id).set(systemSettings);
    
    console.log('✅ Database initialization completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • 1 Pet Owner created (${samplePetOwner.email})`);
    console.log(`   • 2 Pets created for pet owner`);
    console.log(`   • 1 Provider created (${sampleProvider.email})`);
    console.log(`   • 1 Provider profile created`);
    console.log(`   • ${sampleServices.length} Services created`);
    console.log(`   • 1 Pro subscription created`);
    console.log(`   • ${sampleRewardItems.length} Reward items created`);
    console.log(`   • System settings configured`);
    
  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  }
}

// Run the initialization
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('\n🎉 Fetchly database is ready for both Pet Owners and Providers!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Initialization failed:', error);
      process.exit(1);
    });
}

module.exports = { initializeDatabase, COLLECTIONS };
