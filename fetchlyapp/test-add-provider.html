<!DOCTYPE html>
<html>
<head>
    <title>Test Add Provider to Firebase</title>
    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, getDocs, query, where } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Your Firebase config (replace with your actual config)
        const firebaseConfig = {
            apiKey: "AIzaSyBqHYlzP-HqKxKxKxKxKxKxKxKxKxKxKxK",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdefghijklmnop"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Test provider data
        const testProvider = {
            userId: 'test-user-123',
            businessName: 'Test Pet Care Services',
            ownerName: '<PERSON>e',
            email: '<EMAIL>',
            phone: '(*************',
            serviceType: 'Pet Sitting',
            website: 'https://testpetcare.com',
            address: '123 Test Street',
            city: 'Austin',
            state: 'TX',
            zipCode: '78701',
            description: 'Professional pet care services for your beloved pets.',
            experience: '5 years',
            specialties: ['Dog Walking', 'Pet Sitting', 'Overnight Care'],
            status: 'approved',
            verified: true,
            featured: false,
            businessHours: {
                monday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                tuesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                wednesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                thursday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                friday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                saturday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                sunday: { closed: true }
            },
            documents: {},
            profilePhoto: '',
            bannerPhoto: '',
            businessPhotos: [],
            rating: 4.8,
            reviewCount: 25,
            totalBookings: 150,
            totalRevenue: 7500,
            completionRate: 98,
            responseTime: '< 1 hour',
            responseRate: 95,
            membershipTier: 'free',
            fetchPoints: 0,
            commissionsaved: 0,
            socialMedia: {},
            settings: {
                emailNotifications: true,
                smsNotifications: false,
                bookingNotifications: true,
                marketingEmails: false,
                autoAcceptBookings: false,
                requireDeposit: false,
                cancellationPolicy: 'flexible'
            },
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // Function to add test provider
        async function addTestProvider() {
            try {
                console.log('Adding test provider...');
                const docRef = await addDoc(collection(db, 'providers'), testProvider);
                console.log('✅ Test provider added with ID:', docRef.id);
                document.getElementById('result').innerHTML = `✅ Test provider added with ID: ${docRef.id}`;
                
                // Verify it was added
                await checkProviders();
            } catch (error) {
                console.error('❌ Error adding provider:', error);
                document.getElementById('result').innerHTML = `❌ Error: ${error.message}`;
            }
        }

        // Function to check existing providers
        async function checkProviders() {
            try {
                console.log('Checking existing providers...');
                
                // Get all providers
                const querySnapshot = await getDocs(collection(db, 'providers'));
                console.log(`📊 Found ${querySnapshot.size} total providers`);
                
                let providersList = '<h3>Existing Providers:</h3>';
                querySnapshot.forEach((doc) => {
                    const data = doc.data();
                    providersList += `
                        <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
                            <strong>ID:</strong> ${doc.id}<br>
                            <strong>Business:</strong> ${data.businessName}<br>
                            <strong>Owner:</strong> ${data.ownerName}<br>
                            <strong>City:</strong> ${data.city}, ${data.state} ${data.zipCode}<br>
                            <strong>Service:</strong> ${data.serviceType}<br>
                            <strong>Status:</strong> ${data.status}<br>
                            <strong>Email:</strong> ${data.email}<br>
                        </div>
                    `;
                });
                
                document.getElementById('providers').innerHTML = providersList;
                
            } catch (error) {
                console.error('❌ Error checking providers:', error);
                document.getElementById('providers').innerHTML = `❌ Error: ${error.message}`;
            }
        }

        // Make functions available globally
        window.addTestProvider = addTestProvider;
        window.checkProviders = checkProviders;

        // Check providers on page load
        checkProviders();
    </script>
</head>
<body>
    <h1>Firebase Provider Test</h1>
    
    <button onclick="addTestProvider()">Add Test Provider</button>
    <button onclick="checkProviders()">Check Existing Providers</button>
    
    <div id="result" style="margin: 20px; padding: 10px; background: #f0f0f0;"></div>
    
    <div id="providers" style="margin: 20px;"></div>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Update the Firebase config above with your actual project details</li>
        <li>Click "Check Existing Providers" to see what's in your database</li>
        <li>Click "Add Test Provider" to add a test provider</li>
        <li>Go to your search page to test if it shows up</li>
    </ol>
</body>
</html>
