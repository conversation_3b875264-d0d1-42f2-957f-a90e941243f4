const { Pool } = require('pg');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Common passwords to try
const commonPasswords = [
  'postgres',
  'admin', 
  'password',
  'root',
  '123456',
  'Admin',
  'Password',
  'postgresql',
  'fetchly',
  'P@ssw0rd',
  'admin123',
  'postgres123'
];

async function testPassword(password) {
  const config = {
    host: 'localhost',
    port: 3005,
    user: 'postgres',
    password: password,
    database: 'postgres'
  };

  const pool = new Pool(config);
  
  try {
    const client = await pool.connect();
    client.release();
    await pool.end();
    return true;
  } catch (error) {
    await pool.end();
    return false;
  }
}

async function findPassword() {
  console.log('🔍 PostgreSQL Password Finder');
  console.log('=============================');
  console.log('');
  console.log('PostgreSQL is running on port 3005');
  console.log('Trying common passwords...');
  console.log('');

  // Try common passwords first
  for (let i = 0; i < commonPasswords.length; i++) {
    const password = commonPasswords[i];
    process.stdout.write(`Testing "${password}"... `);
    
    if (await testPassword(password)) {
      console.log('✅ SUCCESS!');
      console.log('');
      console.log('🎉 Found working password!');
      console.log(`Password: "${password}"`);
      console.log('');
      console.log('Now updating your .env.local file...');
      
      // Update .env.local file
      const fs = require('fs');
      let envContent = fs.readFileSync('.env.local', 'utf8');
      envContent = envContent.replace(
        /POSTGRES_PASSWORD=.*/,
        `POSTGRES_PASSWORD=${password}`
      );
      fs.writeFileSync('.env.local', envContent);
      
      console.log('✅ .env.local updated successfully!');
      console.log('');
      console.log('🚀 Ready to continue setup:');
      console.log('   Run: node final-setup.js');
      console.log('   Or: npm run db:setup');
      
      return password;
    } else {
      console.log('❌ Failed');
    }
  }

  console.log('');
  console.log('❌ None of the common passwords worked.');
  console.log('');
  
  // Ask user for custom password
  while (true) {
    const customPassword = await new Promise(resolve => {
      rl.question('Please enter your PostgreSQL postgres user password (or "quit" to exit): ', resolve);
    });

    if (customPassword.toLowerCase() === 'quit') {
      console.log('Setup cancelled.');
      break;
    }

    process.stdout.write(`Testing "${customPassword}"... `);
    
    if (await testPassword(customPassword)) {
      console.log('✅ SUCCESS!');
      console.log('');
      console.log('🎉 Password verified!');
      console.log('');
      console.log('Now updating your .env.local file...');
      
      // Update .env.local file
      const fs = require('fs');
      let envContent = fs.readFileSync('.env.local', 'utf8');
      envContent = envContent.replace(
        /POSTGRES_PASSWORD=.*/,
        `POSTGRES_PASSWORD=${customPassword}`
      );
      fs.writeFileSync('.env.local', envContent);
      
      console.log('✅ .env.local updated successfully!');
      console.log('');
      console.log('🚀 Ready to continue setup:');
      console.log('   Run: node final-setup.js');
      console.log('   Or: npm run db:setup');
      
      return customPassword;
    } else {
      console.log('❌ Failed');
      console.log('Please try again or check your PostgreSQL installation.');
      console.log('');
    }
  }

  return null;
}

async function showHelp() {
  console.log('');
  console.log('📋 If you can\'t remember your PostgreSQL password:');
  console.log('');
  console.log('Option 1: Check pgAdmin');
  console.log('   - Open pgAdmin from your Start menu');
  console.log('   - Try connecting to see if it remembers the password');
  console.log('');
  console.log('Option 2: Reset PostgreSQL Password');
  console.log('   1. Stop PostgreSQL service:');
  console.log('      net stop postgresql-x64-17');
  console.log('');
  console.log('   2. Edit pg_hba.conf (as Administrator):');
  console.log('      File: C:\\Program Files\\PostgreSQL\\17\\data\\pg_hba.conf');
  console.log('      Change: host all all 127.0.0.1/32 scram-sha-256');
  console.log('      To:     host all all 127.0.0.1/32 trust');
  console.log('');
  console.log('   3. Start PostgreSQL:');
  console.log('      net start postgresql-x64-17');
  console.log('');
  console.log('   4. Connect and reset password:');
  console.log('      "C:\\Program Files\\PostgreSQL\\17\\bin\\psql.exe" -U postgres -h localhost -p 3005');
  console.log('      ALTER USER postgres PASSWORD \'newpassword\';');
  console.log('');
  console.log('   5. Restore pg_hba.conf (change trust back to scram-sha-256)');
  console.log('');
  console.log('   6. Restart PostgreSQL:');
  console.log('      net stop postgresql-x64-17');
  console.log('      net start postgresql-x64-17');
  console.log('');
  console.log('Option 3: Check Installation Notes');
  console.log('   - Look for any notes you made during PostgreSQL installation');
  console.log('   - Check your password manager');
  console.log('   - The password might be the same as your Windows password');
  console.log('');
}

async function main() {
  console.log('');
  console.log('╔══════════════════════════════════════════════════════════════╗');
  console.log('║              🔍 PostgreSQL Password Finder 🔍               ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log('');
  
  const foundPassword = await findPassword();
  
  if (!foundPassword) {
    await showHelp();
  }
  
  rl.close();
}

main().catch(console.error);
