import { query, transaction } from '../database';
import { Booking, BookingStatus } from '@/types/user';

export class BookingService {
  // Get all bookings for a user
  static async getUserBookings(userId: string, status?: BookingStatus): Promise<Booking[]> {
    try {
      let whereClause = 'WHERE user_id = $1';
      const params = [userId];

      if (status) {
        whereClause += ' AND status = $2';
        params.push(status);
      }

      const result = await query(`
        SELECT 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
        FROM bookings 
        ${whereClause}
        ORDER BY scheduled_date DESC, scheduled_time DESC
      `, params);

      return result.rows.map(this.mapDatabaseBookingToBooking);
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw new Error('Failed to get bookings');
    }
  }

  // Get booking by ID
  static async getBookingById(bookingId: string, userId?: string): Promise<Booking | null> {
    try {
      const whereClause = userId ? 'WHERE id = $1 AND user_id = $2' : 'WHERE id = $1';
      const params = userId ? [bookingId, userId] : [bookingId];

      const result = await query(`
        SELECT 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
        FROM bookings 
        ${whereClause}
      `, params);

      if (result.rows.length === 0) {
        return null;
      }

      return this.mapDatabaseBookingToBooking(result.rows[0]);
    } catch (error) {
      console.error('Error getting booking by ID:', error);
      throw new Error('Failed to get booking');
    }
  }

  // Create new booking
  static async createBooking(bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<Booking> {
    try {
      const result = await query(`
        INSERT INTO bookings (
          user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
      `, [
        bookingData.userId,
        bookingData.petId,
        bookingData.providerId,
        bookingData.serviceId,
        bookingData.serviceName,
        bookingData.providerName,
        bookingData.petName,
        bookingData.scheduledDate,
        bookingData.scheduledTime,
        bookingData.duration,
        bookingData.status || 'pending',
        bookingData.totalPrice,
        bookingData.paidAmount || 0,
        bookingData.paymentMethod,
        bookingData.notes,
        bookingData.specialRequests
      ]);

      return this.mapDatabaseBookingToBooking(result.rows[0]);
    } catch (error) {
      console.error('Error creating booking:', error);
      throw new Error('Failed to create booking');
    }
  }

  // Update booking status
  static async updateBookingStatus(bookingId: string, status: BookingStatus, userId?: string): Promise<Booking> {
    try {
      const whereClause = userId ? 'WHERE id = $1 AND user_id = $2' : 'WHERE id = $1';
      const params = userId ? [bookingId, userId] : [bookingId];

      let additionalFields = '';
      if (status === 'completed') {
        additionalFields = ', completed_at = CURRENT_TIMESTAMP';
      } else if (status === 'cancelled') {
        additionalFields = ', cancelled_at = CURRENT_TIMESTAMP';
      }

      const result = await query(`
        UPDATE bookings 
        SET status = $${params.length + 1}, updated_at = CURRENT_TIMESTAMP${additionalFields}
        ${whereClause}
        RETURNING 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
      `, [...params, status]);

      if (result.rows.length === 0) {
        throw new Error('Booking not found or access denied');
      }

      return this.mapDatabaseBookingToBooking(result.rows[0]);
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw new Error('Failed to update booking status');
    }
  }

  // Cancel booking
  static async cancelBooking(bookingId: string, userId: string, reason?: string): Promise<Booking> {
    try {
      const result = await query(`
        UPDATE bookings 
        SET 
          status = 'cancelled',
          cancelled_at = CURRENT_TIMESTAMP,
          cancellation_reason = $3,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2
        RETURNING 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
      `, [bookingId, userId, reason]);

      if (result.rows.length === 0) {
        throw new Error('Booking not found or access denied');
      }

      return this.mapDatabaseBookingToBooking(result.rows[0]);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw new Error('Failed to cancel booking');
    }
  }

  // Reschedule booking
  static async rescheduleBooking(
    bookingId: string, 
    userId: string, 
    newDate: string, 
    newTime: string
  ): Promise<Booking> {
    try {
      const result = await query(`
        UPDATE bookings 
        SET 
          scheduled_date = $3,
          scheduled_time = $4,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2 AND status IN ('pending', 'confirmed')
        RETURNING 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
      `, [bookingId, userId, newDate, newTime]);

      if (result.rows.length === 0) {
        throw new Error('Booking not found, access denied, or cannot be rescheduled');
      }

      return this.mapDatabaseBookingToBooking(result.rows[0]);
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      throw new Error('Failed to reschedule booking');
    }
  }

  // Add review and rating
  static async addReview(
    bookingId: string, 
    userId: string, 
    rating: number, 
    review: string
  ): Promise<Booking> {
    try {
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      const result = await query(`
        UPDATE bookings 
        SET 
          rating = $3,
          review = $4,
          review_date = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2 AND status = 'completed'
        RETURNING 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
      `, [bookingId, userId, rating, review]);

      if (result.rows.length === 0) {
        throw new Error('Booking not found, access denied, or not completed');
      }

      return this.mapDatabaseBookingToBooking(result.rows[0]);
    } catch (error) {
      console.error('Error adding review:', error);
      throw new Error('Failed to add review');
    }
  }

  // Process payment for booking
  static async processPayment(
    bookingId: string, 
    userId: string, 
    amount: number, 
    paymentMethod: string
  ): Promise<Booking> {
    try {
      const queries = [
        {
          text: `
            UPDATE bookings 
            SET 
              paid_amount = paid_amount + $3,
              payment_method = $4,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND user_id = $2
          `,
          params: [bookingId, userId, amount, paymentMethod]
        },
        {
          text: `
            INSERT INTO transactions (
              user_id, booking_id, type, amount, description, payment_method, status,
              balance_before, balance_after
            ) VALUES (
              $1, $2, 'payment', $3, $4, $5, 'completed',
              (SELECT fetchly_balance FROM users WHERE id = $1),
              (SELECT fetchly_balance FROM users WHERE id = $1)
            )
          `,
          params: [userId, bookingId, amount, `Payment for booking ${bookingId}`, paymentMethod]
        }
      ];

      await transaction(queries);

      // Return updated booking
      const booking = await this.getBookingById(bookingId, userId);
      if (!booking) {
        throw new Error('Booking not found after payment');
      }

      return booking;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw new Error('Failed to process payment');
    }
  }

  // Get upcoming bookings
  static async getUpcomingBookings(userId: string): Promise<Booking[]> {
    try {
      const result = await query(`
        SELECT 
          id, user_id, pet_id, provider_id, service_id, service_name, provider_name, pet_name,
          scheduled_date, scheduled_time, duration, status, total_price, paid_amount,
          payment_method, notes, special_requests, rating, review, review_date,
          created_at, updated_at, completed_at, cancelled_at, cancellation_reason
        FROM bookings 
        WHERE user_id = $1 
          AND status IN ('pending', 'confirmed')
          AND scheduled_date >= CURRENT_DATE
        ORDER BY scheduled_date ASC, scheduled_time ASC
      `, [userId]);

      return result.rows.map(this.mapDatabaseBookingToBooking);
    } catch (error) {
      console.error('Error getting upcoming bookings:', error);
      throw new Error('Failed to get upcoming bookings');
    }
  }

  // Helper method to map database booking to Booking interface
  private static mapDatabaseBookingToBooking(dbBooking: any): Booking {
    return {
      id: dbBooking.id,
      userId: dbBooking.user_id,
      petId: dbBooking.pet_id,
      providerId: dbBooking.provider_id,
      serviceId: dbBooking.service_id,
      serviceName: dbBooking.service_name,
      providerName: dbBooking.provider_name,
      petName: dbBooking.pet_name,
      scheduledDate: dbBooking.scheduled_date,
      scheduledTime: dbBooking.scheduled_time,
      duration: dbBooking.duration,
      status: dbBooking.status,
      totalPrice: parseFloat(dbBooking.total_price),
      paidAmount: parseFloat(dbBooking.paid_amount) || 0,
      paymentMethod: dbBooking.payment_method,
      notes: dbBooking.notes,
      specialRequests: dbBooking.special_requests,
      rating: dbBooking.rating,
      review: dbBooking.review,
      reviewDate: dbBooking.review_date?.toISOString(),
      createdAt: dbBooking.created_at.toISOString(),
      updatedAt: dbBooking.updated_at?.toISOString(),
      completedAt: dbBooking.completed_at?.toISOString(),
      cancelledAt: dbBooking.cancelled_at?.toISOString(),
      cancellationReason: dbBooking.cancellation_reason
    };
  }
}
