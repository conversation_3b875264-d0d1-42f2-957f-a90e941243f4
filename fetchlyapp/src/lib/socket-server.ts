import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { verifyToken } from './auth';
import { query } from './database';

export interface SocketUser {
  id: string;
  email: string;
  name: string;
  role: string;
  socketId: string;
}

export class ChatServer {
  private io: SocketIOServer;
  private connectedUsers: Map<string, SocketUser> = new Map();
  private userSockets: Map<string, string> = new Map(); // userId -> socketId

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NODE_ENV === 'development' ? '*' : 'https://yourdomain.com',
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const payload = verifyToken(token);
        
        // Get user details
        const result = await query(
          'SELECT id, email, name, role FROM users WHERE id = $1',
          [payload.userId]
        );

        if (result.rows.length === 0) {
          return next(new Error('User not found'));
        }

        const user = result.rows[0];
        socket.data.user = user;
        
        next();
      } catch (error) {
        next(new Error('Invalid authentication token'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      const user = socket.data.user;
      console.log(`User ${user.name} connected with socket ${socket.id}`);

      // Store user connection
      this.connectedUsers.set(socket.id, {
        ...user,
        socketId: socket.id
      });
      this.userSockets.set(user.id, socket.id);

      // Join user to their personal room
      socket.join(`user:${user.id}`);

      // Handle joining chat rooms
      socket.on('join_room', async (roomId: string) => {
        try {
          // Verify user has access to this room
          const hasAccess = await this.verifyRoomAccess(user.id, roomId);
          if (hasAccess) {
            socket.join(`room:${roomId}`);
            socket.emit('joined_room', { roomId });
            
            // Send recent messages
            const messages = await this.getRecentMessages(roomId);
            socket.emit('room_messages', { roomId, messages });
          } else {
            socket.emit('error', { message: 'Access denied to this room' });
          }
        } catch (error) {
          socket.emit('error', { message: 'Failed to join room' });
        }
      });

      // Handle leaving chat rooms
      socket.on('leave_room', (roomId: string) => {
        socket.leave(`room:${roomId}`);
        socket.emit('left_room', { roomId });
      });

      // Handle sending messages
      socket.on('send_message', async (data: {
        roomId: string;
        content: string;
        type?: 'text' | 'image' | 'file';
        fileUrl?: string;
        fileName?: string;
        fileSize?: number;
      }) => {
        try {
          // Verify user has access to this room
          const hasAccess = await this.verifyRoomAccess(user.id, data.roomId);
          if (!hasAccess) {
            socket.emit('error', { message: 'Access denied to this room' });
            return;
          }

          // Save message to database
          const message = await this.saveMessage({
            chatRoomId: data.roomId,
            senderId: user.id,
            content: data.content,
            type: data.type || 'text',
            fileUrl: data.fileUrl,
            fileName: data.fileName,
            fileSize: data.fileSize
          });

          // Broadcast message to room
          this.io.to(`room:${data.roomId}`).emit('new_message', {
            ...message,
            sender: {
              id: user.id,
              name: user.name,
              avatar: user.avatar
            }
          });

          // Update room's last message timestamp
          await this.updateRoomLastMessage(data.roomId);

        } catch (error) {
          console.error('Error sending message:', error);
          socket.emit('error', { message: 'Failed to send message' });
        }
      });

      // Handle message read receipts
      socket.on('mark_messages_read', async (data: { roomId: string; messageIds: string[] }) => {
        try {
          await this.markMessagesAsRead(user.id, data.messageIds);
          
          // Notify other users in the room
          socket.to(`room:${data.roomId}`).emit('messages_read', {
            userId: user.id,
            messageIds: data.messageIds
          });
        } catch (error) {
          console.error('Error marking messages as read:', error);
        }
      });

      // Handle typing indicators
      socket.on('typing_start', (data: { roomId: string }) => {
        socket.to(`room:${data.roomId}`).emit('user_typing', {
          userId: user.id,
          userName: user.name
        });
      });

      socket.on('typing_stop', (data: { roomId: string }) => {
        socket.to(`room:${data.roomId}`).emit('user_stopped_typing', {
          userId: user.id
        });
      });

      // Handle creating new chat rooms
      socket.on('create_room', async (data: {
        type: 'direct' | 'group';
        participants: string[];
        name?: string;
        description?: string;
      }) => {
        try {
          const roomId = await this.createChatRoom({
            type: data.type,
            participants: [user.id, ...data.participants],
            name: data.name,
            description: data.description
          });

          socket.emit('room_created', { roomId });
        } catch (error) {
          console.error('Error creating room:', error);
          socket.emit('error', { message: 'Failed to create room' });
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`User ${user.name} disconnected`);
        this.connectedUsers.delete(socket.id);
        this.userSockets.delete(user.id);
      });
    });
  }

  private async verifyRoomAccess(userId: string, roomId: string): Promise<boolean> {
    try {
      const result = await query(
        'SELECT participants FROM chat_rooms WHERE id = $1',
        [roomId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      const participants = result.rows[0].participants;
      return participants.includes(userId);
    } catch (error) {
      console.error('Error verifying room access:', error);
      return false;
    }
  }

  private async getRecentMessages(roomId: string, limit: number = 50): Promise<any[]> {
    try {
      const result = await query(`
        SELECT m.*, u.name as sender_name, u.avatar as sender_avatar
        FROM messages m
        JOIN users u ON m.sender_id = u.id
        WHERE m.chat_room_id = $1 AND m.is_deleted = false
        ORDER BY m.created_at DESC
        LIMIT $2
      `, [roomId, limit]);

      return result.rows.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error getting recent messages:', error);
      return [];
    }
  }

  private async saveMessage(data: {
    chatRoomId: string;
    senderId: string;
    content: string;
    type: string;
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
  }): Promise<any> {
    const result = await query(`
      INSERT INTO messages (
        chat_room_id, sender_id, content, type, file_url, file_name, file_size
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      data.chatRoomId,
      data.senderId,
      data.content,
      data.type,
      data.fileUrl,
      data.fileName,
      data.fileSize
    ]);

    return result.rows[0];
  }

  private async updateRoomLastMessage(roomId: string): Promise<void> {
    await query(
      'UPDATE chat_rooms SET last_message_at = CURRENT_TIMESTAMP WHERE id = $1',
      [roomId]
    );
  }

  private async markMessagesAsRead(userId: string, messageIds: string[]): Promise<void> {
    await query(`
      UPDATE messages 
      SET read_by = array_append(read_by, $1)
      WHERE id = ANY($2) AND NOT ($1 = ANY(read_by))
    `, [userId, messageIds]);
  }

  private async createChatRoom(data: {
    type: string;
    participants: string[];
    name?: string;
    description?: string;
  }): Promise<string> {
    const result = await query(`
      INSERT INTO chat_rooms (type, participants, name, description)
      VALUES ($1, $2, $3, $4)
      RETURNING id
    `, [data.type, data.participants, data.name, data.description]);

    return result.rows[0].id;
  }

  // Public methods for external use
  public sendNotificationToUser(userId: string, notification: any): void {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      this.io.to(socketId).emit('notification', notification);
    }
  }

  public sendMessageToRoom(roomId: string, message: any): void {
    this.io.to(`room:${roomId}`).emit('new_message', message);
  }

  public getConnectedUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values());
  }

  public isUserOnline(userId: string): boolean {
    return this.userSockets.has(userId);
  }
}

let chatServer: ChatServer | null = null;

export function initializeChatServer(server: HTTPServer): ChatServer {
  if (!chatServer) {
    chatServer = new ChatServer(server);
  }
  return chatServer;
}

export function getChatServer(): ChatServer | null {
  return chatServer;
}
