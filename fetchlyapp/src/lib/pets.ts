import { DatabaseService, COLLECTIONS } from './database';
import { Timestamp } from 'firebase/firestore';

// Pet Types
export type PetType = 'dog' | 'cat' | 'bird' | 'rabbit' | 'hamster' | 'fish' | 'reptile' | 'other';
export type PetGender = 'male' | 'female' | 'unknown';
export type PetSize = 'small' | 'medium' | 'large' | 'extra_large';

// Pet Interface
export interface Pet {
  id: string;
  userId: string;
  name: string;
  type: PetType;
  breed?: string;
  gender: PetGender;
  size: PetSize;
  weight?: number; // in pounds
  dateOfBirth?: string; // ISO date string
  color?: string;
  microchipId?: string;
  profileImage?: string;
  images?: string[];
  medicalNotes?: string;
  behaviorNotes?: string;
  dietaryRestrictions?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Vaccination Interface
export interface Vaccination {
  id: string;
  petId: string;
  userId: string;
  vaccineName: string;
  vaccineType: string; // e.g., 'core', 'non-core', 'required'
  dateAdministered: string; // ISO date string
  expirationDate?: string; // ISO date string
  veterinarianName?: string;
  clinicName?: string;
  batchNumber?: string;
  notes?: string;
  documentUrl?: string; // Link to vaccination certificate
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Common vaccination types
export const COMMON_VACCINATIONS = {
  dog: [
    { name: 'DHPP', type: 'core', description: 'Distemper, Hepatitis, Parvovirus, Parainfluenza' },
    { name: 'Rabies', type: 'core', description: 'Rabies vaccination' },
    { name: 'Bordetella', type: 'non-core', description: 'Kennel cough' },
    { name: 'Lyme Disease', type: 'non-core', description: 'Lyme disease prevention' },
    { name: 'Canine Influenza', type: 'non-core', description: 'Dog flu prevention' }
  ],
  cat: [
    { name: 'FVRCP', type: 'core', description: 'Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia' },
    { name: 'Rabies', type: 'core', description: 'Rabies vaccination' },
    { name: 'FeLV', type: 'non-core', description: 'Feline Leukemia Virus' },
    { name: 'FIV', type: 'non-core', description: 'Feline Immunodeficiency Virus' }
  ]
};

// Pet Service Class
export class PetService {
  // Create a new pet
  static async createPet(petData: Omit<Pet, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    return DatabaseService.create(COLLECTIONS.PETS, petData);
  }

  // Get all pets for a user
  static async getUserPets(userId: string): Promise<Pet[]> {
    return DatabaseService.query(COLLECTIONS.PETS, [
      { field: 'userId', operator: '==', value: userId },
      { field: 'isActive', operator: '==', value: true }
    ], 'createdAt', 'desc');
  }

  // Alias for backward compatibility
  static async getPetsByUser(userId: string): Promise<Pet[]> {
    return this.getUserPets(userId);
  }

  // Get a specific pet by ID
  static async getPetById(petId: string): Promise<Pet | null> {
    return DatabaseService.getById(COLLECTIONS.PETS, petId);
  }

  // Update pet information
  static async updatePet(petId: string, updates: Partial<Pet>): Promise<void> {
    return DatabaseService.update(COLLECTIONS.PETS, petId, updates);
  }

  // Soft delete a pet (mark as inactive)
  static async deletePet(petId: string): Promise<void> {
    return DatabaseService.update(COLLECTIONS.PETS, petId, { isActive: false });
  }

  // Add an image to pet's gallery
  static async addPetImage(petId: string, imageUrl: string): Promise<void> {
    const pet = await this.getPetById(petId);
    if (pet) {
      const images = pet.images || [];
      images.push(imageUrl);
      await this.updatePet(petId, { images });
    }
  }

  // Remove an image from pet's gallery
  static async removePetImage(petId: string, imageUrl: string): Promise<void> {
    const pet = await this.getPetById(petId);
    if (pet && pet.images) {
      const images = pet.images.filter(img => img !== imageUrl);
      await this.updatePet(petId, { images });
    }
  }

  // Set pet's profile image
  static async setPetProfileImage(petId: string, imageUrl: string): Promise<void> {
    await this.updatePet(petId, { profileImage: imageUrl });
  }

  // Calculate pet's age
  static calculateAge(dateOfBirth: string): { years: number; months: number } {
    const birth = new Date(dateOfBirth);
    const now = new Date();
    
    let years = now.getFullYear() - birth.getFullYear();
    let months = now.getMonth() - birth.getMonth();
    
    if (months < 0) {
      years--;
      months += 12;
    }
    
    return { years, months };
  }
}

// Vaccination Service Class
export class VaccinationService {
  // Add a vaccination record
  static async addVaccination(vaccinationData: Omit<Vaccination, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    return DatabaseService.create(COLLECTIONS.VACCINATIONS, vaccinationData);
  }

  // Get all vaccinations for a pet
  static async getPetVaccinations(petId: string): Promise<Vaccination[]> {
    return DatabaseService.query(COLLECTIONS.VACCINATIONS, [
      { field: 'petId', operator: '==', value: petId }
    ], 'dateAdministered', 'desc');
  }

  // Get all vaccinations for a user (across all pets)
  static async getUserVaccinations(userId: string): Promise<Vaccination[]> {
    return DatabaseService.query(COLLECTIONS.VACCINATIONS, [
      { field: 'userId', operator: '==', value: userId }
    ], 'dateAdministered', 'desc');
  }

  // Update vaccination record
  static async updateVaccination(vaccinationId: string, updates: Partial<Vaccination>): Promise<void> {
    return DatabaseService.update(COLLECTIONS.VACCINATIONS, vaccinationId, updates);
  }

  // Delete vaccination record
  static async deleteVaccination(vaccinationId: string): Promise<void> {
    return DatabaseService.delete(COLLECTIONS.VACCINATIONS, vaccinationId);
  }

  // Check if vaccination is due soon (within 30 days)
  static isVaccinationDueSoon(vaccination: Vaccination): boolean {
    if (!vaccination.expirationDate) return false;
    
    const expiration = new Date(vaccination.expirationDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
    
    return expiration <= thirtyDaysFromNow;
  }

  // Check if vaccination is overdue
  static isVaccinationOverdue(vaccination: Vaccination): boolean {
    if (!vaccination.expirationDate) return false;
    
    const expiration = new Date(vaccination.expirationDate);
    const now = new Date();
    
    return expiration < now;
  }

  // Get upcoming vaccinations for a pet
  static async getUpcomingVaccinations(petId: string): Promise<Vaccination[]> {
    const vaccinations = await this.getPetVaccinations(petId);
    return vaccinations.filter(v => this.isVaccinationDueSoon(v) || this.isVaccinationOverdue(v));
  }
}

// Helper functions for pet management
export const PetHelpers = {
  // Get pet type options
  getPetTypes(): Array<{ value: PetType; label: string }> {
    return [
      { value: 'dog', label: 'Dog' },
      { value: 'cat', label: 'Cat' },
      { value: 'bird', label: 'Bird' },
      { value: 'rabbit', label: 'Rabbit' },
      { value: 'hamster', label: 'Hamster' },
      { value: 'fish', label: 'Fish' },
      { value: 'reptile', label: 'Reptile' },
      { value: 'other', label: 'Other' }
    ];
  },

  // Get size options
  getSizeOptions(): Array<{ value: PetSize; label: string }> {
    return [
      { value: 'small', label: 'Small (0-25 lbs)' },
      { value: 'medium', label: 'Medium (26-60 lbs)' },
      { value: 'large', label: 'Large (61-100 lbs)' },
      { value: 'extra_large', label: 'Extra Large (100+ lbs)' }
    ];
  },

  // Get gender options
  getGenderOptions(): Array<{ value: PetGender; label: string }> {
    return [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'unknown', label: 'Unknown' }
    ];
  },

  // Format pet age for display
  formatAge(dateOfBirth: string): string {
    const { years, months } = PetService.calculateAge(dateOfBirth);
    
    if (years === 0) {
      return `${months} month${months !== 1 ? 's' : ''} old`;
    } else if (months === 0) {
      return `${years} year${years !== 1 ? 's' : ''} old`;
    } else {
      return `${years} year${years !== 1 ? 's' : ''}, ${months} month${months !== 1 ? 's' : ''} old`;
    }
  },

  // Get vaccination recommendations for pet type
  getVaccinationRecommendations(petType: PetType): Array<{ name: string; type: string; description: string }> {
    return COMMON_VACCINATIONS[petType as keyof typeof COMMON_VACCINATIONS] || [];
  }
};
