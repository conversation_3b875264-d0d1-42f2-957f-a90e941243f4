import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firebase Admin SDK only when needed
let app: any = null;

function getFirebaseAdminApp() {
  if (app) return app;

  const existingApps = getApps();
  if (existingApps.length > 0) {
    app = existingApps[0];
    return app;
  }

  // Only initialize if we have the required environment variables
  if (!process.env.FIREBASE_ADMIN_PROJECT_ID ||
      !process.env.FIREBASE_ADMIN_CLIENT_EMAIL ||
      !process.env.FIREBASE_ADMIN_PRIVATE_KEY) {
    console.warn('Firebase Admin environment variables not found');
    return null;
  }

  const firebaseAdminConfig = {
    credential: cert({
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
      clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
    projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  };

  app = initializeApp(firebaseAdminConfig);
  return app;
}

// Export lazy-loaded instances
export const auth = (() => {
  const app = getFirebaseAdminApp();
  return app ? getAuth(app) : null;
})();

export const adminDb = (() => {
  const app = getFirebaseAdminApp();
  return app ? getFirestore(app) : null;
})();

export default getFirebaseAdminApp();
