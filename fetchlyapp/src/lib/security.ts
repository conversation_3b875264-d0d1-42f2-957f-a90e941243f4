import { NextRequest } from 'next/server';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

// Email validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (password.length > 128) {
    errors.push('Password must be less than 128 characters');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  // Check for common patterns
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i
  ];
  
  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      errors.push('Password contains common patterns and is not secure');
      break;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// SQL injection protection (additional layer)
export function detectSQLInjection(input: string): boolean {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(--|\/\*|\*\/)/,
    /(\b(CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()/i,
    /(\b(WAITFOR|DELAY)\b)/i,
    /(xp_|sp_)/i,
    /(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)/i
  ];
  
  return sqlPatterns.some(pattern => pattern.test(input));
}

// XSS protection
export function detectXSS(input: string): boolean {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[^>]*>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi
  ];
  
  return xssPatterns.some(pattern => pattern.test(input));
}

// File upload validation
export function validateFileUpload(file: {
  name: string;
  size: number;
  type: string;
}): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const maxSize = parseInt(process.env.UPLOAD_MAX_SIZE || '10485760'); // 10MB default
  const allowedTypes = (process.env.UPLOAD_ALLOWED_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(',');
  
  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size exceeds maximum allowed size of ${maxSize / 1024 / 1024}MB`);
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase();
  const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
  
  if (!extension || !allowedExtensions.includes(extension)) {
    errors.push('Invalid file extension');
  }
  
  // Check for suspicious file names
  const suspiciousPatterns = [
    /\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|php|asp|jsp)$/i,
    /\.\w+\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|php|asp|jsp)$/i
  ];
  
  if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
    errors.push('Suspicious file name detected');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Rate limiting configurations
export const rateLimitConfigs = {
  // General API rate limit
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window
    message: 'Too many requests from this IP, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  },
  
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: 'Too many authentication attempts, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  },
  
  // Password reset
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 attempts per hour
    message: 'Too many password reset attempts, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  },
  
  // File upload
  upload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 20, // 20 uploads per hour
    message: 'Too many file uploads, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  }
};

// Security headers configuration
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://api.stripe.com wss: ws:",
    "frame-src https://js.stripe.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')
};

// CORS configuration
export const corsConfig = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com', 'https://www.yourdomain.com']
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
};

// IP whitelist for admin endpoints
export const adminIPWhitelist = process.env.ADMIN_IP_WHITELIST?.split(',') || [];

// Check if IP is whitelisted for admin access
export function isAdminIPWhitelisted(ip: string): boolean {
  if (adminIPWhitelist.length === 0) return true; // No whitelist configured
  return adminIPWhitelist.includes(ip);
}

// Generate secure random string
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

// Validate UUID format
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Validate date format
export function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

// Validate phone number (basic US format)
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;
  return phoneRegex.test(phone);
}

// Log security events
export function logSecurityEvent(event: {
  type: 'auth_failure' | 'rate_limit' | 'sql_injection' | 'xss_attempt' | 'invalid_file' | 'admin_access';
  ip: string;
  userAgent?: string;
  userId?: string;
  details?: any;
}): void {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    ...event
  };
  
  // In production, you would send this to a proper logging service
  console.warn('SECURITY EVENT:', JSON.stringify(logEntry));
  
  // You could also store critical security events in the database
  if (['sql_injection', 'xss_attempt'].includes(event.type)) {
    // Store in database for analysis
    // This would be implemented based on your logging requirements
  }
}

// Comprehensive input validation
export function validateAndSanitizeInput(
  input: any,
  type: 'string' | 'email' | 'phone' | 'uuid' | 'date' | 'number',
  options: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  } = {}
): {
  isValid: boolean;
  value: any;
  errors: string[];
} {
  const errors: string[] = [];
  let value = input;
  
  // Check if required
  if (options.required && (input === undefined || input === null || input === '')) {
    errors.push('This field is required');
    return { isValid: false, value: null, errors };
  }
  
  // If not required and empty, return valid
  if (!options.required && (input === undefined || input === null || input === '')) {
    return { isValid: true, value: null, errors: [] };
  }
  
  // Type-specific validation
  switch (type) {
    case 'string':
      value = sanitizeInput(String(input));
      if (options.minLength && value.length < options.minLength) {
        errors.push(`Must be at least ${options.minLength} characters long`);
      }
      if (options.maxLength && value.length > options.maxLength) {
        errors.push(`Must be no more than ${options.maxLength} characters long`);
      }
      if (detectSQLInjection(value)) {
        errors.push('Invalid characters detected');
      }
      if (detectXSS(value)) {
        errors.push('Invalid content detected');
      }
      break;
      
    case 'email':
      value = sanitizeInput(String(input)).toLowerCase();
      if (!isValidEmail(value)) {
        errors.push('Invalid email format');
      }
      break;
      
    case 'phone':
      value = sanitizeInput(String(input));
      if (!isValidPhoneNumber(value)) {
        errors.push('Invalid phone number format');
      }
      break;
      
    case 'uuid':
      value = sanitizeInput(String(input));
      if (!isValidUUID(value)) {
        errors.push('Invalid ID format');
      }
      break;
      
    case 'date':
      if (!isValidDate(String(input))) {
        errors.push('Invalid date format');
      }
      value = new Date(String(input));
      break;
      
    case 'number':
      value = Number(input);
      if (isNaN(value)) {
        errors.push('Must be a valid number');
      }
      if (options.min !== undefined && value < options.min) {
        errors.push(`Must be at least ${options.min}`);
      }
      if (options.max !== undefined && value > options.max) {
        errors.push(`Must be no more than ${options.max}`);
      }
      break;
  }
  
  return {
    isValid: errors.length === 0,
    value,
    errors
  };
}
