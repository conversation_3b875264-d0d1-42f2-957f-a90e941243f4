import Stripe from 'stripe';

// Server-only Stripe Configuration
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

// Server-only webhook signature verification
export function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): Stripe.Event {
  try {
    return stripe.webhooks.constructEvent(payload, signature, secret);
  } catch (error: any) {
    throw new Error(`Invalid webhook signature: ${error.message}`);
  }
}

// Server-only logging
export function logProductionEvent(
  event: string,
  data: any,
  level: 'info' | 'warn' | 'error' = 'info'
) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    event,
    level,
    data: typeof data === 'object' ? JSON.stringify(data) : data,
    environment: process.env.NODE_ENV,
  };
  
  // In production, you'd send this to your logging service
  console.log(`[${level.toUpperCase()}] ${timestamp} - ${event}:`, logData);
}
