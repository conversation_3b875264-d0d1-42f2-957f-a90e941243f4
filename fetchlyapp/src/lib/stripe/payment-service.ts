import { stripe, STRIPE_CONFIG, calculatePlatformFee, formatAmountForStripe } from './config';
import { doc, updateDoc, addDoc, collection, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export interface PaymentIntentData {
  amount: number; // in dollars
  currency?: string;
  customerId?: string;
  providerId: string;
  serviceId: string;
  description: string;
  metadata?: Record<string, string>;
}

export interface WalletTopupData {
  amount: number; // in dollars
  customerId: string;
  paymentMethodId?: string;
}

export interface WalletPaymentData {
  amount: number; // in dollars
  userId: string;
  providerId: string;
  serviceId: string;
  description: string;
}

/**
 * PetOwner Payment Service
 * Handles credit card payments and Fetchly wallet operations
 */
export class PaymentService {
  
  /**
   * Create a payment intent for credit card payment
   * Includes platform fee and transfer to provider
   */
  static async createPaymentIntent(data: PaymentIntentData) {
    try {
      const amountInCents = formatAmountForStripe(data.amount);
      const platformFee = calculatePlatformFee(amountInCents);
      
      // Get provider's Stripe account ID
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, data.providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }
      
      const provider = providerDoc.data();
      if (!provider.stripeAccountId) {
        throw new Error('Provider has not completed Stripe onboarding');
      }

      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: data.currency || STRIPE_CONFIG.DEFAULT_CURRENCY,
        customer: data.customerId,
        payment_method_types: ['card'],
        application_fee_amount: platformFee,
        transfer_data: {
          destination: provider.stripeAccountId,
        },
        metadata: {
          providerId: data.providerId,
          serviceId: data.serviceId,
          type: 'service_payment',
          ...data.metadata,
        },
        description: data.description,
      });

      // Log transaction in Firestore
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'payment',
        status: 'pending',
        amount: data.amount,
        platformFee: platformFee / 100,
        providerId: data.providerId,
        serviceId: data.serviceId,
        customerId: data.customerId,
        stripePaymentIntentId: paymentIntent.id,
        description: data.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error: any) {
      console.error('Error creating payment intent:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Top up Fetchly wallet using credit card
   */
  static async topupWallet(data: WalletTopupData) {
    try {
      const amountInCents = formatAmountForStripe(data.amount);
      
      if (amountInCents < STRIPE_CONFIG.MIN_WALLET_TOPUP) {
        throw new Error(`Minimum topup amount is $${STRIPE_CONFIG.MIN_WALLET_TOPUP / 100}`);
      }

      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: STRIPE_CONFIG.DEFAULT_CURRENCY,
        customer: data.customerId,
        payment_method: data.paymentMethodId,
        payment_method_types: ['card'],
        confirm: data.paymentMethodId ? true : false,
        metadata: {
          type: 'wallet_topup',
          userId: data.customerId,
        },
        description: `Fetchly Wallet Top-up - $${data.amount}`,
      });

      // Log transaction in Firestore
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'wallet_topup',
        status: 'pending',
        amount: data.amount,
        userId: data.customerId,
        stripePaymentIntentId: paymentIntent.id,
        description: `Wallet top-up of $${data.amount}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error: any) {
      console.error('Error topping up wallet:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Pay using Fetchly wallet balance
   */
  static async payWithWallet(data: WalletPaymentData) {
    try {
      // Get user's wallet balance
      const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, data.userId));
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const user = userDoc.data();
      const walletBalance = user.fetchlyBalance || 0;

      if (walletBalance < data.amount) {
        throw new Error('Insufficient wallet balance');
      }

      // Get provider's Stripe account for transfer
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, data.providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }

      const provider = providerDoc.data();
      if (!provider.stripeAccountId) {
        throw new Error('Provider has not completed Stripe onboarding');
      }

      const amountInCents = formatAmountForStripe(data.amount);
      const platformFee = calculatePlatformFee(amountInCents);
      const providerAmount = amountInCents - platformFee;

      // Create transfer to provider
      const transfer = await stripe.transfers.create({
        amount: providerAmount,
        currency: STRIPE_CONFIG.DEFAULT_CURRENCY,
        destination: provider.stripeAccountId,
        metadata: {
          type: 'wallet_payment',
          userId: data.userId,
          providerId: data.providerId,
          serviceId: data.serviceId,
        },
      });

      // Update user's wallet balance
      await updateDoc(doc(db, COLLECTIONS.USERS, data.userId), {
        fetchlyBalance: walletBalance - data.amount,
        updatedAt: new Date().toISOString(),
      });

      // Log transaction in Firestore
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'wallet_payment',
        status: 'succeeded',
        amount: data.amount,
        platformFee: platformFee / 100,
        providerId: data.providerId,
        serviceId: data.serviceId,
        userId: data.userId,
        stripeTransferId: transfer.id,
        description: data.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        transfer,
        newBalance: walletBalance - data.amount,
      };
    } catch (error: any) {
      console.error('Error processing wallet payment:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get user's saved payment methods
   */
  static async getPaymentMethods(customerId: string) {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return {
        success: true,
        paymentMethods: paymentMethods.data,
      };
    } catch (error: any) {
      console.error('Error fetching payment methods:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Save a payment method for future use
   */
  static async savePaymentMethod(customerId: string, paymentMethodId: string) {
    try {
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      return {
        success: true,
        message: 'Payment method saved successfully',
      };
    } catch (error: any) {
      console.error('Error saving payment method:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
