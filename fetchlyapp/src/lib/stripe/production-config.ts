// Production Configuration Constants (Client-safe)
// Note: Stripe instance is created in server-side files only

// Production Configuration Constants
export const PRODUCTION_CONFIG = {
  // Platform fees (in percentage)
  PLATFORM_FEE_PERCENTAGE: 10, // 10% platform fee
  
  // Minimum amounts (in dollars)
  MIN_WALLET_TOPUP: 5,
  MAX_WALLET_TOPUP: 500,
  MIN_SERVICE_PAYMENT: 10,
  MAX_SERVICE_PAYMENT: 2000,
  MIN_INVOICE_AMOUNT: 1,
  MAX_INVOICE_AMOUNT: 10000,
  
  // Payout settings
  MIN_PAYOUT_AMOUNT: 25,
  PAYOUT_SCHEDULE: 'weekly', // weekly, daily, monthly
  
  // Currency
  DEFAULT_CURRENCY: 'usd',
  
  // Webhook events to listen for
  WEBHOOK_EVENTS: [
    'payment_intent.succeeded',
    'payment_intent.payment_failed',
    'checkout.session.completed',
    'invoice.payment_succeeded',
    'invoice.payment_failed',
    'account.updated',
    'payout.created',
    'payout.paid',
    'payout.failed',
    'transfer.created',
    'transfer.paid',
    'transfer.failed',
    'charge.dispute.created',
    'customer.subscription.created',
    'customer.subscription.updated',
    'customer.subscription.deleted',
  ],
  
  // Connect account requirements
  CONNECT_REQUIREMENTS: {
    business_type: 'individual',
    country: 'US',
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true },
    },
    tos_acceptance: {
      service_agreement: 'recipient',
    },
  },
  
  // Payment method types
  PAYMENT_METHODS: ['card', 'us_bank_account'],
  
  // Subscription plans (for premium features)
  SUBSCRIPTION_PLANS: {
    BASIC: {
      priceId: 'price_basic_monthly',
      name: 'Basic Plan',
      price: 9.99,
      features: ['Basic profile', 'Up to 5 services', 'Standard support'],
    },
    PREMIUM: {
      priceId: 'price_premium_monthly',
      name: 'Premium Plan',
      price: 29.99,
      features: ['Premium profile', 'Unlimited services', 'Priority support', 'Analytics'],
    },
    ENTERPRISE: {
      priceId: 'price_enterprise_monthly',
      name: 'Enterprise Plan',
      price: 99.99,
      features: ['Enterprise features', 'Custom branding', 'Dedicated support', 'API access'],
    },
  },
};

// Production utility functions
export class ProductionStripeService {
  
  /**
   * Calculate platform fee for a given amount
   */
  static calculatePlatformFee(amount: number): number {
    return Math.round(amount * (PRODUCTION_CONFIG.PLATFORM_FEE_PERCENTAGE / 100) * 100) / 100;
  }
  
  /**
   * Calculate provider earnings after platform fee
   */
  static calculateProviderEarnings(amount: number): number {
    const platformFee = this.calculatePlatformFee(amount);
    return Math.round((amount - platformFee) * 100) / 100;
  }
  
  /**
   * Validate payment amount
   */
  static validatePaymentAmount(amount: number, type: 'wallet' | 'service' | 'invoice'): boolean {
    switch (type) {
      case 'wallet':
        return amount >= PRODUCTION_CONFIG.MIN_WALLET_TOPUP && amount <= PRODUCTION_CONFIG.MAX_WALLET_TOPUP;
      case 'service':
        return amount >= PRODUCTION_CONFIG.MIN_SERVICE_PAYMENT && amount <= PRODUCTION_CONFIG.MAX_SERVICE_PAYMENT;
      case 'invoice':
        return amount >= PRODUCTION_CONFIG.MIN_INVOICE_AMOUNT && amount <= PRODUCTION_CONFIG.MAX_INVOICE_AMOUNT;
      default:
        return false;
    }
  }
  
  /**
   * Format currency for display
   */
  static formatCurrency(amount: number, currency: string = PRODUCTION_CONFIG.DEFAULT_CURRENCY): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  }
  
  /**
   * Convert dollars to cents for Stripe
   */
  static dollarsToCents(dollars: number): number {
    return Math.round(dollars * 100);
  }
  
  /**
   * Convert cents to dollars from Stripe
   */
  static centsToDollars(cents: number): number {
    return Math.round(cents) / 100;
  }
}

// Error handling for production
export class StripeProductionError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'StripeProductionError';
  }
}
