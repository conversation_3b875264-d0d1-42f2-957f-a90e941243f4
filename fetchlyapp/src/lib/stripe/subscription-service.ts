import { doc, updateDoc, addDoc, collection, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

// Re-export types from client config
export type { SubscriptionTier, ProviderSubscription } from './client-config';
export { SUBSCRIPTION_TIERS } from './client-config';

export class SubscriptionService {
  /**
   * Create subscription checkout session (client-side API call)
   */
  static async createSubscriptionCheckout(
    providerId: string,
    tier: 'pro' | 'premium',
    successUrl?: string,
    cancelUrl?: string
  ) {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tier,
          successUrl,
          cancelUrl,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Error creating subscription checkout:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Cancel subscription (client-side API call)
   */
  static async cancelSubscription(providerId: string, cancelAtPeriodEnd: boolean = true) {
    try {
      const response = await fetch('/api/subscriptions/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cancelAtPeriodEnd,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }



  /**
   * Get provider subscription status
   */
  static async getSubscription(providerId: string): Promise<ProviderSubscription | null> {
    try {
      const subscriptionDoc = await getDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId));
      if (!subscriptionDoc.exists()) {
        return null;
      }
      return subscriptionDoc.data() as ProviderSubscription;
    } catch (error) {
      console.error('Error getting subscription:', error);
      return null;
    }
  }

  /**
   * Check if provider can create more bookings (client-side)
   */
  static async canCreateBooking(providerId: string): Promise<boolean> {
    try {
      const subscription = await this.getSubscription(providerId);
      if (!subscription) {
        // No subscription = free tier with 5 booking limit
        return false; // Will be checked against free tier limits
      }

      const tierConfig = SUBSCRIPTION_TIERS[subscription.tier];
      if (tierConfig.features.bookingLimit === null) {
        return true; // Unlimited
      }

      return subscription.bookingsThisMonth < tierConfig.features.bookingLimit;
    } catch (error) {
      console.error('Error checking booking limit:', error);
      return false;
    }
  }
}
