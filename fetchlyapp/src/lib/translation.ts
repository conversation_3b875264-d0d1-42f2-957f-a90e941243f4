// Translation utility for Spanish to English
// Using a simple translation service (can be replaced with Google Translate API)

interface TranslationResponse {
  translatedText: string;
  originalText: string;
  detectedLanguage: string;
}

// Simple Spanish to English dictionary for common words/phrases
const spanishToEnglish: { [key: string]: string } = {
  // Greetings
  'hola': 'hello',
  'buenos días': 'good morning',
  'buenas tardes': 'good afternoon',
  'buenas noches': 'good evening',
  'adiós': 'goodbye',
  'hasta luego': 'see you later',
  
  // Common phrases
  'gracias': 'thank you',
  'de nada': 'you\'re welcome',
  'por favor': 'please',
  'lo siento': 'sorry',
  'disculpe': 'excuse me',
  'no entiendo': 'I don\'t understand',
  'habla inglés': 'do you speak English',
  
  // Pet-related terms
  'perro': 'dog',
  'gato': 'cat',
  'mascota': 'pet',
  'veterinario': 'veterinarian',
  'comida': 'food',
  'agua': 'water',
  'paseo': 'walk',
  'jugar': 'play',
  'cuidar': 'take care',
  'amor': 'love',
  'bonito': 'beautiful',
  'lindo': 'cute',
  
  // Common words
  'sí': 'yes',
  'no': 'no',
  'muy': 'very',
  'bien': 'good/well',
  'mal': 'bad',
  'grande': 'big',
  'pequeño': 'small',
  'nuevo': 'new',
  'viejo': 'old',
  'bueno': 'good',
  'malo': 'bad',
  'feliz': 'happy',
  'triste': 'sad',
  
  // Numbers
  'uno': 'one',
  'dos': 'two',
  'tres': 'three',
  'cuatro': 'four',
  'cinco': 'five',
  
  // Time
  'hoy': 'today',
  'ayer': 'yesterday',
  'mañana': 'tomorrow',
  'ahora': 'now',
  'después': 'later',
  'antes': 'before',
  
  // Actions
  'comer': 'eat',
  'beber': 'drink',
  'dormir': 'sleep',
  'caminar': 'walk',
  'correr': 'run',
  'saltar': 'jump',
  'mirar': 'look',
  'escuchar': 'listen',
  'hablar': 'speak',
  'escribir': 'write',
  'leer': 'read',
  
  // Family
  'familia': 'family',
  'padre': 'father',
  'madre': 'mother',
  'hijo': 'son',
  'hija': 'daughter',
  'hermano': 'brother',
  'hermana': 'sister',
  
  // Common sentences
  'me gusta': 'I like',
  'no me gusta': 'I don\'t like',
  'tengo hambre': 'I\'m hungry',
  'tengo sed': 'I\'m thirsty',
  'estoy cansado': 'I\'m tired',
  'estoy feliz': 'I\'m happy',
  'estoy triste': 'I\'m sad',
  'qué tal': 'how are you',
  'muy bien': 'very good',
  'más o menos': 'so-so',
  'cuánto cuesta': 'how much does it cost',
  'dónde está': 'where is',
  'qué hora es': 'what time is it',
  'cómo te llamas': 'what\'s your name',
  'me llamo': 'my name is',
  'mucho gusto': 'nice to meet you',
  'con permiso': 'excuse me',
  'buen trabajo': 'good job',
  'felicidades': 'congratulations',
  'que tengas un buen día': 'have a good day'
};

// Detect if text is likely Spanish
export function detectSpanish(text: string): boolean {
  const spanishWords = Object.keys(spanishToEnglish);
  const words = text.toLowerCase().split(/\s+/);
  const spanishWordCount = words.filter(word => 
    spanishWords.some(spanishWord => word.includes(spanishWord))
  ).length;
  
  // If more than 30% of words are Spanish, consider it Spanish text
  return spanishWordCount / words.length > 0.3;
}

// Simple translation function
export function translateSpanishToEnglish(text: string): string {
  let translatedText = text.toLowerCase();
  
  // Replace Spanish phrases/words with English equivalents
  Object.entries(spanishToEnglish).forEach(([spanish, english]) => {
    const regex = new RegExp(`\\b${spanish}\\b`, 'gi');
    translatedText = translatedText.replace(regex, english);
  });
  
  // Capitalize first letter
  translatedText = translatedText.charAt(0).toUpperCase() + translatedText.slice(1);
  
  return translatedText;
}

// Advanced translation using external API (placeholder for future implementation)
export async function translateWithAPI(text: string, targetLanguage: string = 'en'): Promise<TranslationResponse> {
  try {
    // This is a placeholder for Google Translate API or similar service
    // For now, use the simple dictionary translation
    const isSpanish = detectSpanish(text);
    
    if (isSpanish && targetLanguage === 'en') {
      const translated = translateSpanishToEnglish(text);
      return {
        translatedText: translated,
        originalText: text,
        detectedLanguage: 'es'
      };
    }
    
    // If not Spanish or already English, return original
    return {
      translatedText: text,
      originalText: text,
      detectedLanguage: 'en'
    };
  } catch (error) {
    console.error('Translation error:', error);
    return {
      translatedText: text,
      originalText: text,
      detectedLanguage: 'unknown'
    };
  }
}

// Format translation for display
export function formatTranslation(original: string, translated: string): string {
  if (original === translated) {
    return original;
  }
  return `${translated} (Original: ${original})`;
}

// Check if translation is needed
export function needsTranslation(text: string): boolean {
  return detectSpanish(text);
}

// Get translation button text
export function getTranslationButtonText(isTranslated: boolean): string {
  return isTranslated ? 'Show Original' : 'Translate to English';
}

// Common Spanish pet-related phrases for better detection
export const spanishPetPhrases = [
  'mi perro',
  'mi gato',
  'mi mascota',
  'está enfermo',
  'necesita comida',
  'quiere jugar',
  'muy bonito',
  'es muy lindo',
  'le gusta',
  'no le gusta',
  'tiene hambre',
  'tiene sed',
  'está cansado',
  'está feliz',
  'está triste',
  'buen perro',
  'buen gato',
  'ven aquí',
  'siéntate',
  'quieto',
  'vamos',
  'hora de comer',
  'hora de dormir',
  'hora del paseo'
];

export default {
  detectSpanish,
  translateSpanishToEnglish,
  translateWithAPI,
  formatTranslation,
  needsTranslation,
  getTranslationButtonText,
  spanishPetPhrases
};
