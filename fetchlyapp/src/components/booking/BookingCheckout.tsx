'use client';

import { useState, useEffect } from 'react';
import { Clock, DollarSign, Plus, Minus, AlertTriangle, Info } from 'lucide-react';
import { CommissionService } from '@/lib/monetization/commission-service';

interface AddOn {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
}

interface BookingCheckoutProps {
  servicePrice: number;
  serviceName: string;
  providerName: string;
  scheduledDateTime: Date;
  availableAddOns: AddOn[];
  onPricingChange: (pricing: any) => void;
  onProceedToPayment: (selectedAddOns: AddOn[], pricing: any) => void;
}

export default function BookingCheckout({
  servicePrice,
  serviceName,
  providerName,
  scheduledDateTime,
  availableAddOns,
  onPricingChange,
  onProceedToPayment,
}: BookingCheckoutProps) {
  const [selectedAddOns, setSelectedAddOns] = useState<AddOn[]>([]);
  const [showPricingBreakdown, setShowPricingBreakdown] = useState(false);

  // Calculate timing
  const now = new Date();
  const hoursUntilBooking = (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
  const isExpressBooking = CommissionService.isExpressBooking(hoursUntilBooking);

  // Calculate pricing
  const pricing = CommissionService.calculateBookingPricing(
    servicePrice,
    selectedAddOns,
    0, // No tip at booking time
    isExpressBooking
  );

  // Update parent component when pricing changes
  useEffect(() => {
    onPricingChange(pricing);
  }, [selectedAddOns, servicePrice, isExpressBooking]);

  const handleAddOnToggle = (addOn: AddOn) => {
    setSelectedAddOns(prev => {
      const isSelected = prev.some(item => item.id === addOn.id);
      if (isSelected) {
        return prev.filter(item => item.id !== addOn.id);
      } else {
        return [...prev, addOn];
      }
    });
  };

  const handleProceedToPayment = () => {
    onProceedToPayment(selectedAddOns, pricing);
  };

  // Group add-ons by category
  const addOnsByCategory = availableAddOns.reduce((acc, addOn) => {
    if (!acc[addOn.category]) {
      acc[addOn.category] = [];
    }
    acc[addOn.category].push(addOn);
    return acc;
  }, {} as Record<string, AddOn[]>);

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg border border-gray-100">
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Booking Summary</h2>
        <div className="text-gray-600">
          <p className="font-medium">{serviceName}</p>
          <p>with {providerName}</p>
          <p className="text-sm mt-1">
            {scheduledDateTime.toLocaleDateString()} at {scheduledDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </p>
        </div>
      </div>

      {/* Express Booking Warning */}
      {isExpressBooking && (
        <div className="p-4 mx-6 mt-6 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-orange-900">Express Booking Fee</h4>
              <p className="text-sm text-orange-700 mt-1">
                Bookings made less than 3 hours in advance include a ${CommissionService.EXPRESS_BOOKING_FEE} express fee.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Service Details */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-gray-900">{serviceName}</h3>
            <p className="text-sm text-gray-600">Base service</p>
          </div>
          <div className="text-right">
            <p className="font-semibold text-gray-900">${servicePrice.toFixed(2)}</p>
          </div>
        </div>
      </div>

      {/* Add-ons Selection */}
      {Object.keys(addOnsByCategory).length > 0 && (
        <div className="p-6 border-b border-gray-100">
          <h3 className="font-semibold text-gray-900 mb-4">Add-ons & Extras</h3>
          
          {Object.entries(addOnsByCategory).map(([category, addOns]) => (
            <div key={category} className="mb-6 last:mb-0">
              <h4 className="font-medium text-gray-700 mb-3 text-sm uppercase tracking-wide">
                {category}
              </h4>
              <div className="space-y-3">
                {addOns.map((addOn) => {
                  const isSelected = selectedAddOns.some(item => item.id === addOn.id);
                  return (
                    <div
                      key={addOn.id}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleAddOnToggle(addOn)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div
                              className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                                isSelected
                                  ? 'border-blue-500 bg-blue-500'
                                  : 'border-gray-300'
                              }`}
                            >
                              {isSelected && (
                                <Plus className="w-3 h-3 text-white" />
                              )}
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">{addOn.name}</h5>
                              <p className="text-sm text-gray-600">{addOn.description}</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">+${addOn.price.toFixed(2)}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pricing Breakdown */}
      <div className="p-6 bg-gray-50">
        <button
          onClick={() => setShowPricingBreakdown(!showPricingBreakdown)}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="font-semibold text-gray-900">Pricing Breakdown</h3>
          <Info className="w-5 h-5 text-gray-500" />
        </button>

        {showPricingBreakdown && (
          <div className="mt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Service</span>
              <span className="text-gray-900">${pricing.serviceAmount.toFixed(2)}</span>
            </div>
            
            {selectedAddOns.map((addOn) => (
              <div key={addOn.id} className="flex justify-between text-sm">
                <span className="text-gray-600">{addOn.name}</span>
                <span className="text-gray-900">${addOn.price.toFixed(2)}</span>
              </div>
            ))}

            {pricing.expressBookingFee > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Express Booking Fee</span>
                <span className="text-gray-900">${pricing.expressBookingFee.toFixed(2)}</span>
              </div>
            )}

            <div className="border-t border-gray-200 pt-2 mt-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Subtotal</span>
                <span className="text-gray-900">${pricing.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Platform Fee (10%)</span>
                <span className="text-gray-900">${pricing.platformCommission.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Total */}
        <div className="border-t border-gray-300 pt-4 mt-4">
          <div className="flex justify-between items-center">
            <span className="text-lg font-semibold text-gray-900">Total</span>
            <span className="text-2xl font-bold text-gray-900">${pricing.total.toFixed(2)}</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Provider receives ${pricing.providerEarnings.toFixed(2)} after platform fee
          </p>
        </div>

        {/* Platform Fee Info */}
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <Info className="w-4 h-4 text-blue-600 mt-0.5" />
            <div className="text-xs text-blue-700">
              <p className="font-medium">Platform Fee Breakdown:</p>
              <p>• 10% fee on service and add-ons helps maintain the platform</p>
              <p>• Secure payment processing and dispute protection included</p>
              <p>• Tips can be added after service completion (5% platform fee)</p>
            </div>
          </div>
        </div>

        {/* Proceed Button */}
        <button
          onClick={handleProceedToPayment}
          className="w-full mt-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
        >
          Proceed to Payment
        </button>
      </div>
    </div>
  );
}
