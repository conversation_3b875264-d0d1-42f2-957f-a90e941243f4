'use client';

import { useState } from 'react';
import { Heart, Star, DollarSign, CreditCard } from 'lucide-react';
import { CommissionService } from '@/lib/monetization/commission-service';
import toast from 'react-hot-toast';

interface TipInterfaceProps {
  bookingId: string;
  providerId: string;
  providerName: string;
  serviceName: string;
  serviceAmount: number;
  onTipSubmitted: (tipAmount: number) => void;
  onSkip: () => void;
}

export default function TipInterface({
  bookingId,
  providerId,
  providerName,
  serviceName,
  serviceAmount,
  onTipSubmitted,
  onSkip,
}: TipInterfaceProps) {
  const [selectedTip, setSelectedTip] = useState<number | null>(null);
  const [customTip, setCustomTip] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [rating, setRating] = useState(5);

  // Preset tip amounts
  const presetTips = [3, 5, 10];
  
  // Calculate suggested tip percentages
  const tipPercentages = [15, 18, 20];
  const suggestedTips = tipPercentages.map(percentage => 
    Math.round((serviceAmount * percentage / 100) * 100) / 100
  );

  const finalTipAmount = selectedTip || parseFloat(customTip) || 0;
  const tipCommission = finalTipAmount * CommissionService.TIP_COMMISSION_RATE;
  const providerReceives = finalTipAmount - tipCommission;

  const handleTipSelection = (amount: number) => {
    setSelectedTip(amount);
    setCustomTip('');
  };

  const handleCustomTipChange = (value: string) => {
    setCustomTip(value);
    setSelectedTip(null);
  };

  const handleSubmitTip = async () => {
    if (finalTipAmount <= 0) {
      toast.error('Please select or enter a tip amount');
      return;
    }

    if (finalTipAmount > 1000) {
      toast.error('Tip amount cannot exceed $1,000');
      return;
    }

    setIsProcessing(true);

    try {
      // Here you would integrate with your payment processing
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success(`Thank you! $${finalTipAmount.toFixed(2)} tip sent to ${providerName}`);
      onTipSubmitted(finalTipAmount);
    } catch (error) {
      console.error('Error processing tip:', error);
      toast.error('Failed to process tip. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-500 to-blue-500 p-6 text-white">
        <div className="text-center">
          <Heart className="w-12 h-12 mx-auto mb-3 text-white" />
          <h2 className="text-xl font-bold mb-1">Show Your Appreciation</h2>
          <p className="text-green-100 text-sm">
            Your service with {providerName} is complete!
          </p>
        </div>
      </div>

      {/* Service Summary */}
      <div className="p-6 border-b border-gray-100">
        <div className="text-center">
          <h3 className="font-semibold text-gray-900 mb-1">{serviceName}</h3>
          <p className="text-gray-600 text-sm">Service Amount: ${serviceAmount.toFixed(2)}</p>
        </div>

        {/* Rating */}
        <div className="mt-4">
          <p className="text-sm font-medium text-gray-700 mb-2 text-center">
            How was your experience?
          </p>
          <div className="flex justify-center space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => setRating(star)}
                className={`p-1 transition-colors ${
                  star <= rating ? 'text-yellow-400' : 'text-gray-300'
                }`}
              >
                <Star className="w-6 h-6 fill-current" />
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tip Selection */}
      <div className="p-6">
        <h3 className="font-semibold text-gray-900 mb-4 text-center">
          Add a tip for {providerName}
        </h3>

        {/* Preset Tips */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          {presetTips.map((amount) => (
            <button
              key={amount}
              onClick={() => handleTipSelection(amount)}
              className={`p-3 rounded-lg border-2 text-center transition-all duration-200 ${
                selectedTip === amount
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-semibold">${amount}</div>
            </button>
          ))}
        </div>

        {/* Suggested Percentage Tips */}
        <div className="mb-4">
          <p className="text-xs text-gray-500 mb-2 text-center">Suggested based on service amount:</p>
          <div className="grid grid-cols-3 gap-3">
            {suggestedTips.map((amount, index) => (
              <button
                key={amount}
                onClick={() => handleTipSelection(amount)}
                className={`p-3 rounded-lg border-2 text-center transition-all duration-200 ${
                  selectedTip === amount
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-semibold">${amount.toFixed(2)}</div>
                <div className="text-xs text-gray-500">{tipPercentages[index]}%</div>
              </button>
            ))}
          </div>
        </div>

        {/* Custom Tip Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Or enter custom amount:
          </label>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={customTip}
              onChange={(e) => handleCustomTipChange(e.target.value)}
              placeholder="0.00"
              min="0"
              max="1000"
              step="0.01"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Tip Breakdown */}
        {finalTipAmount > 0 && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-sm space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-600">Tip Amount:</span>
                <span className="font-medium">${finalTipAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Platform Fee (5%):</span>
                <span className="font-medium">${tipCommission.toFixed(2)}</span>
              </div>
              <div className="flex justify-between border-t border-gray-200 pt-1">
                <span className="text-gray-600">Provider Receives:</span>
                <span className="font-semibold text-green-600">${providerReceives.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleSubmitTip}
            disabled={finalTipAmount <= 0 || isProcessing}
            className={`w-full py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
              finalTipAmount > 0 && !isProcessing
                ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white hover:from-green-600 hover:to-blue-600 transform hover:scale-105'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <CreditCard className="w-5 h-5" />
                <span>
                  {finalTipAmount > 0 
                    ? `Send $${finalTipAmount.toFixed(2)} Tip` 
                    : 'Select Tip Amount'
                  }
                </span>
              </>
            )}
          </button>

          <button
            onClick={onSkip}
            disabled={isProcessing}
            className="w-full py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Skip for Now
          </button>
        </div>

        {/* Info */}
        <div className="mt-4 text-xs text-gray-500 text-center">
          <p>Tips help support service providers and show appreciation for great service.</p>
          <p className="mt-1">A small platform fee helps us maintain secure payment processing.</p>
        </div>
      </div>
    </div>
  );
}
