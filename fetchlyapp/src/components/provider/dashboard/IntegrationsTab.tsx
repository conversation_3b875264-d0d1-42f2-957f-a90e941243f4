'use client';

import { useState } from 'react';
import { 
  ExternalLink, 
  Settings, 
  Check, 
  Plus,
  Calendar,
  CreditCard,
  MessageSquare,
  Mail,
  Camera,
  MapPin
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: any;
  category: string;
  connected: boolean;
  isPro: boolean;
}

export default function IntegrationsTab() {
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  const integrations: Integration[] = [
    {
      id: 'google-calendar',
      name: 'Google Calendar',
      description: 'Sync your bookings with Google Calendar',
      icon: Calendar,
      category: 'scheduling',
      connected: false,
      isPro: false
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Process payments securely',
      icon: CreditCard,
      category: 'payments',
      connected: true,
      isPro: false
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      description: 'Email marketing automation',
      icon: Mail,
      category: 'marketing',
      connected: false,
      isPro: true
    },
    {
      id: 'zoom',
      name: 'Zoom',
      description: 'Virtual consultations and meetings',
      icon: MessageSquare,
      category: 'communication',
      connected: false,
      isPro: true
    },
    {
      id: 'instagram',
      name: 'Instagram',
      description: 'Share your work on Instagram',
      icon: Camera,
      category: 'social',
      connected: false,
      isPro: true
    },
    {
      id: 'google-maps',
      name: 'Google Maps',
      description: 'Location services and directions',
      icon: MapPin,
      category: 'location',
      connected: true,
      isPro: false
    }
  ];

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'scheduling', name: 'Scheduling' },
    { id: 'payments', name: 'Payments' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'communication', name: 'Communication' },
    { id: 'social', name: 'Social Media' },
    { id: 'location', name: 'Location' }
  ];

  const filteredIntegrations = integrations.filter(integration => 
    categoryFilter === 'all' || integration.category === categoryFilter
  );

  return (
    <div className="flex flex-col items-center justify-center min-h-[40vh]">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">Integrations</h2>
      <p className="text-gray-600 text-lg">Integrations coming soon.</p>
    </div>
  );
}
