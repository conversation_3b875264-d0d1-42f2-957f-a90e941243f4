'use client';

import { useState, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Clock,
  MapPin,
  User,
  Phone,
  Mail,
  Check,
  X,
  Eye,
  ExternalLink,
  CalendarPlus,
  Settings
} from 'lucide-react';
import { Booking } from '@/lib/firebase/providers';
import { Timestamp } from 'firebase/firestore';

// Enhanced booking interface with all required fields
interface EnhancedBooking extends Booking {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  petName: string;
  petType: string; // Made required to match Booking interface
  serviceName: string; // Required since we ensure it's always set in getBookingsForDate
}

export default function CalendarTab() {
  const { bookings, isLoading, updateBookingStatus } = useProvider();
  const { user } = useAuth();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');
  const [selectedBooking, setSelectedBooking] = useState<EnhancedBooking | null>(null);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [calendarSyncEnabled, setCalendarSyncEnabled] = useState(false);
  const [showAddBooking, setShowAddBooking] = useState(false);
  const [isCreatingBooking, setIsCreatingBooking] = useState(false);

  // Handle booking approval
  const handleBookingApproval = async (bookingId: string, approved: boolean) => {
    try {
      const newStatus = approved ? 'confirmed' : 'cancelled';
      await updateBookingStatus?.(bookingId, newStatus);

      // If approved and calendar sync is enabled, add to calendar
      if (approved && calendarSyncEnabled) {
        await syncToCalendar(bookingId);
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  };

  // Sync booking to external calendar (Google Calendar integration)
  const syncToCalendar = async (bookingId: string) => {
    const booking = bookings?.find(b => b.id === bookingId);
    if (!booking) return;

    try {
      // Create calendar event
      const event = {
        summary: `${booking.customerName || 'Customer'} - ${booking.petName || 'Pet'}`,
        description: `Pet: ${booking.petName || 'N/A'}\nCustomer: ${booking.customerName || 'N/A'}\nPhone: ${booking.customerPhone || 'N/A'}\nEmail: ${booking.customerEmail || 'N/A'}\nService: ${'serviceName' in booking ? (booking as any).serviceName : 'Pet Service'}`,
        start: {
          dateTime: new Date(booking.date.toDate().toDateString() + ' ' + booking.startTime).toISOString(),
        },
        end: {
          dateTime: new Date(booking.date.toDate().toDateString() + ' ' + booking.endTime).toISOString(),
        },
        attendees: [
          { email: booking.customerEmail }
        ]
      };

      // This would integrate with Google Calendar API
      console.log('Syncing to calendar:', event);
      // TODO: Implement actual Google Calendar API integration
    } catch (error) {
      console.error('Error syncing to calendar:', error);
    }
  };

  // Get bookings for a specific date
  const getBookingsForDate = (date: Date): EnhancedBooking[] => {
    if (!bookings) return [];

    return bookings.filter(booking => {
      if (!booking.date) return false;

      const bookingDate = booking.date instanceof Timestamp
        ? booking.date.toDate()
        : new Date(booking.date);

      return (
        bookingDate.getDate() === date.getDate() &&
        bookingDate.getMonth() === date.getMonth() &&
        bookingDate.getFullYear() === date.getFullYear()
      );
    }).map(booking => {
      // Create a new object with all booking properties
      const enhancedBooking: EnhancedBooking = {
        ...booking,
        customerName: booking.customerName || 'Unknown Customer',
        customerEmail: booking.customerEmail || '',
        customerPhone: booking.customerPhone || '',
        petName: booking.petName || 'Pet',
        petType: booking.petType || 'Unknown',
        // Only include serviceName if it exists on the booking
        ...('serviceName' in booking ? { serviceName: (booking as any).serviceName } : { serviceName: 'Pet Service' })
      };
      return enhancedBooking;
    });
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const currentDay = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDay));
      currentDay.setDate(currentDay.getDate() + 1);
    }
    
    return days;
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const calendarDays = generateCalendarDays();
  const today = new Date();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-800 mb-4">Calendar Management</h1>
      {/* Calendar Header */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-800">
              {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateMonth('prev')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </button>
              <button
                onClick={() => navigateMonth('next')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronRight className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Calendar Sync Toggle */}
            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg">
              <CalendarPlus className="w-4 h-4 text-gray-600" />
              <span className="text-sm text-gray-700">Sync</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={calendarSyncEnabled}
                  onChange={(e) => setCalendarSyncEnabled(e.target.checked)}
                />
                <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex bg-gray-100 rounded-lg p-1">
              {(['month', 'week', 'day'] as const).map((mode) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === mode
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  {mode.charAt(0).toUpperCase() + mode.slice(1)}
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowAddBooking(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Booking</span>
            </button>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day headers */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-600">
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {calendarDays.map((day, index) => {
            const isCurrentMonth = day.getMonth() === currentDate.getMonth();
            const isToday = day.toDateString() === today.toDateString();
            const dayBookings = getBookingsForDate(day);
            const isSelected = selectedDate?.toDateString() === day.toDateString();
            
            return (
              <div
                key={index}
                onClick={() => setSelectedDate(day)}
                className={`min-h-[100px] p-2 border border-gray-100 cursor-pointer transition-colors ${
                  isCurrentMonth ? 'bg-white hover:bg-gray-50' : 'bg-gray-50 text-gray-400'
                } ${isToday ? 'ring-2 ring-blue-500' : ''} ${isSelected ? 'bg-blue-50' : ''}`}
              >
                <div className={`text-sm font-medium mb-1 ${isToday ? 'text-blue-600' : ''}`}>
                  {day.getDate()}
                </div>
                <div className="space-y-1">
                  {dayBookings.slice(0, 2).map((booking) => (
                    <div
                      key={booking.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedBooking(booking);
                        setShowBookingModal(true);
                      }}
                      className={`text-xs p-1 rounded border cursor-pointer hover:shadow-sm transition-shadow ${getStatusColor(booking.status)}`}
                    >
                      <div className="font-medium truncate">{booking.customerName}</div>
                      <div className="truncate">{booking.petName}</div>
                      <div className="truncate">{formatTime(booking.startTime)}</div>
                      {booking.status === 'pending' && (
                        <div className="flex items-center space-x-1 mt-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleBookingApproval(booking.id!, true);
                            }}
                            className="w-4 h-4 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600"
                          >
                            <Check className="w-2 h-2" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleBookingApproval(booking.id!, false);
                            }}
                            className="w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"
                          >
                            <X className="w-2 h-2" />
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                  {dayBookings.length > 2 && (
                    <div className="text-xs text-gray-500">
                      +{dayBookings.length - 2} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Selected Date Details */}
      {selectedDate && (
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Bookings for {selectedDate.toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </h3>
          
          {getBookingsForDate(selectedDate).length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No bookings for this date</p>
            </div>
          ) : (
            <div className="space-y-4">
              {getBookingsForDate(selectedDate).map((booking) => (
                <div key={booking.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-semibold text-gray-800">{booking.customerName}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <User className="w-4 h-4" />
                          <span>{booking.petName} ({booking.petType})</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4" />
                          <span>{formatTime(booking.startTime)} - {formatTime(booking.endTime)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="w-4 h-4" />
                          <span>{booking.customerPhone}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Mail className="w-4 h-4" />
                          <span>{booking.customerEmail}</span>
                        </div>
                      </div>
                      
                      {booking.notes && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-700">{booking.notes}</p>
                        </div>
                      )}
                    </div>
                    
                    <div className="text-right">
                      <p className="font-semibold text-gray-800">${booking.totalAmount}</p>
                      <p className="text-sm text-gray-600">{booking.duration} min</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Booking Details Modal */}
      {showBookingModal && selectedBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800">Booking Details</h3>
              <button
                onClick={() => setShowBookingModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Customer Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">Customer Information</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-700">{selectedBooking.customerName}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-700">{selectedBooking.customerEmail}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-700">{selectedBooking.customerPhone}</span>
                  </div>
                </div>
              </div>

              {/* Pet Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">Pet Information</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-700">Name: {selectedBooking.petName}</span>
                  </div>
                  {selectedBooking.petType && (
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-700">Type: {selectedBooking.petType}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Booking Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">Booking Information</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-700">
                      {selectedBooking.date.toDate().toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-700">
                      {formatTime(selectedBooking.startTime)} - {formatTime(selectedBooking.endTime)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-700">Service: {selectedBooking.serviceName}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-700">Amount: ${selectedBooking.totalAmount}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedBooking.status)}`}>
                      {selectedBooking.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {selectedBooking.notes && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-800 mb-3">Notes</h4>
                  <p className="text-gray-700">{selectedBooking.notes}</p>
                </div>
              )}

              {/* Action Buttons */}
              {selectedBooking.status === 'pending' && (
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => {
                      handleBookingApproval(selectedBooking.id!, true);
                      setShowBookingModal(false);
                    }}
                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <Check className="w-4 h-4" />
                    <span>Approve</span>
                  </button>
                  <button
                    onClick={() => {
                      handleBookingApproval(selectedBooking.id!, false);
                      setShowBookingModal(false);
                    }}
                    className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <X className="w-4 h-4" />
                    <span>Decline</span>
                  </button>
                </div>
              )}

              {/* Calendar Sync Button */}
              {selectedBooking.status === 'confirmed' && calendarSyncEnabled && (
                <button
                  onClick={() => syncToCalendar(selectedBooking.id!)}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>Sync to Calendar</span>
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Booking Modal */}
      {showAddBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-800">Add New Booking</h3>
              <button
                onClick={() => setShowAddBooking(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <div className="text-center py-8">
              <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-800 mb-2">Manual Booking Creation</h4>
              <p className="text-gray-600 mb-6">
                This feature allows providers to manually add bookings to their calendar.
                Typically, bookings are created by customers through the booking system.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Coming soon: Full manual booking creation form with customer details,
                service selection, and scheduling options.
              </p>
              <button
                onClick={() => setShowAddBooking(false)}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
