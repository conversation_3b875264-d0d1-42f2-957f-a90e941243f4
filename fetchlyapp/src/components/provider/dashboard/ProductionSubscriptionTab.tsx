"use client";

import { useState, useEffect } from "react";
import { Lock, Check, Crown, Shield, CreditCard, Zap, Star, TrendingUp, Calendar, X } from "lucide-react";
import { useProvider } from "@/contexts/ProviderContext";
import { SubscriptionService, ProviderSubscription } from "@/lib/stripe/subscription-service";
import { SUBSCRIPTION_TIERS } from "@/lib/stripe/client-config";
import toast from 'react-hot-toast';

export default function ProductionSubscriptionTab() {
  const { provider } = useProvider();
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [subscription, setSubscription] = useState<ProviderSubscription | null>(null);
  const [loading, setLoading] = useState(true);

  // Get current subscription status
  const currentTier = provider?.membershipTier || 'free';
  const isFreeTier = currentTier === 'free';
  const isProTier = currentTier === 'pro';
  const isPremiumTier = currentTier === 'premium';

  useEffect(() => {
    loadSubscriptionData();
  }, [provider]);

  const loadSubscriptionData = async () => {
    if (!provider?.id) return;
    
    try {
      const subscriptionData = await SubscriptionService.getSubscription(provider.id);
      setSubscription(subscriptionData);
    } catch (error) {
      console.error('Error loading subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (tier: 'pro' | 'premium') => {
    if (!provider?.id) {
      toast.error('Provider information not found');
      return;
    }

    setIsUpgrading(true);
    
    try {
      const result = await SubscriptionService.createSubscriptionCheckout(
        provider.id,
        tier,
        `${window.location.origin}/provider/dashboard?tab=subscription&success=true`,
        `${window.location.origin}/provider/dashboard?tab=subscription&cancelled=true`
      );

      if (result.success && result.url) {
        window.location.href = result.url;
      } else {
        throw new Error(result.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating subscription checkout:', error);
      toast.error('Failed to start subscription process. Please try again.');
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!provider?.id || !subscription) return;

    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    try {
      const result = await SubscriptionService.cancelSubscription(provider.id, true);
      if (result.success) {
        toast.success('Subscription cancelled. You will retain access until the end of your billing period.');
        loadSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Current Status */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
              !isFreeTier ? 'bg-gradient-to-br from-purple-500 to-blue-600' : 'bg-gray-100'
            }`}>
              {!isFreeTier ? (
                <Crown className="w-8 h-8 text-white" />
              ) : (
                <Lock className="w-8 h-8 text-gray-600" />
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">
                {isPremiumTier ? 'Premium Member' : isProTier ? 'Pro Member' : 'Free Plan'}
              </h2>
              <p className="text-gray-600">
                {!isFreeTier
                  ? 'You have access to premium features'
                  : 'Upgrade to unlock advanced features'
                }
              </p>
              {subscription && subscription.currentPeriodEnd && (
                <p className="text-sm text-gray-500 mt-1">
                  {subscription.cancelAtPeriodEnd ? 'Cancels on' : 'Renews on'}: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
          <div className="flex space-x-3">
            {isFreeTier && (
              <button
                onClick={() => handleUpgrade('pro')}
                disabled={isUpgrading}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
              >
                {isUpgrading ? 'Processing...' : 'Upgrade Now'}
              </button>
            )}
            {!isFreeTier && subscription && !subscription.cancelAtPeriodEnd && (
              <button
                onClick={handleCancelSubscription}
                className="bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors"
              >
                Cancel Subscription
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Subscription Plans */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Free Plan */}
        <div className={`relative rounded-2xl p-6 border-2 transition-all duration-200 ${
          isFreeTier
            ? 'border-blue-500 bg-blue-50 shadow-lg'
            : 'border-gray-200 bg-white hover:border-gray-300'
        }`}>
          {isFreeTier && (
            <div className="absolute -top-3 left-6 bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
              Current Plan
            </div>
          )}

          <div className="text-center mb-6">
            <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Shield className="w-6 h-6 text-gray-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-800">Free</h3>
            <p className="text-3xl font-bold text-gray-800">$0<span className="text-lg font-normal text-gray-600">/month</span></p>
          </div>

          <ul className="space-y-2 mb-6 text-sm">
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-green-600 flex-shrink-0" />
              <span className="text-gray-700">5 bookings/month limit</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-green-600 flex-shrink-0" />
              <span className="text-gray-700">Basic profile</span>
            </li>
            <li className="flex items-center space-x-2">
              <X className="w-4 h-4 text-red-500 flex-shrink-0" />
              <span className="text-gray-500">No analytics</span>
            </li>
            <li className="flex items-center space-x-2">
              <X className="w-4 h-4 text-red-500 flex-shrink-0" />
              <span className="text-gray-500">No calendar sync</span>
            </li>
          </ul>

          <button
            className={`w-full py-3 rounded-xl font-semibold transition-all duration-200 ${
              isFreeTier
                ? 'bg-blue-600 text-white cursor-default'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            disabled={isFreeTier}
          >
            {isFreeTier ? 'Current Plan' : 'Downgrade'}
          </button>
        </div>

        {/* Pro Plan */}
        <div className={`relative rounded-2xl p-6 border-2 transition-all duration-200 ${
          isProTier
            ? 'border-purple-500 bg-gradient-to-br from-purple-50 to-blue-50 shadow-lg'
            : 'border-purple-200 bg-white hover:border-purple-300 hover:shadow-md'
        }`}>
          {isProTier && (
            <div className="absolute -top-3 left-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
              Current Plan
            </div>
          )}
          <div className="absolute -top-3 right-6 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">
            POPULAR
          </div>

          <div className="text-center mb-6">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Star className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-800">Pro</h3>
            <p className="text-3xl font-bold text-gray-800">$9.99<span className="text-lg font-normal text-gray-600">/month</span></p>
          </div>

          <ul className="space-y-2 mb-6 text-sm">
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-purple-600 flex-shrink-0" />
              <span className="text-gray-700">Unlimited bookings</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-purple-600 flex-shrink-0" />
              <span className="text-gray-700">Advanced analytics</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-purple-600 flex-shrink-0" />
              <span className="text-gray-700">Calendar sync</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-purple-600 flex-shrink-0" />
              <span className="text-gray-700">Priority search placement</span>
            </li>
          </ul>

          <button
            onClick={isProTier ? undefined : () => handleUpgrade('pro')}
            disabled={isProTier || isUpgrading}
            className={`w-full py-3 rounded-xl font-semibold transition-all duration-200 ${
              isProTier
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white cursor-default'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 transform hover:scale-105'
            }`}
          >
            {isProTier ? 'Current Plan' : isUpgrading ? 'Processing...' : 'Upgrade to Pro'}
          </button>
        </div>

        {/* Premium Plan */}
        <div className={`relative rounded-2xl p-6 border-2 transition-all duration-200 ${
          isPremiumTier
            ? 'border-yellow-500 bg-gradient-to-br from-yellow-50 to-orange-50 shadow-lg'
            : 'border-yellow-200 bg-white hover:border-yellow-300 hover:shadow-md'
        }`}>
          {isPremiumTier && (
            <div className="absolute -top-3 left-6 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-1 rounded-full text-sm font-medium">
              Current Plan
            </div>
          )}
          <div className="absolute -top-3 right-6 bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold">
            PREMIUM
          </div>

          <div className="text-center mb-6">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-800">Premium</h3>
            <p className="text-3xl font-bold text-gray-800">$29.99<span className="text-lg font-normal text-gray-600">/month</span></p>
          </div>

          <ul className="space-y-2 mb-6 text-sm">
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-yellow-600 flex-shrink-0" />
              <span className="text-gray-700">Everything in Pro</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-yellow-600 flex-shrink-0" />
              <span className="text-gray-700">Homepage featured listing</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-yellow-600 flex-shrink-0" />
              <span className="text-gray-700">SMS alerts</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-yellow-600 flex-shrink-0" />
              <span className="text-gray-700">Custom URL</span>
            </li>
            <li className="flex items-center space-x-2">
              <Check className="w-4 h-4 text-yellow-600 flex-shrink-0" />
              <span className="text-gray-700">Weekly free boosts</span>
            </li>
          </ul>

          <button
            onClick={isPremiumTier ? undefined : () => handleUpgrade('premium')}
            disabled={isPremiumTier || isUpgrading}
            className={`w-full py-3 rounded-xl font-semibold transition-all duration-200 ${
              isPremiumTier
                ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white cursor-default'
                : 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600 transform hover:scale-105'
            }`}
          >
            {isPremiumTier ? 'Current Plan' : isUpgrading ? 'Processing...' : 'Upgrade to Premium'}
          </button>
        </div>
      </div>

      {/* Billing Information */}
      {subscription && !isFreeTier && (
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Billing Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm text-gray-600">Current Plan</p>
              <p className="font-semibold text-gray-900 capitalize">{subscription.tier}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className={`font-semibold capitalize ${
                subscription.status === 'active' ? 'text-green-600' : 'text-red-600'
              }`}>
                {subscription.status}
              </p>
            </div>
            {subscription.currentPeriodEnd && (
              <div>
                <p className="text-sm text-gray-600">
                  {subscription.cancelAtPeriodEnd ? 'Cancels On' : 'Next Billing Date'}
                </p>
                <p className="font-semibold text-gray-900">
                  {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                </p>
              </div>
            )}
            <div>
              <p className="text-sm text-gray-600">Bookings This Month</p>
              <p className="font-semibold text-gray-900">{subscription.bookingsThisMonth}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
