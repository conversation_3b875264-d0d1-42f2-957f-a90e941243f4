'use client';

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { X, CreditCard, Wallet, Lock, Check } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  providerId: string;
  serviceId: string;
  description: string;
  onSuccess: (paymentResult: any) => void;
}

interface PaymentFormProps {
  amount: number;
  providerId: string;
  serviceId: string;
  description: string;
  onSuccess: (paymentResult: any) => void;
  onClose: () => void;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  amount,
  providerId,
  serviceId,
  description,
  onSuccess,
  onClose,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const { user, getIdToken } = useAuth();
  
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'wallet'>('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [walletBalance, setWalletBalance] = useState(0);

  useEffect(() => {
    // Fetch user's wallet balance
    if (user) {
      setWalletBalance(user.fetchlyBalance || 0);
    }
  }, [user]);

  const handleCardPayment = async () => {
    if (!stripe || !elements) return;

    setIsProcessing(true);
    setError(null);

    try {
      const token = await getIdToken();
      
      // Create payment intent
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          amount,
          providerId,
          serviceId,
          description,
        }),
      });

      const { clientSecret, error: apiError } = await response.json();

      if (apiError) {
        setError(apiError);
        return;
      }

      // Confirm payment
      const cardElement = elements.getElement(CardElement);
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: cardElement!,
            billing_details: {
              name: user?.name,
              email: user?.email,
            },
          },
        }
      );

      if (stripeError) {
        setError(stripeError.message || 'Payment failed');
      } else if (paymentIntent.status === 'succeeded') {
        onSuccess({
          type: 'card',
          paymentIntentId: paymentIntent.id,
          amount,
        });
      }
    } catch (err: any) {
      setError(err.message || 'Payment failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleWalletPayment = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      const token = await getIdToken();
      
      const response = await fetch('/api/wallet/pay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          amount,
          providerId,
          serviceId,
          description,
        }),
      });

      const result = await response.json();

      if (result.error) {
        setError(result.error);
      } else {
        onSuccess({
          type: 'wallet',
          transferId: result.transferId,
          newBalance: result.newBalance,
          amount,
        });
      }
    } catch (err: any) {
      setError(err.message || 'Payment failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (paymentMethod === 'card') {
      await handleCardPayment();
    } else {
      await handleWalletPayment();
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
    },
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Complete Payment</h2>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-6">
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 border border-green-200">
          <h3 className="font-semibold text-gray-800 mb-2">{description}</h3>
          <p className="text-3xl font-bold text-green-600">${amount.toFixed(2)}</p>
        </div>
      </div>

      {/* Payment Method Selection */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Payment Method</h3>
        <div className="grid grid-cols-2 gap-4">
          <button
            type="button"
            onClick={() => setPaymentMethod('card')}
            className={`p-4 rounded-xl border-2 transition-all ${
              paymentMethod === 'card'
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 hover:border-green-300'
            }`}
          >
            <CreditCard className="w-6 h-6 mx-auto mb-2 text-green-600" />
            <div className="text-sm font-medium">Credit Card</div>
          </button>
          
          <button
            type="button"
            onClick={() => setPaymentMethod('wallet')}
            disabled={walletBalance < amount}
            className={`p-4 rounded-xl border-2 transition-all ${
              paymentMethod === 'wallet'
                ? 'border-blue-500 bg-blue-50'
                : walletBalance < amount
                ? 'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed'
                : 'border-gray-200 hover:border-blue-300'
            }`}
          >
            <Wallet className="w-6 h-6 mx-auto mb-2 text-blue-600" />
            <div className="text-sm font-medium">Fetchly Wallet</div>
            <div className="text-xs text-gray-500 mt-1">
              Balance: ${walletBalance.toFixed(2)}
            </div>
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {paymentMethod === 'card' && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Card Details
            </label>
            <div className="p-4 border border-gray-300 rounded-xl bg-white">
              <CardElement options={cardElementOptions} />
            </div>
          </div>
        )}

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
          <Lock className="w-4 h-4" />
          <span>Your payment information is secure and encrypted</span>
        </div>

        <button
          type="submit"
          disabled={isProcessing || (!stripe && paymentMethod === 'card')}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {isProcessing ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Processing...
            </>
          ) : (
            <>
              <Check className="w-5 h-5" />
              Pay ${amount.toFixed(2)}
            </>
          )}
        </button>
      </form>
    </div>
  );
};

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  amount,
  providerId,
  serviceId,
  description,
  onSuccess,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <Elements stripe={stripePromise}>
          <PaymentForm
            amount={amount}
            providerId={providerId}
            serviceId={serviceId}
            description={description}
            onSuccess={onSuccess}
            onClose={onClose}
          />
        </Elements>
      </div>
    </div>
  );
};

export default PaymentModal;
