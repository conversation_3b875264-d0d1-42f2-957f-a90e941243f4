'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    petName: '<PERSON>',
    petType: 'Golden Retriever',
    rating: 5,
    text: 'Fetchly made finding a reliable groomer so easy! <PERSON> looks amazing and the booking process was seamless. The provider sent photos throughout the grooming session which gave me peace of mind.',
    service: 'Pet Grooming',
    location: 'Seattle, WA',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 2,
    name: '<PERSON>',
    petName: '<PERSON>',
    petType: 'Persian Cat',
    rating: 5,
    text: 'When <PERSON> needed emergency care at 2 AM, <PERSON><PERSON><PERSON> helped me find a 24/7 vet clinic nearby. The staff was incredible and <PERSON> is now healthy and happy. Thank you for being there when we needed you most!',
    service: 'Emergency Veterinary',
    location: 'Portland, OR',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 3,
    name: '<PERSON>',
    petName: '<PERSON> & <PERSON>',
    petType: 'Mixed Breeds',
    rating: 5,
    text: 'As a busy working mom, finding reliable pet daycare was crucial. The provider I found through Fetchly sends daily updates and photos. My dogs love going there and I have complete peace of mind.',
    service: 'Pet Daycare',
    location: 'San Francisco, CA',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 4,
    name: 'David Thompson',
    petName: 'Charlie',
    petType: 'Labrador Mix',
    rating: 5,
    text: 'Charlie was having behavioral issues and I was at my wit\'s end. The trainer I found through Fetchly was amazing! Charlie is now well-behaved and we both learned so much. Highly recommend!',
    service: 'Pet Training',
    location: 'Austin, TX',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 5,
    name: 'Lisa Park',
    petName: 'Mochi',
    petType: 'Shiba Inu',
    rating: 5,
    text: 'Going on vacation used to be stressful because of Mochi. The pet hotel I booked through Fetchly was like a luxury resort for dogs! Daily photos, playtime, and excellent care. Mochi didn\'t want to leave!',
    service: 'Pet Hotel',
    location: 'Los Angeles, CA',
    avatar: '👩‍🎨'
  }
];

export function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-advance testimonials
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
            What Pet Parents Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join thousands of happy pet parents who trust Fetchly to connect them
            with the best pet care professionals in their area
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="relative">
            {/* Navigation Buttons */}
            <button
              onClick={prevTestimonial}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 p-3 bg-gradient-to-r from-green-600 to-blue-600 text-white shadow-lg rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-200"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>

            <button
              onClick={nextTestimonial}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 p-3 bg-gradient-to-r from-green-600 to-blue-600 text-white shadow-lg rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-200"
            >
              <ChevronRight className="w-6 h-6" />
            </button>

            {/* Testimonial Card */}
            <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-3xl p-8 md:p-12 text-center relative overflow-hidden text-white">
              {/* Background Quote */}
              <Quote className="absolute top-8 left-8 w-16 h-16 text-white opacity-20" />

              {/* Rating */}
              <div className="flex justify-center mb-6">
                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-xl md:text-2xl text-white leading-relaxed mb-8 font-medium">
                "{testimonials[currentIndex].text}"
              </blockquote>

              {/* Author Info */}
              <div className="flex flex-col md:flex-row items-center justify-center gap-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden border-4 border-white/30">
                    <img
                      src={testimonials[currentIndex].avatar}
                      alt={testimonials[currentIndex].name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-white">{testimonials[currentIndex].name}</div>
                    <div className="text-green-100">
                      {testimonials[currentIndex].petName} ({testimonials[currentIndex].petType})
                    </div>
                  </div>
                </div>

                <div className="hidden md:block w-px h-12 bg-white/30"></div>

                <div className="text-center md:text-left">
                  <div className="font-medium text-yellow-300">{testimonials[currentIndex].service}</div>
                  <div className="text-green-100 text-sm">{testimonials[currentIndex].location}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 gap-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? 'bg-gradient-to-r from-green-600 to-blue-600 scale-125'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="w-16 h-16 bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Star className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="font-bold text-gray-800 mb-2">4.9/5 Average Rating</h3>
            <p className="text-gray-600 text-sm">Based on 10,000+ verified reviews</p>
          </div>

          <div className="text-center bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <div className="text-2xl">🛡️</div>
            </div>
            <h3 className="font-bold text-gray-800 mb-2">100% Verified Providers</h3>
            <p className="text-gray-600 text-sm">All providers are background checked</p>
          </div>

          <div className="text-center bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="w-16 h-16 bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <div className="text-2xl">💝</div>
            </div>
            <h3 className="font-bold text-gray-800 mb-2">Satisfaction Guarantee</h3>
            <p className="text-gray-600 text-sm">Your happiness is our priority</p>
          </div>
        </div>
      </div>
    </section>
  );
}
