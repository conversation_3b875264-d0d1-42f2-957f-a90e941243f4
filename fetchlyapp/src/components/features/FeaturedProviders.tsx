'use client';

import { <PERSON>, MapPin, Clock, Heart, Award } from 'lucide-react';
import Link from 'next/link';

const featuredProviders = [
  {
    id: '1',
    name: 'Paws & Claws Grooming',
    type: 'Grooming Salon',
    rating: 4.9,
    reviewCount: 234,
    location: 'Downtown, Seattle',
    distance: '0.8 miles',
    image: 'https://images.unsplash.com/photo-**********-8cc77767d783?w=400&h=300&fit=crop&crop=center',
    specialties: ['Full Service Grooming', 'Nail Care', 'Teeth Cleaning'],
    price: '$$',
    availability: 'Available Today',
    verified: true,
    featured: true
  },
  {
    id: '2',
    name: 'Seattle Pet Hospital',
    type: 'Veterinary Clinic',
    rating: 4.8,
    reviewCount: 456,
    location: 'Capitol Hill, Seattle',
    distance: '1.2 miles',
    image: 'https://images.unsplash.com/photo-**********-2a8555f1a136?w=400&h=300&fit=crop&crop=center',
    specialties: ['Emergency Care', 'Surgery', 'Wellness Exams'],
    price: '$$$',
    availability: '24/7 Emergency',
    verified: true,
    featured: true
  },
  {
    id: '3',
    name: 'Happy Tails Pet Hotel',
    type: 'Pet Boarding',
    rating: 4.7,
    reviewCount: 189,
    location: 'Bellevue, WA',
    distance: '2.1 miles',
    image: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center',
    specialties: ['Luxury Suites', 'Play Areas', 'Daily Updates'],
    price: '$$',
    availability: 'Booking Available',
    verified: true,
    featured: false
  },
  {
    id: '4',
    name: 'Pawsome Training Academy',
    type: 'Pet Training',
    rating: 4.9,
    reviewCount: 167,
    location: 'Fremont, Seattle',
    distance: '1.8 miles',
    image: 'https://images.unsplash.com/photo-**********-71594a27632d?w=400&h=300&fit=crop&crop=center',
    specialties: ['Puppy Classes', 'Behavior Training', 'Agility'],
    price: '$$',
    availability: 'Next Class: Mon',
    verified: true,
    featured: false
  }
];

export function FeaturedProviders() {
  return (
    <section className="py-20 bg-gradient-to-r from-green-600 to-blue-600">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
            Featured Pet Care Providers
          </h2>
          <p className="text-xl text-green-100 max-w-3xl mx-auto">
            Discover top-rated, verified professionals in your area who are passionate
            about providing exceptional care for your furry friends
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 mb-12">
          {featuredProviders.map((provider) => (
            <Link
              key={provider.id}
              href={`/provider/${provider.id}`}
              className="group"
            >
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 hover:bg-white transition-all duration-300 group-hover:scale-[1.02] group-hover:shadow-xl">
                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={provider.image}
                    alt={provider.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  
                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex gap-2">
                    {provider.verified && (
                      <span className="bg-green-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                        <Award className="w-3 h-3" />
                        Verified
                      </span>
                    )}
                    {provider.featured && (
                      <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                        Featured
                      </span>
                    )}
                  </div>

                  {/* Favorite Button */}
                  <button className="absolute top-4 right-4 p-2 bg-white/80 hover:bg-white rounded-full transition-colors duration-200">
                    <Heart className="w-4 h-4 text-gray-600 hover:text-red-500" />
                  </button>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Header */}
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 group-hover:text-gray-900 mb-1">
                        {provider.name}
                      </h3>
                      <p className="text-sm text-gray-600">{provider.type}</p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1 mb-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="font-semibold text-gray-800">{provider.rating}</span>
                      </div>
                      <p className="text-xs text-gray-500">({provider.reviewCount} reviews)</p>
                    </div>
                  </div>

                  {/* Location & Distance */}
                  <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      <span>{provider.location}</span>
                    </div>
                    <span className="text-green-600 font-medium">{provider.distance}</span>
                  </div>

                  {/* Specialties */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {provider.specialties.slice(0, 3).map((specialty, index) => (
                        <span
                          key={index}
                          className="text-xs bg-gradient-to-r from-green-100 to-blue-100 text-green-700 px-2 py-1 rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Bottom Info */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                    <div className="flex items-center gap-4">
                      <span className="text-sm font-medium text-gray-700">{provider.price}</span>
                      <div className="flex items-center gap-1 text-sm text-green-600">
                        <Clock className="w-4 h-4" />
                        <span>{provider.availability}</span>
                      </div>
                    </div>

                    <span className="text-sm font-medium text-blue-600 group-hover:text-green-600 group-hover:underline transition-colors duration-300">
                      View Details →
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link
            href="/search"
            className="bg-white text-green-600 px-8 py-4 rounded-xl hover:bg-gray-100 transition-all duration-300 font-semibold inline-flex items-center gap-2 text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            View All Providers
            <span className="text-xl">→</span>
          </Link>
        </div>

        {/* Stats Section */}
        <div className="mt-20 glass-card rounded-2xl p-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-primary-600 mb-2">2,500+</div>
              <div className="text-gray-600">Verified Providers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-warm-600 mb-2">50,000+</div>
              <div className="text-gray-600">Happy Pets</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-golden-600 mb-2">4.8★</div>
              <div className="text-gray-600">Average Rating</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-accent-600 mb-2">24/7</div>
              <div className="text-gray-600">Support Available</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
