'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Camera, 
  Heart, 
  Calendar,
  Weight,
  Stethoscope,
  Shield,
  MapPin,
  Phone,
  User,
  X
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { Pet, Vaccination, Medication, VetInfo } from '@/types/user';
import { PetService } from '@/lib/firestore';
import { useAuth } from '@/contexts/AuthContext';
import { Timestamp } from 'firebase/firestore';

interface PetProfilesProps {
  pets: Pet[];
  onPetsUpdate: (pets: Pet[]) => void;
}

export default function PetProfiles({ pets, onPetsUpdate }: PetProfilesProps) {
  const { user } = useAuth();
  const [showAddPet, setShowAddPet] = useState(false);
  const [editingPet, setEditingPet] = useState<Pet | null>(null);
  const [selectedPet, setSelectedPet] = useState<Pet | null>(null);
  const [loading, setLoading] = useState(false);

  const [petForm, setPetForm] = useState({
    name: '',
    type: 'Dog',
    breed: '',
    dateOfBirth: '',
    weight: '',
    color: '',
    gender: 'male' as 'male' | 'female',
    microchipId: '',
    photo: '',
    allergies: [] as string[],
    medicalNotes: '',
    vetInfo: {
      name: '',
      phone: '',
      address: '',
      email: '',
    },
  });

  const resetForm = () => {
    setPetForm({
      name: '',
      type: 'Dog',
      breed: '',
      dateOfBirth: '',
      weight: '',
      color: '',
      gender: 'male',
      microchipId: '',
      photo: '',
      allergies: [],
      medicalNotes: '',
      vetInfo: {
        name: '',
        phone: '',
        address: '',
        email: '',
      },
    });
  };

  const handleAddPet = () => {
    resetForm();
    setEditingPet(null);
    setShowAddPet(true);
  };

  const handleEditPet = (pet: Pet) => {
    setPetForm({
      name: pet.name,
      type: pet.type,
      breed: pet.breed,
      dateOfBirth: pet.dateOfBirth.toDate().toISOString().split('T')[0],
      weight: pet.weight.toString(),
      color: pet.color,
      gender: pet.gender,
      microchipId: pet.microchipId || '',
      photo: pet.photo || '',
      allergies: pet.allergies,
      medicalNotes: pet.medicalNotes,
      vetInfo: {
        ...pet.vetInfo,
        email: pet.vetInfo.email || '',
      },
    });
    setEditingPet(pet);
    setShowAddPet(true);
  };

  const handleSavePet = async () => {
    if (!user) return;

    try {
      setLoading(true);

      const petData = {
        userId: user.id,
        name: petForm.name,
        type: petForm.type,
        breed: petForm.breed,
        dateOfBirth: Timestamp.fromDate(new Date(petForm.dateOfBirth)),
        weight: parseFloat(petForm.weight),
        color: petForm.color,
        gender: petForm.gender,
        microchipId: petForm.microchipId,
        photo: petForm.photo,
        vaccinations: editingPet?.vaccinations || [],
        allergies: petForm.allergies,
        medications: editingPet?.medications || [],
        medicalNotes: petForm.medicalNotes,
        vetInfo: petForm.vetInfo,
      };

      if (editingPet) {
        // Update existing pet
        await PetService.updatePet(editingPet.id, petData);
        const updatedPets = pets.map(pet => 
          pet.id === editingPet.id 
            ? { ...pet, ...petData, updatedAt: Timestamp.now() }
            : pet
        );
        onPetsUpdate(updatedPets);
      } else {
        // Create new pet
        const petId = await PetService.createPet(petData);
        const newPet: Pet = {
          id: petId,
          ...petData,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        };
        onPetsUpdate([...pets, newPet]);
      }

      setShowAddPet(false);
      resetForm();
      setEditingPet(null);
    } catch (error) {
      console.error('Error saving pet:', error);
      alert('Failed to save pet. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePet = async (petId: string) => {
    if (!confirm('Are you sure you want to delete this pet profile?')) return;

    try {
      setLoading(true);
      await PetService.deletePet(petId);
      onPetsUpdate(pets.filter(pet => pet.id !== petId));
    } catch (error) {
      console.error('Error deleting pet:', error);
      alert('Failed to delete pet. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (dateOfBirth: Timestamp) => {
    const today = new Date();
    const birthDate = dateOfBirth.toDate();
    const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + 
                       (today.getMonth() - birthDate.getMonth());
    
    if (ageInMonths < 12) {
      return `${ageInMonths} months`;
    } else {
      const years = Math.floor(ageInMonths / 12);
      const months = ageInMonths % 12;
      return months > 0 ? `${years}y ${months}m` : `${years} years`;
    }
  };

  const addAllergy = () => {
    const allergy = prompt('Enter allergy:');
    if (allergy && !petForm.allergies.includes(allergy)) {
      setPetForm({
        ...petForm,
        allergies: [...petForm.allergies, allergy]
      });
    }
  };

  const removeAllergy = (index: number) => {
    setPetForm({
      ...petForm,
      allergies: petForm.allergies.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-pink-500 to-rose-500 p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Heart className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">My Pets</h3>
              <p className="text-pink-100 text-sm">{pets.length} furry friends</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleAddPet}
            className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            <Plus className="w-5 h-5" />
            <span>Add Pet</span>
          </motion.button>
        </div>
      </div>

      {/* Pet Grid */}
      <div className="p-6">
        {pets.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {pets.map((pet) => (
              <motion.div
                key={pet.id}
                whileHover={{ scale: 1.02 }}
                className="bg-gray-50 rounded-xl p-4 cursor-pointer hover:shadow-md transition-all"
                onClick={() => setSelectedPet(pet)}
              >
                <div className="relative mb-3">
                  <div className="w-20 h-20 mx-auto rounded-full overflow-hidden bg-gray-200">
                    {pet.photo ? (
                      <Image
                        src={pet.photo}
                        alt={pet.name}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <Heart className="w-8 h-8" />
                      </div>
                    )}
                  </div>
                  <div className="absolute top-0 right-0 flex space-x-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditPet(pet);
                      }}
                      className="p-1 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors"
                    >
                      <Edit3 className="w-3 h-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeletePet(pet.id);
                      }}
                      className="p-1 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
                
                <div className="text-center">
                  <h4 className="font-semibold text-gray-900">{pet.name}</h4>
                  <p className="text-sm text-gray-600">{pet.breed}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {calculateAge(pet.dateOfBirth)} • {pet.weight} lbs
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Heart className="w-16 h-16 mx-auto text-gray-300 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No pets yet</h4>
            <p className="text-gray-600 mb-4">Add your first furry friend to get started!</p>
            <button
              onClick={handleAddPet}
              className="inline-flex items-center space-x-2 bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              <Plus className="w-5 h-5" />
              <span>Add Your First Pet</span>
            </button>
          </div>
        )}
      </div>

      {/* Add/Edit Pet Modal */}
      <AnimatePresence>
        {showAddPet && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowAddPet(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">
                  {editingPet ? 'Edit Pet' : 'Add New Pet'}
                </h3>
                <button
                  onClick={() => setShowAddPet(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Basic Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Name *
                      </label>
                      <input
                        type="text"
                        value={petForm.name}
                        onChange={(e) => setPetForm({ ...petForm, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Pet's name"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Type *
                      </label>
                      <select
                        value={petForm.type}
                        onChange={(e) => setPetForm({ ...petForm, type: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      >
                        <option value="Dog">Dog</option>
                        <option value="Cat">Cat</option>
                        <option value="Bird">Bird</option>
                        <option value="Rabbit">Rabbit</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Breed
                      </label>
                      <input
                        type="text"
                        value={petForm.breed}
                        onChange={(e) => setPetForm({ ...petForm, breed: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Breed"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date of Birth *
                      </label>
                      <input
                        type="date"
                        value={petForm.dateOfBirth}
                        onChange={(e) => setPetForm({ ...petForm, dateOfBirth: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Weight (lbs)
                      </label>
                      <input
                        type="number"
                        value={petForm.weight}
                        onChange={(e) => setPetForm({ ...petForm, weight: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Weight in pounds"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Gender
                      </label>
                      <select
                        value={petForm.gender}
                        onChange={(e) => setPetForm({ ...petForm, gender: e.target.value as 'male' | 'female' })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      >
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Allergies */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">Allergies</h4>
                    <button
                      type="button"
                      onClick={addAllergy}
                      className="text-sm text-pink-600 hover:text-pink-700 font-medium"
                    >
                      + Add Allergy
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {petForm.allergies.map((allergy, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center space-x-1 bg-red-100 text-red-800 text-sm px-2 py-1 rounded-full"
                      >
                        <span>{allergy}</span>
                        <button
                          onClick={() => removeAllergy(index)}
                          className="hover:text-red-900"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                {/* Veterinarian Information */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Veterinarian Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Vet Name
                      </label>
                      <input
                        type="text"
                        value={petForm.vetInfo.name}
                        onChange={(e) => setPetForm({
                          ...petForm,
                          vetInfo: { ...petForm.vetInfo, name: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Dr. Smith"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={petForm.vetInfo.phone}
                        onChange={(e) => setPetForm({
                          ...petForm,
                          vetInfo: { ...petForm.vetInfo, phone: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="(*************"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address
                      </label>
                      <input
                        type="text"
                        value={petForm.vetInfo.address}
                        onChange={(e) => setPetForm({
                          ...petForm,
                          vetInfo: { ...petForm.vetInfo, address: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="123 Main St, City, State"
                      />
                    </div>
                  </div>
                </div>

                {/* Medical Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Medical Notes
                  </label>
                  <textarea
                    value={petForm.medicalNotes}
                    onChange={(e) => setPetForm({ ...petForm, medicalNotes: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    placeholder="Any special medical conditions or notes..."
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={handleSavePet}
                  disabled={loading || !petForm.name || !petForm.dateOfBirth}
                  className="flex-1 bg-pink-600 hover:bg-pink-700 disabled:bg-gray-300 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  {loading ? 'Saving...' : editingPet ? 'Update Pet' : 'Add Pet'}
                </button>
                <button
                  onClick={() => setShowAddPet(false)}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Pet Detail Modal */}
      <AnimatePresence>
        {selectedPet && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedPet(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">{selectedPet.name}</h3>
                <button
                  onClick={() => setSelectedPet(null)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto rounded-full overflow-hidden bg-gray-200 mb-3">
                    {selectedPet.photo ? (
                      <Image
                        src={selectedPet.photo}
                        alt={selectedPet.name}
                        width={96}
                        height={96}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <Heart className="w-12 h-12" />
                      </div>
                    )}
                  </div>
                  <h4 className="text-lg font-semibold">{selectedPet.name}</h4>
                  <p className="text-gray-600">{selectedPet.breed} • {selectedPet.type}</p>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span>{calculateAge(selectedPet.dateOfBirth)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Weight className="w-4 h-4 text-gray-400" />
                    <span>{selectedPet.weight} lbs</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="capitalize">{selectedPet.gender}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-gray-400" />
                    <span>{selectedPet.microchipId || 'No chip'}</span>
                  </div>
                </div>

                {selectedPet.allergies.length > 0 && (
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Allergies</h5>
                    <div className="flex flex-wrap gap-1">
                      {selectedPet.allergies.map((allergy, index) => (
                        <span
                          key={index}
                          className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full"
                        >
                          {allergy}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {selectedPet.vetInfo.name && (
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Veterinarian</h5>
                    <div className="bg-gray-50 rounded-lg p-3 space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <Stethoscope className="w-4 h-4 text-gray-400" />
                        <span>{selectedPet.vetInfo.name}</span>
                      </div>
                      {selectedPet.vetInfo.phone && (
                        <div className="flex items-center space-x-2">
                          <Phone className="w-4 h-4 text-gray-400" />
                          <span>{selectedPet.vetInfo.phone}</span>
                        </div>
                      )}
                      {selectedPet.vetInfo.address && (
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span>{selectedPet.vetInfo.address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {selectedPet.medicalNotes && (
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Medical Notes</h5>
                    <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                      {selectedPet.medicalNotes}
                    </p>
                  </div>
                )}
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => {
                    setSelectedPet(null);
                    handleEditPet(selectedPet);
                  }}
                  className="flex-1 bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  Edit Profile
                </button>
                <button
                  onClick={() => setSelectedPet(null)}
                  className="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                >
                  Close
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
