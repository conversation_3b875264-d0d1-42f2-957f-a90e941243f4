'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  X,
  Heart,
  MessageCircle,
  Send,
  Reply,
  Trash2
} from 'lucide-react';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  Timestamp,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';
import NoSSR from './NoSSR';
import { translateSpanishToEnglish, needsTranslation } from '@/lib/translation';

interface Comment {
  id: string;
  postId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  userRole: 'provider' | 'petowner';
  content: string;
  timestamp: Date;
  likes: number;
  likedBy: string[];
  parentId?: string; // For replies
  replies?: Comment[];
}

interface CommentsModalProps {
  postId: string;
  postAuthor: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function CommentsModal({ postId, isOpen, onClose }: CommentsModalProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [translatedComments, setTranslatedComments] = useState<{ [key: string]: string }>({});
  const [showTranslation, setShowTranslation] = useState<{ [key: string]: boolean }>({});

  // Load comments
  useEffect(() => {
    if (!isOpen || !postId) return;

    // If user is not authenticated, don't load comments
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    const commentsQuery = query(
      collection(db, 'comments'),
      where('postId', '==', postId),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(commentsQuery, (snapshot) => {
      // Use a Set to track processed comment IDs to prevent duplicates
      const processedIds = new Set();
      const commentsData: Comment[] = [];
      
      snapshot.docChanges().forEach((change) => {
        // Only process added or modified comments
        if (change.type === 'added' || change.type === 'modified') {
          const commentId = change.doc.id;
          if (!processedIds.has(commentId)) {
            processedIds.add(commentId);
            commentsData.push({
              id: commentId,
              ...change.doc.data(),
              timestamp: change.doc.data().timestamp?.toDate() || new Date()
            } as Comment);
          }
        }
      });

      // Organize comments and replies
      const topLevelComments = commentsData
        .filter(comment => !comment.parentId)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        
      const repliesMap = commentsData
        .filter(comment => comment.parentId)
        .reduce((acc, reply) => {
          if (!acc[reply.parentId!]) {
            acc[reply.parentId!] = [];
          }
          acc[reply.parentId!].push(reply);
          return acc;
        }, {} as Record<string, Comment[]>);

      // Sort replies by timestamp (newest first)
      Object.values(repliesMap).forEach(replies => {
        replies.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      });

      const commentsWithReplies = topLevelComments.map(comment => ({
        ...comment,
        replies: repliesMap[comment.id] || []
      }));

      setComments(commentsWithReplies);
      setLoading(false);
    }, (error) => {
      console.error('Error loading comments:', error);
      toast.error('Failed to load comments');
      setLoading(false);
    });

    return () => unsubscribe();
  }, [isOpen, postId]);

  // Submit new comment
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !user || submitting) return;

    try {
      setSubmitting(true);
      
      const commentData = {
        postId,
        userId: user.id,
        userName: user.name || 'Anonymous',
        userAvatar: user.avatar || '/favicon.png',
        userRole: user.role || 'petowner',
        content: newComment.trim(),
        timestamp: Timestamp.now(),
        likes: 0,
        likedBy: []
      };

      await addDoc(collection(db, 'comments'), commentData);
      
      // Update post comment count
      const postRef = doc(db, 'posts', postId);
      await updateDoc(postRef, {
        comments: increment(1)
      });

      setNewComment('');
      toast.success('Comment added!');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    } finally {
      setSubmitting(false);
    }
  };

  // Submit reply
  const handleSubmitReply = async (parentId: string) => {
    if (!replyContent.trim() || !user || submitting) return;

    try {
      setSubmitting(true);
      
      const replyData = {
        postId,
        userId: user.id,
        userName: user.name || 'Anonymous',
        userAvatar: user.avatar || '/favicon.png',
        userRole: user.role || 'petowner',
        content: replyContent.trim(),
        timestamp: Timestamp.now(),
        likes: 0,
        likedBy: [],
        parentId
      };

      await addDoc(collection(db, 'comments'), replyData);
      
      setReplyContent('');
      setReplyingTo(null);
      toast.success('Reply added!');
    } catch (error) {
      console.error('Error adding reply:', error);
      toast.error('Failed to add reply');
    } finally {
      setSubmitting(false);
    }
  };

  // Like comment
  const handleLikeComment = async (commentId: string) => {
    if (!user) return;

    try {
      const comment = comments.find(c => c.id === commentId) || 
                    comments.flatMap(c => c.replies || []).find(r => r.id === commentId);
      
      if (!comment) return;

      const isLiked = comment.likedBy.includes(user.id);
      const newLikedBy = isLiked 
        ? comment.likedBy.filter(id => id !== user.id)
        : [...comment.likedBy, user.id];

      const commentRef = doc(db, 'comments', commentId);
      await updateDoc(commentRef, {
        likes: newLikedBy.length,
        likedBy: newLikedBy
      });
    } catch (error) {
      console.error('Error liking comment:', error);
    }
  };

  // Delete comment
  const handleDeleteComment = async (commentId: string) => {
    if (!user) return;

    try {
      await deleteDoc(doc(db, 'comments', commentId));

      // Update post comment count
      const postRef = doc(db, 'posts', postId);
      await updateDoc(postRef, {
        comments: increment(-1)
      });

      toast.success('Comment deleted');
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
    }
  };

  const formatTimeAgo = (timestamp: any) => {
    try {
      // Handle Firebase Timestamp or Date object
      let date: Date;

      if (!timestamp) {
        return 'Just now';
      }

      // If it's a Firebase Timestamp
      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      }
      // If it's already a Date object
      else if (timestamp instanceof Date) {
        date = timestamp;
      }
      // If it's a timestamp number
      else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      }
      // If it's a string
      else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      }
      // Fallback
      else {
        return 'Just now';
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        return 'Just now';
      }

      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 60) return 'Just now';
      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
      if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
      if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting time:', error, 'timestamp:', timestamp);
      return 'Just now';
    }
  };

  // Toggle translation for a comment
  const toggleTranslation = (commentId: string, text: string) => {
    if (showTranslation[commentId]) {
      // Hide translation, show original
      setShowTranslation(prev => ({ ...prev, [commentId]: false }));
    } else {
      // Show translation
      if (!translatedComments[commentId]) {
        // Translate and cache
        const translated = translateSpanishToEnglish(text);
        setTranslatedComments(prev => ({ ...prev, [commentId]: translated }));
      }
      setShowTranslation(prev => ({ ...prev, [commentId]: true }));
    }
  };

  // Handle username click to navigate to profile
  const handleUsernameClick = (userId: string, userName: string, userRole?: string) => {
    if (!userId) return;

    // Determine if user is a provider based on role or username
    const isProvider = userRole === 'provider' ||
                      userName?.toLowerCase().includes('provider') ||
                      userName?.toLowerCase().includes('vet') ||
                      userName?.toLowerCase().includes('groomer') ||
                      userName?.toLowerCase().includes('trainer');

    if (isProvider) {
      console.log('🏢 Navigating to provider profile:', `/provider/public/${userId}`);
      router.push(`/provider/public/${userId}`);
    } else {
      console.log('🐕 Navigating to pet owner profile:', `/profile?id=${userId}`);
      router.push(`/profile?id=${userId}`);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">
            Comments ({comments.reduce((total, comment) => total + 1 + (comment.replies?.length || 0), 0)})
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Comments List */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {!user ? (
            <div className="text-center py-8">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Sign in to view comments</h3>
              <p className="text-gray-500 mb-6">Join the community to see what others are saying!</p>
              <div className="flex justify-center space-x-4">
                <Link
                  href={`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/signup"
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            </div>
          ) : loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading comments...</p>
            </div>
          ) : comments.length > 0 ? (
            comments.map((comment) => (
              <div key={comment.id} className="space-y-4">
                {/* Main Comment */}
                <div className="flex space-x-3">
                  <img
                    src={comment.userAvatar}
                    alt={comment.userName}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleUsernameClick(comment.userId, comment.userName, comment.userRole)}
                            className="font-semibold text-gray-900 hover:text-blue-600 transition-colors cursor-pointer"
                          >
                            {comment.userName}
                          </button>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            comment.userRole === 'provider' 
                              ? 'bg-blue-100 text-blue-700' 
                              : 'bg-green-100 text-green-700'
                          }`}>
                            {comment.userRole === 'provider' ? 'Provider' : 'Pet Owner'}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">
                          <NoSSR fallback="Just now">
                            {formatTimeAgo(comment.timestamp)}
                          </NoSSR>
                        </span>
                      </div>

                      {/* Comment Content with Translation */}
                      <div className="space-y-2">
                        <p className="text-gray-700">
                          {showTranslation[comment.id]
                            ? translatedComments[comment.id] || comment.content
                            : comment.content
                          }
                        </p>

                        {/* Translation Button */}
                        {needsTranslation(comment.content) && (
                          <button
                            onClick={() => toggleTranslation(comment.id, comment.content)}
                            className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
                          >
                            <span>🌐</span>
                            <span>
                              {showTranslation[comment.id] ? 'Show Original' : 'Translate to English'}
                            </span>
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Comment Actions */}
                    <div className="flex items-center space-x-4 mt-2">
                      <button
                        onClick={() => handleLikeComment(comment.id)}
                        className={`flex items-center space-x-1 text-sm transition-colors ${
                          comment.likedBy.includes(user?.id || '')
                            ? 'text-red-500 hover:text-red-600'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                        title={comment.likes > 0 ? `${comment.likes} ${comment.likes === 1 ? 'like' : 'likes'}` : 'Like'}
                      >
                        <Heart className={`w-4 h-4 ${comment.likedBy.includes(user?.id || '') ? 'fill-current' : ''}`} />
                        {comment.likes > 0 && <span className="text-xs">{comment.likes}</span>}
                      </button>
                      
                      <button
                        onClick={() => setReplyingTo(comment.id)}
                        className="text-gray-500 hover:text-gray-700"
                        title="Reply"
                      >
                        <Reply className="w-4 h-4" />
                      </button>
                      
                      {user?.id === comment.userId && (
                        <button
                          onClick={() => handleDeleteComment(comment.id)}
                          className="text-red-500 hover:text-red-600"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>

                    {/* Reply Form */}
                    {replyingTo === comment.id && user && (
                      <form
                        onSubmit={(e) => {
                          e.preventDefault();
                          handleSubmitReply(comment.id);
                        }}
                        className="mt-3 flex space-x-3"
                      >
                        <img
                          src={user.avatar || '/favicon.png'}
                          alt={user.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <div className="flex-1 flex space-x-2">
                          <input
                            type="text"
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            placeholder={`Reply to ${comment.userName}...`}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <button
                            type="submit"
                            disabled={!replyContent.trim() || submitting}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Send className="w-4 h-4" />
                          </button>
                        </div>
                      </form>
                    )}

                    {/* Replies */}
                    {comment.replies && comment.replies.length > 0 && (
                      <div className="mt-4 ml-6 space-y-3">
                        {comment.replies.map((reply) => (
                          <div key={reply.id} className="flex space-x-3">
                            <img
                              src={reply.userAvatar}
                              alt={reply.userName}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <div className="flex-1">
                              <div className="bg-gray-100 rounded-lg p-3">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="flex items-center space-x-2">
                                    <button
                                      onClick={() => handleUsernameClick(reply.userId, reply.userName, reply.userRole)}
                                      className="font-medium text-gray-900 text-sm hover:text-blue-600 transition-colors cursor-pointer"
                                    >
                                      {reply.userName}
                                    </button>
                                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                      reply.userRole === 'provider' 
                                        ? 'bg-blue-100 text-blue-700' 
                                        : 'bg-green-100 text-green-700'
                                    }`}>
                                      {reply.userRole === 'provider' ? 'Provider' : 'Pet Owner'}
                                    </span>
                                  </div>
                                  <span className="text-xs text-gray-500">
                                    <NoSSR fallback="Just now">
                                      {formatTimeAgo(reply.timestamp)}
                                    </NoSSR>
                                  </span>
                                </div>

                                {/* Reply Content with Translation */}
                                <div className="space-y-1">
                                  <p className="text-gray-700 text-sm">
                                    {showTranslation[reply.id]
                                      ? translatedComments[reply.id] || reply.content
                                      : reply.content
                                    }
                                  </p>

                                  {/* Translation Button for Reply */}
                                  {needsTranslation(reply.content) && (
                                    <button
                                      onClick={() => toggleTranslation(reply.id, reply.content)}
                                      className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
                                    >
                                      <span>🌐</span>
                                      <span>
                                        {showTranslation[reply.id] ? 'Show Original' : 'Translate'}
                                      </span>
                                    </button>
                                  )}
                                </div>
                              </div>
                              
                              <div className="flex items-center space-x-3 mt-1">
                                <button
                                  onClick={() => handleLikeComment(reply.id)}
                                  className={`flex items-center space-x-1 text-xs transition-colors ${
                                    reply.likedBy.includes(user?.id || '')
                                      ? 'text-red-500 hover:text-red-600'
                                      : 'text-gray-500 hover:text-gray-700'
                                  }`}
                                  title={reply.likes > 0 ? `${reply.likes} ${reply.likes === 1 ? 'like' : 'likes'}` : 'Like'}
                                >
                                  <Heart className={`w-3 h-3 ${reply.likedBy.includes(user?.id || '') ? 'fill-current' : ''}`} />
                                  {reply.likes > 0 && <span className="text-xs">{reply.likes}</span>}
                                </button>
                                
                                {user?.id === reply.userId && (
                                  <button
                                    onClick={() => handleDeleteComment(reply.id)}
                                    className="text-red-500 hover:text-red-600"
                                    title="Delete"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
              <p className="text-gray-500">Be the first to comment on this post!</p>
            </div>
          )}
        </div>

        {/* Comment Form */}
        <div className="border-t border-gray-200 p-6">
          {user ? (
            <form onSubmit={handleSubmitComment} className="flex space-x-3">
              <img
                src={user.avatar || '/favicon.png'}
                alt={user.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="flex-1 flex space-x-3">
                <input
                  type="text"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="submit"
                  disabled={!newComment.trim() || submitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {submitting ? 'Posting...' : 'Post'}
                </button>
              </div>
            </form>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 mb-4">Sign in to leave a comment</p>
              <div className="flex justify-center space-x-4">
                <Link
                  href={`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/signup"
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
