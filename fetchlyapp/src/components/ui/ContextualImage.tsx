'use client';

import Image from 'next/image';
import { useState } from 'react';

interface ContextualImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
  priority?: boolean;
}

export function ContextualImage({
  src,
  alt,
  width = 400,
  height = 300,
  className = '',
  fallbackSrc = '/fetchlylogo.png',
  priority = false
}: ContextualImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = () => {
    setImgSrc(fallbackSrc);
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className={`relative overflow-hidden rounded-2xl ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      <Image
        src={imgSrc}
        alt={alt}
        width={width}
        height={height}
        className={`transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'} object-cover w-full h-full`}
        onError={handleError}
        onLoad={handleLoad}
        priority={priority}
      />
    </div>
  );
}
