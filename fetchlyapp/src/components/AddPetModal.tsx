'use client';

import { useState } from 'react';
import { X, PawPrint, Upload } from 'lucide-react';
import { Pet, PetType, PetGender, PetSize, PetService, PetHelpers } from '@/lib/pets';
import { uploadImage, generateStoragePath, validateImageFile, compressImage } from '@/lib/storage';
import { useAuth } from '@/contexts/AuthContext';

interface AddPetModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

export default function AddPetModal({ onClose, onSuccess }: AddPetModalProps) {
  const { user } = useAuth();
  const [petData, setPetData] = useState({
    name: '',
    type: 'dog' as PetType,
    breed: '',
    gender: 'unknown' as PetGender,
    size: 'medium' as PetSize,
    weight: '',
    dateOfBirth: '',
    color: '',
    microchipId: '',
    medicalNotes: '',
    behaviorNotes: '',
    dietaryRestrictions: ''
  });
  const [saving, setSaving] = useState(false);
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!petData.name.trim()) {
      alert('Pet name is required');
      return;
    }

    if (!user?.id) {
      alert('You must be logged in to add a pet');
      return;
    }

    try {
      setSaving(true);

      let profileImageUrl = '';

      // Upload profile image if provided
      if (profileImage) {
        const validation = validateImageFile(profileImage);
        if (!validation.valid) {
          alert(validation.error);
          return;
        }

        const compressedFile = await compressImage(profileImage, 600, 600, 0.8);
        const path = generateStoragePath(user?.id || 'anonymous', 'pets', profileImage.name);

        const result = await uploadImage(compressedFile, path, (progress) => {
          setUploadProgress(progress.progress);
        });

        if (result.success && result.url) {
          profileImageUrl = result.url;
        } else {
          alert(result.error || 'Image upload failed');
          return;
        }
      }

      // Create pet record - only include fields that have values (no undefined)
      const newPet: any = {
        userId: user?.id || '',
        name: petData.name.trim(),
        type: petData.type,
        gender: petData.gender,
        size: petData.size,
        images: profileImageUrl ? [profileImageUrl] : [],
        isActive: true
      };

      // Only add optional fields if they have values
      if (petData.breed.trim()) {
        newPet.breed = petData.breed.trim();
      }
      if (petData.weight && !isNaN(parseFloat(petData.weight))) {
        newPet.weight = parseFloat(petData.weight);
      }
      if (petData.dateOfBirth) {
        newPet.dateOfBirth = petData.dateOfBirth;
      }
      if (petData.color.trim()) {
        newPet.color = petData.color.trim();
      }
      if (petData.microchipId.trim()) {
        newPet.microchipId = petData.microchipId.trim();
      }
      if (profileImageUrl) {
        newPet.profileImage = profileImageUrl;
      }
      if (petData.medicalNotes.trim()) {
        newPet.medicalNotes = petData.medicalNotes.trim();
      }
      if (petData.behaviorNotes.trim()) {
        newPet.behaviorNotes = petData.behaviorNotes.trim();
      }
      if (petData.dietaryRestrictions.trim()) {
        newPet.dietaryRestrictions = petData.dietaryRestrictions.trim();
      }

      await PetService.createPet(newPet);
      onSuccess();
    } catch (error) {
      console.error('Error creating pet:', error);
      alert('Failed to create pet. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Add New Pet</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Profile Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Profile Photo
              </label>
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {profileImage ? (
                    <img
                      src={URL.createObjectURL(profileImage)}
                      alt="Preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <PawPrint className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <label className="cursor-pointer bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">
                  <Upload className="h-4 w-4 inline mr-2" />
                  Choose Photo
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => setProfileImage(e.target.files?.[0] || null)}
                    className="hidden"
                  />
                </label>
              </div>
              {uploadProgress > 0 && (
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pet Name *
                </label>
                <input
                  type="text"
                  value={petData.name}
                  onChange={(e) => setPetData({ ...petData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pet Type *
                </label>
                <select
                  value={petData.type}
                  onChange={(e) => setPetData({ ...petData, type: e.target.value as PetType })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {PetHelpers.getPetTypes().map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Breed
                </label>
                <input
                  type="text"
                  value={petData.breed}
                  onChange={(e) => setPetData({ ...petData, breed: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Golden Retriever"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gender
                </label>
                <select
                  value={petData.gender}
                  onChange={(e) => setPetData({ ...petData, gender: e.target.value as PetGender })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {PetHelpers.getGenderOptions().map(gender => (
                    <option key={gender.value} value={gender.value}>{gender.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Size
                </label>
                <select
                  value={petData.size}
                  onChange={(e) => setPetData({ ...petData, size: e.target.value as PetSize })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {PetHelpers.getSizeOptions().map(size => (
                    <option key={size.value} value={size.value}>{size.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Weight (lbs)
                </label>
                <input
                  type="number"
                  value={petData.weight}
                  onChange={(e) => setPetData({ ...petData, weight: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0"
                  min="0"
                  step="0.1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date of Birth
                </label>
                <input
                  type="date"
                  value={petData.dateOfBirth}
                  onChange={(e) => setPetData({ ...petData, dateOfBirth: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Color/Markings
                </label>
                <input
                  type="text"
                  value={petData.color}
                  onChange={(e) => setPetData({ ...petData, color: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Brown and white"
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                disabled={saving}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving || !petData.name.trim()}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Creating...' : 'Create Pet'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
