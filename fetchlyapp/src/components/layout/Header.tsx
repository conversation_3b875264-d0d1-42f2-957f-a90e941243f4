'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, X, User, LogOut, Settings, ChevronDown, LayoutDashboard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';
import LanguageSelector from '@/components/LanguageSelector';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, signOut, isAuthenticated } = useAuth();

  return (
    <header className="sticky top-0 z-50 glass-card border-b border-white/20">
      <div className="container mx-auto px-3 sm:px-4 py-2 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center group">
            <img
              src="/fetchlylogo.png"
              alt="Fetchly"
              className="h-8 sm:h-10 md:h-12 w-auto group-hover:scale-105 transition-transform duration-300"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/search"
              className="text-text-primary hover:text-primary-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Find Services
            </Link>
            <Link
              href="/blog"
              className="text-text-primary hover:text-accent-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Blog
            </Link>
            <Link
              href="/emergency"
              className="text-text-primary hover:text-secondary-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Emergency
            </Link>
            <Link
              href="/community"
              className="text-text-primary hover:text-accent-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Community
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSelector />
            {isAuthenticated && <NotificationDropdown />}

            {isAuthenticated ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center gap-2 p-2 hover:bg-white/50 transition-all duration-300"
                >
                  <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-white/30">
                    <img
                      src={user?.avatar || '/favicon.png'}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/favicon.png';
                      }}
                    />
                  </div>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 z-50">
                    <div className="border-b border-white/20 pb-3 mb-3">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-white/30">
                          <img
                            src={user?.avatar || '/favicon.png'}
                            alt="Profile"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = '/favicon.png';
                            }}
                          />
                        </div>
                        <div>
                          <p className="font-medium text-gray-800">{user?.name}</p>
                          <p className="text-sm text-gray-600">{user?.email}</p>
                        </div>
                      </div>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                        user?.role === 'admin' ? 'bg-gradient-to-r from-red-100 to-pink-100 text-red-700' :
                        user?.role === 'provider' ? 'bg-gradient-to-r from-green-100 to-blue-100 text-green-700' :
                        'bg-gradient-to-r from-blue-100 to-green-100 text-blue-700'
                      }`}>
                        {user?.role === 'pet_owner' ? 'Pet Owner' :
                         user?.role === 'provider' ? 'Service Provider' : 'Admin'}
                      </span>
                    </div>

                    <div className="space-y-2">


                      {user?.role === 'provider' && (
                        <Link
                          href="/provider/dashboard"
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Settings className="w-4 h-4 text-green-600" />
                          <span className="text-gray-700">Provider Dashboard</span>
                        </Link>
                      )}

                      {user?.role === 'pet_owner' && (
                        <Link
                          href="/dashboard"
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <LayoutDashboard className="w-4 h-4 text-blue-600" />
                          <span className="text-gray-700">Dashboard</span>
                        </Link>
                      )}

                      <Link
                        href={user?.role === 'provider' ? '/provider/profile' : '/profile'}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="w-4 h-4 text-green-600" />
                        <span className="text-gray-700">Profile</span>
                      </Link>

                      <button
                        onClick={() => {
                          signOut();
                          setIsUserMenuOpen(false);
                        }}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-red-50 transition-all duration-300 w-full text-left"
                      >
                        <LogOut className="w-4 h-4 text-red-500" />
                        <span className="text-gray-700">Sign Out</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <>
                <Link
                  href="/auth/signin"
                  className="text-cool-700 hover:text-primary-500 font-medium transition-all duration-300 hover:scale-105"
                >
                  Sign In
                </Link>

                <Link
                  href="/auth/signup"
                  className="btn-primary"
                >
                  Get Started
                </Link>
              </>
            )}
          </div>

          {/* Mobile Actions */}
          <div className="md:hidden flex items-center space-x-2">
            <LanguageSelector />
            {isAuthenticated && <NotificationDropdown />}

            {isAuthenticated && (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center p-1"
                >
                  <div className="w-7 h-7 rounded-full overflow-hidden border-2 border-white/30">
                    <img
                      src={user?.avatar || '/favicon.png'}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/favicon.png';
                      }}
                    />
                  </div>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-white/95 backdrop-blur-sm rounded-2xl p-3 shadow-xl border border-white/20 z-50">
                    <div className="border-b border-white/20 pb-2 mb-2">
                      <div className="flex items-center space-x-2 mb-1">
                        <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-white/30">
                          <img
                            src={user?.avatar || '/favicon.png'}
                            alt="Profile"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = '/favicon.png';
                            }}
                          />
                        </div>
                        <div>
                          <p className="font-medium text-gray-800 text-sm">{user?.name}</p>
                          <p className="text-xs text-gray-600">{user?.email}</p>
                        </div>
                      </div>
                    </div>



                    {user?.role === 'provider' && (
                      <Link
                        href="/provider/dashboard"
                        className="flex items-center gap-2 p-2 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300 text-sm"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Settings className="w-4 h-4 text-green-600" />
                        <span className="text-gray-700">Provider Dashboard</span>
                      </Link>
                    )}

                    {user?.role === 'pet_owner' && (
                      <Link
                        href="/dashboard"
                        className="flex items-center gap-2 p-2 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300 text-sm"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <LayoutDashboard className="w-4 h-4 text-blue-600" />
                        <span className="text-gray-700">Dashboard</span>
                      </Link>
                    )}

                    <Link
                      href={user?.role === 'provider' ? '/provider/profile' : '/profile'}
                      className="flex items-center gap-2 p-2 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-300 text-sm"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <User className="w-4 h-4 text-green-600" />
                      <span className="text-gray-700">Profile</span>
                    </Link>

                    <button
                      onClick={() => {
                        signOut();
                        setIsUserMenuOpen(false);
                      }}
                      className="flex items-center gap-2 p-2 rounded-lg hover:bg-red-50 transition-all duration-300 w-full text-left text-sm"
                    >
                      <LogOut className="w-4 h-4 text-red-500" />
                      <span className="text-gray-700">Sign Out</span>
                    </button>
                  </div>
                )}
              </div>
            )}

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-700 hover:text-green-600 transition-colors duration-200"
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-3 pb-3 border-t border-white/20">
            <nav className="flex flex-col space-y-3 mt-3">
              <Link
                href="/search"
                className="block px-3 py-2 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 hover:text-green-600 font-medium transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Find Services
              </Link>
              <Link
                href="/blog"
                className="block px-3 py-2 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 hover:text-green-600 font-medium transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Blog
              </Link>
              <Link
                href="/emergency"
                className="block px-3 py-2 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 hover:text-red-600 font-medium transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Emergency
              </Link>
              <Link
                href="/community"
                className="block px-3 py-2 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:text-purple-600 font-medium transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Community
              </Link>
              
              <div className="flex flex-col space-y-3 pt-4 border-t border-primary-200/30">
                {isAuthenticated ? (
                  <>
                    {user?.role === 'pet_owner' && (
                      <Link
                        href="/dashboard"
                        className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                    )}
                    {user?.role === 'provider' && (
                      <Link
                        href="/provider/dashboard"
                        className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Provider Dashboard
                      </Link>
                    )}
                    <Link
                      href={user?.role === 'provider' ? '/provider/profile' : '/profile'}
                      className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    <button
                      onClick={() => {
                        signOut();
                        setIsMenuOpen(false);
                      }}
                      className="text-left text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/auth/signin"
                      className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="btn-primary text-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Get Started
                    </Link>
                  </>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
