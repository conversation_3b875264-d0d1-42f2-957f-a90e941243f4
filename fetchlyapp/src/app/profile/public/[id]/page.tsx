'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, getDocs, doc, getDoc, addDoc, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  MapPin, Heart, Share2, MessageCircle, ArrowLeft, PawPrint,
  Users, Star, Calendar, Camera, Trophy, Crown, Sparkles
} from 'lucide-react';
import toast from 'react-hot-toast';
import PostCard from '@/components/PostCard';
import ImageModal from '@/components/ImageModal';

export default function PublicPetOwnerProfile({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth();
  const router = useRouter();
  const resolvedParams = use(params);
  const userId = resolvedParams.id;
  
  const [profileUser, setProfileUser] = useState<any>(null);
  const [pets, setPets] = useState<any[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [followingCount, setFollowingCount] = useState(0);
  const [selectedImage, setSelectedImage] = useState<{ url: string; title: string } | null>(null);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading public pet owner profile for userId:', userId);
        console.log('👤 Current authenticated user:', {
          id: user?.id,
          role: user?.role,
          name: user?.name,
          email: user?.email
        });

        // Load user data
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (!userDoc.exists()) {
          throw new Error('User not found');
        }

        const userData = userDoc.data();
        setProfileUser({
          id: userId,
          ...userData
        });

        // Load user's pets
        console.log('🐾 Loading pets for user:', userId);
        const petsQuery = query(
          collection(db, 'pets'),
          where('userId', '==', userId)
        );
        const petsSnapshot = await getDocs(petsQuery);
        const userPets = petsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('🐾 Pets loaded:', userPets.length, userPets);
        setPets(userPets);

        // Load user's public posts
        console.log('📝 Loading posts for user:', userId);
        const postsQuery = query(
          collection(db, 'posts'),
          where('userId', '==', userId),
          where('isPublic', '==', true)
        );
        const postsSnapshot = await getDocs(postsQuery);
        const userPosts = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('📝 Posts loaded:', userPosts.length, userPosts);
        setPosts(userPosts);

        // Check if current user is following this profile
        if (user) {
          console.log('👥 Checking follow status for:', user.id, '→', userId);
          const followQuery = query(
            collection(db, 'followers'),
            where('followedUserId', '==', userId),
            where('followerUserId', '==', user.id)
          );
          const followSnapshot = await getDocs(followQuery);
          const isFollowingUser = !followSnapshot.empty;
          console.log('👥 Follow status:', isFollowingUser);
          setIsFollowing(isFollowingUser);
        } else {
          console.log('👥 No authenticated user - skipping follow status check');
        }

        // Get follower count
        console.log('📊 Loading follower counts for user:', userId);
        const followerQuery = query(
          collection(db, 'followers'),
          where('followedUserId', '==', userId)
        );
        const followerSnapshot = await getDocs(followerQuery);
        const followers = followerSnapshot.docs.length;
        console.log('📊 Followers:', followers);
        setFollowerCount(followers);

        // Get following count
        const followingQuery = query(
          collection(db, 'followers'),
          where('followerUserId', '==', userId)
        );
        const followingSnapshot = await getDocs(followingQuery);
        const following = followingSnapshot.docs.length;
        console.log('📊 Following:', following);
        setFollowingCount(following);

      } catch (error) {
        console.error('❌ Error loading profile:', error);
        console.error('❌ Error details:', {
          message: error.message,
          code: error.code,
          userId: userId,
          currentUser: user?.id
        });
        toast.error(`Failed to load profile: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [userId, user]);

  const handleFollow = async () => {
    if (!user) {
      console.log('❌ No user authenticated for follow action');
      toast.error('Please sign in to follow users');
      return;
    }

    console.log('👥 Follow action:', isFollowing ? 'UNFOLLOW' : 'FOLLOW', user.id, '→', userId);

    try {
      if (isFollowing) {
        // Unfollow
        console.log('👥 Unfollowing user...');
        const followQuery = query(
          collection(db, 'followers'),
          where('followedUserId', '==', userId),
          where('followerUserId', '==', user.id)
        );
        const followSnapshot = await getDocs(followQuery);
        if (!followSnapshot.empty) {
          await deleteDoc(followSnapshot.docs[0].ref);
          setIsFollowing(false);
          setFollowerCount(prev => prev - 1);
          console.log('✅ Unfollowed successfully');
          toast.success('Unfollowed user');
        } else {
          console.log('⚠️ No follow relationship found to delete');
        }
      } else {
        // Follow
        console.log('👥 Following user...');
        await addDoc(collection(db, 'followers'), {
          followedUserId: userId,
          followerUserId: user.id,
          createdAt: new Date()
        });
        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        console.log('✅ Followed successfully');
        toast.success('Following user');
      }
    } catch (error) {
      console.error('❌ Error following/unfollowing:', error);
      toast.error('Failed to update follow status');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Profile Not Found</h2>
          <p className="text-gray-600 mb-4">This user profile doesn't exist or isn't available.</p>
          <button
            onClick={() => router.push('/community')}
            className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-lg hover:from-green-600 hover:to-blue-600"
          >
            Back to Community
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">

          {/* Profile Header - Hot Public Design */}
          <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden mb-8 hover:shadow-3xl transition-all duration-500">
            <div className="relative w-full h-96 bg-gradient-to-r from-green-400 via-blue-500 to-cyan-500 overflow-hidden">
              {profileUser.banner ? (
                <div className="relative w-full h-full overflow-hidden">
                  <img
                    src={profileUser.banner}
                    alt="Profile banner"
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="absolute inset-0 bg-gradient-to-br from-green-400/80 via-blue-500/80 to-cyan-500/80"></div>
              )}

              {/* Animated background elements */}
              <div className="absolute inset-0 opacity-20">
                <div className="absolute top-10 left-10 w-3 h-3 bg-white rounded-full animate-pulse"></div>
                <div className="absolute top-20 right-20 w-2 h-2 bg-cyan-200 rounded-full animate-ping"></div>
                <div className="absolute bottom-20 left-20 w-2.5 h-2.5 bg-green-200 rounded-full animate-bounce"></div>
                <div className="absolute bottom-10 right-10 w-1.5 h-1.5 bg-blue-200 rounded-full animate-pulse"></div>
              </div>

              {/* Public profile badge */}
              <div className="absolute top-6 left-6 bg-white/20 backdrop-blur-md px-4 py-2 rounded-2xl border border-white/30">
                <div className="flex items-center space-x-2">
                  <Crown className="w-5 h-5 text-white" />
                  <span className="text-white font-semibold">Public Profile</span>
                </div>
              </div>
            </div>

            <div className="relative px-6 pb-6">
              <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                {/* Profile Picture - Enhanced Public Design */}
                <div className="relative -mt-20 mb-6 sm:mb-0">
                  <div className="relative group">
                    <div className="w-44 h-44 rounded-full bg-gradient-to-r from-green-400 to-blue-500 p-1.5 shadow-2xl">
                      <img
                        src={profileUser.avatar || '/favicon.png'}
                        alt={profileUser.name}
                        className="w-full h-full rounded-full object-cover border-4 border-white shadow-lg group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    {/* Public profile indicator */}
                    <div className="absolute top-3 right-3 w-8 h-8 bg-blue-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center">
                      <Sparkles className="w-5 h-5 text-white" />
                    </div>
                  </div>
                </div>

                {/* User Info - Enhanced */}
                <div className="flex-1">
                  <div className="space-y-4">
                    <div>
                      <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                        {profileUser.name}
                      </h1>
                      <div className="text-gray-600 text-lg">
                        {pets.length > 0 ? (
                          <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full inline-flex">
                            <PawPrint className="w-5 h-5 text-green-600" />
                            <span className="font-medium">Pet parent to {pets.slice(0, 3).map(pet => pet.name).join(', ')}{pets.length > 3 ? ` and ${pets.length - 3} more` : ''}</span>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full inline-flex">
                            <PawPrint className="w-5 h-5 text-blue-600" />
                            <span className="font-medium">Pet lover</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-4 text-sm">
                      {profileUser.location && (
                        <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                          <MapPin className="w-4 h-4 text-green-600" />
                          <span className="text-gray-700 font-medium">{profileUser.location}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                        <Calendar className="w-4 h-4 text-blue-600" />
                        <span className="text-gray-700 font-medium">Member since {profileUser.joinedDate ? new Date(profileUser.joinedDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Follow Button - Enhanced */}
                {user && user.id !== userId && (
                  <div className="flex flex-wrap gap-3 mt-6 sm:mt-0">
                    <button
                      onClick={handleFollow}
                      className={`px-8 py-4 rounded-2xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl border backdrop-blur-sm font-bold text-lg ${
                        isFollowing
                          ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-green-400'
                          : 'bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white border-blue-400'
                      }`}
                    >
                      <Users className="w-5 h-5" />
                      <span>{isFollowing ? 'Following' : 'Follow'}</span>
                    </button>
                    <button
                      className="px-6 py-4 bg-white/60 backdrop-blur-sm hover:bg-white/80 text-gray-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-white/30 flex items-center space-x-2 font-semibold"
                    >
                      <MessageCircle className="w-5 h-5" />
                      <span>Message</span>
                    </button>
                    <button
                      className="px-4 py-4 bg-white/60 backdrop-blur-sm hover:bg-white/80 text-gray-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-white/30"
                    >
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* User Stats Section - Enhanced Hot Design */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-8">
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
              <div className="relative">
                <div className="text-4xl font-black bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                  {pets.length}
                </div>
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div className="text-gray-700 font-semibold text-lg">Pets</div>
              <div className="w-full h-1 bg-gradient-to-r from-green-400 to-blue-400 rounded-full mt-3 opacity-60"></div>
            </div>
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
              <div className="relative">
                <div className="text-4xl font-black bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                  {posts.length}
                </div>
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              </div>
              <div className="text-gray-700 font-semibold text-lg">Posts</div>
              <div className="w-full h-1 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mt-3 opacity-60"></div>
            </div>
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
              <div className="relative">
                <div className="text-4xl font-black bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                  {followerCount}
                </div>
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
              </div>
              <div className="text-gray-700 font-semibold text-lg">Followers</div>
              <div className="w-full h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mt-3 opacity-60"></div>
            </div>
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
              <div className="relative">
                <div className="text-4xl font-black bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                  {followingCount}
                </div>
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
              </div>
              <div className="text-gray-700 font-semibold text-lg">Following</div>
              <div className="w-full h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full mt-3 opacity-60"></div>
            </div>
          </div>
          {/* Pet Management Section - Enhanced Hot Design */}
          <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 p-8 hover:shadow-3xl transition-all duration-500">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
              <div>
                <h2 className="text-3xl font-black bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent mb-2">
                  {profileUser.name}'s Furry Family
                </h2>
                <p className="text-gray-600 font-medium">
                  Meet their adorable companions
                </p>
              </div>
            </div>

            {pets.length === 0 ? (
              <div className="text-center py-16">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center mx-auto shadow-2xl">
                    <PawPrint className="w-12 h-12 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"></div>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-3">No pets to show</h3>
                <p className="text-gray-600 mb-8 text-lg max-w-md mx-auto">
                  This user hasn't added any pets to their profile yet
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {pets.map((pet) => (
                  <div key={pet.id} className="bg-white rounded-xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center space-x-4 mb-4">
                      <img
                        src={pet.profileImage || pet.photo || pet.avatar || '/favicon.png'}
                        alt={pet.name}
                        className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => setSelectedImage({
                          url: pet.profileImage || pet.photo || pet.avatar || '/favicon.png',
                          title: `${pet.name} - ${pet.breed}`
                        })}
                      />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{pet.name}</h3>
                        <p className="text-gray-600">{pet.breed} • {pet.age} years old</p>
                      </div>
                    </div>
                    {pet.description && (
                      <p className="text-gray-600 text-sm">{pet.description}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Posts Section */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
              {profileUser.name}'s Posts
            </h3>

            {/* Posts Feed */}
            <div className="space-y-6">
              {posts.length > 0 ? (
                posts.map((post) => (
                  <PostCard
                    key={post.id}
                    post={post}
                    showPrivacyIndicator={false}
                    onImageClick={(imageUrl, title) => setSelectedImage({ url: imageUrl, title })}
                  />
                ))
              ) : (
                <div className="text-center py-12">
                  <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                  <p className="text-gray-500">This user hasn't shared any posts yet</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      <ImageModal
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        imageUrl={selectedImage?.url || ''}
        title={selectedImage?.title}
        altText={selectedImage?.title}
      />
    </div>
  );
}
