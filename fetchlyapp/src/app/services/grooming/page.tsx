'use client';

import { useState } from 'react';
import { Scissors, Star, MapPin, Clock, DollarSign, CheckCircle, Calendar } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const groomingServices = [
  {
    title: "Basic Grooming Package",
    price: "$45-65",
    duration: "2-3 hours",
    includes: ["Bath & Dry", "Nail Trimming", "Ear Cleaning", "Basic Brushing"],
    popular: false
  },
  {
    title: "Full Service Grooming",
    price: "$75-95",
    duration: "3-4 hours",
    includes: ["Everything in Basic", "Hair Cut & Styling", "Teeth Brushing", "Anal Gland Expression", "Cologne Spritz"],
    popular: true
  },
  {
    title: "Premium Spa Package",
    price: "$95-125",
    duration: "4-5 hours",
    includes: ["Everything in Full Service", "Deep Conditioning Treatment", "Nail Polish", "Bow or Bandana", "Photo Session"],
    popular: false
  }
];

const providers = [
  {
    id: 1,
    name: "Paws & Claws Grooming Salon",
    rating: 4.9,
    reviews: 127,
    distance: "0.8 miles",
    price: "$45-85",
    specialties: ["Long Hair Breeds", "Show Cuts", "Nail Art"],
    availability: "Available Today",
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    name: "Furry Friends Spa",
    rating: 4.8,
    reviews: 89,
    distance: "1.2 miles",
    price: "$55-95",
    specialties: ["Sensitive Skin", "Senior Pets", "Anxiety-Friendly"],
    availability: "Next Available: Tomorrow",
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    name: "The Pet Parlor",
    rating: 4.7,
    reviews: 156,
    distance: "2.1 miles",
    price: "$40-75",
    specialties: ["Quick Service", "Walk-ins Welcome", "Mobile Grooming"],
    availability: "Available This Week",
    image: "/api/placeholder/300/200"
  }
];

export default function GroomingPage() {
  const [selectedPackage, setSelectedPackage] = useState('');
  const [location, setLocation] = useState('');

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-primary-400/20 to-cool-400/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Scissors className="w-12 h-12 text-primary-500" />
              <h1 className="text-4xl md:text-6xl font-bold text-cool-800">
                Pet <span className="text-gradient">Grooming</span>
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              Professional grooming services to keep your pet looking and feeling their absolute best
            </p>
            
            {/* Quick Search */}
            <div className="glass-card rounded-2xl p-6 max-w-2xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Service Package</label>
                  <select
                    value={selectedPackage}
                    onChange={(e) => setSelectedPackage(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="">Select package</option>
                    <option value="basic">Basic Grooming</option>
                    <option value="full">Full Service</option>
                    <option value="premium">Premium Spa</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Location</label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                    <input
                      type="text"
                      placeholder="Enter location"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                </div>
                <div className="flex items-end">
                  <Link href="/search?service=grooming" className="btn-primary w-full text-center">
                    Find Groomers
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Packages */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Grooming Packages
            </h2>
            <p className="text-xl text-cool-600">
              Choose the perfect grooming package for your pet
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {groomingServices.map((service, index) => (
              <div key={index} className={`glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 relative ${service.popular ? 'border-2 border-primary-500' : ''}`}>
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-cool-800 mb-2">{service.title}</h3>
                  <div className="text-3xl font-bold text-gradient mb-2">{service.price}</div>
                  <div className="flex items-center justify-center gap-2 text-cool-600">
                    <Clock className="w-4 h-4" />
                    <span>{service.duration}</span>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  {service.includes.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0" />
                      <span className="text-cool-700">{item}</span>
                    </div>
                  ))}
                </div>

                <Link 
                  href={`/search?service=grooming&package=${service.title.toLowerCase().replace(/\s+/g, '-')}`}
                  className={service.popular ? 'btn-primary w-full text-center' : 'btn-secondary w-full text-center'}
                >
                  Book This Package
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Providers */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Top-Rated Groomers
            </h2>
            <p className="text-xl text-cool-600">
              Trusted professionals in your area
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {providers.map((provider) => (
              <div key={provider.id} className="glass-card rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-all duration-300">
                      <Image
                        src="/fetchlylogo.png"
                        alt="Fetchly Logo"
                        width={20}
                        height={20}
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="px-3 py-1 bg-primary-500 text-white text-sm font-medium rounded-full">
                      Grooming Specialist
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-cool-800 group-hover:text-primary-500 transition-colors duration-300">
                      {provider.name}
                    </h3>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-cool-600 mb-1">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        {provider.rating} ({provider.reviews})
                      </div>
                      <div className="text-sm text-cool-600">{provider.distance}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 mb-4 text-sm text-cool-600">
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {provider.price}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {provider.availability}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {provider.specialties.slice(0, 3).map((specialty, index) => (
                      <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                        {specialty}
                      </span>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/provider/${provider.id}`} className="btn-secondary flex-1 text-center">
                      View Profile
                    </Link>
                    <Link href={`/booking?provider=${provider.id}&service=grooming`} className="btn-primary flex-1 text-center">
                      Book Now
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/search?service=grooming" className="btn-secondary">
              View All Groomers
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Professional Grooming */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                Why Professional Grooming?
              </h2>
              <p className="text-xl text-cool-600">
                More than just a bath - it's about your pet's health and happiness
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Image
                    src="/favicon.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Health Benefits</h3>
                <p className="text-cool-600 text-sm">Early detection of skin issues, parasites, and health concerns</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Scissors className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Professional Tools</h3>
                <p className="text-cool-600 text-sm">Specialized equipment and techniques for best results</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Stress-Free</h3>
                <p className="text-cool-600 text-sm">Experienced handlers who know how to keep pets calm</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 flex items-center justify-center">
                  <Clock className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Time Saving</h3>
                <p className="text-cool-600 text-sm">Let professionals handle the mess while you relax</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-3xl mx-auto text-center relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-secondary-500/5 to-accent-500/5"></div>
            <div className="relative z-10">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Ready to Pamper Your Pet?
              </h2>
              <p className="text-xl text-cool-600 mb-8">
                Book a grooming appointment today and see the difference professional care makes
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/search?service=grooming" className="btn-primary">
                  Find Groomers Near Me
                </Link>
                <Link href="/emergency" className="btn-secondary">
                  Emergency Grooming
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
