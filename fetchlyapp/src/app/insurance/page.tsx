'use client';

import { Shield, Heart, Star, CheckCircle, Clock, Users, Award, Zap, TrendingUp, Phone, Mail } from 'lucide-react';
import Link from 'next/link';

export default function InsurancePage() {
  const features = [
    {
      icon: Heart,
      title: "Comprehensive Pet Health Coverage",
      description: "From routine checkups to emergency surgeries, we're designing coverage that truly protects your furry family members."
    },
    {
      icon: Shield,
      title: "Service Provider Protection",
      description: "Revolutionary insurance that protects both pet owners and service providers during every interaction on our platform."
    },
    {
      icon: Star,
      title: "Preventive Care Focus",
      description: "Unlike traditional pet insurance, our coverage will emphasize preventive care to keep pets healthier for longer."
    },
    {
      icon: Zap,
      title: "Instant Claims Processing",
      description: "AI-powered claims processing that gets you reimbursed in minutes, not weeks."
    },
    {
      icon: Users,
      title: "Community-Driven Benefits",
      description: "The more our community grows, the better the coverage becomes for everyone."
    },
    {
      icon: TrendingUp,
      title: "Affordable Premium Structure",
      description: "Transparent, fair pricing that makes comprehensive pet insurance accessible to every pet parent."
    }
  ];



  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-500 via-blue-500 to-cyan-500 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-white/20 rounded-full">
                <Shield className="w-16 h-16" />
              </div>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              The Future of Pet Insurance
            </h1>
            <p className="text-2xl text-blue-100 max-w-3xl mx-auto mb-8">
              We're revolutionizing pet insurance to create the most comprehensive, affordable, and user-friendly coverage that truly puts pets first.
            </p>
            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto">
              <p className="text-xl font-semibold mb-2">🚀 Currently in Development</p>
              <p className="text-blue-100">
                Our team of veterinarians, insurance experts, and technology innovators are working around the clock to bring you something extraordinary.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Vision Statement */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Vision</h2>
            <p className="text-xl text-gray-700 leading-relaxed">
              Every pet deserves the best possible care without financial barriers. We're creating an insurance solution that doesn't just cover costs—it actively promotes pet health, supports responsible pet ownership, and strengthens the bond between pets and their families.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-green-500 text-white mb-6 mx-auto">
                  <feature.icon className="w-8 h-8" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">{feature.title}</h3>
                <p className="text-gray-600 text-center">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>



      {/* Why It Matters */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl p-8 text-white text-center">
              <h2 className="text-3xl font-bold mb-6">Why This Matters</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                <div>
                  <div className="text-4xl font-bold mb-2">67%</div>
                  <p className="text-blue-100">of pet owners avoid vet visits due to cost concerns</p>
                </div>
                <div>
                  <div className="text-4xl font-bold mb-2">$1,480</div>
                  <p className="text-blue-100">average annual vet expenses per pet</p>
                </div>
                <div>
                  <div className="text-4xl font-bold mb-2">90%</div>
                  <p className="text-blue-100">of pets could benefit from preventive care coverage</p>
                </div>
              </div>
              <p className="text-xl text-blue-100">
                We're not just building insurance—we're building peace of mind for every pet parent.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Early Access */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Be Among the First</h2>
            <p className="text-xl text-gray-600 mb-8">
              Join our early access list to be notified when our revolutionary pet insurance becomes available.
            </p>
            
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center mb-6">
                <Award className="w-12 h-12 text-yellow-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Early Access Benefits</h3>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>50% discount on first year premium</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Priority customer support</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>Exclusive beta features access</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span>No waiting periods for coverage</span>
                </li>
              </ul>
              
              <div className="space-y-4">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button className="w-full bg-gradient-to-r from-blue-600 to-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-green-700 transition-all duration-300">
                  Join Early Access List
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Questions About Our Insurance?</h2>
            <div className="max-w-md mx-auto">
              <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
                <Mail className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Email Us</h3>
                <p className="text-gray-600 mb-4">Get detailed information about our insurance plans</p>
                <a href="mailto:<EMAIL>" className="text-green-600 hover:text-green-700 font-semibold">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
