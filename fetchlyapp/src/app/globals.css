@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Nunito:wght@300;400;500;600;700;800&display=swap');
@import "tailwindcss";

/*
  Fetchly Visual Redesign - Frosted Glass & Mint-Blue Aesthetic
  ------------------------------------------------------------
  New design system with sophisticated wellness-focused aesthetic
  - Mint-blue gradient backgrounds
  - Frosted glass containers
  - Preserved brand logo colors
  - Enhanced accessibility and modern UI
*/
:root {
  /* Main Background Gradients */
  --background: linear-gradient(135deg, #F0FDF4 0%, #F0F9FF 100%);
  --background-alt: linear-gradient(135deg, #F0F9FF 0%, #F0FDF4 100%);

  /* Brand Logo Colors (Preserved) */
  --brand-fetch: #87CEEB; /* Sky Blue for "Fetch" */
  --brand-ly: #9CA3AF; /* Soft Gray for "ly" */

  /* Primary Color Palette */
  --primary: #06B6D4; /* Aqua */
  --primary-hover: #0891B2; /* Darker Aqua */
  --secondary: #0EA5E9; /* Sky Blue */
  --accent: #10B981; /* Soft Mint Green */
  --accent-light: #34D399; /* Light Mint */

  /* Text Colors */
  --foreground: #374151; /* Charcoal Gray */
  --text-secondary: #6B7280; /* Medium Gray */
  --text-muted: #9CA3AF; /* Light Gray */
  --text-white: #FFFFFF;

  /* Frosted Glass System */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);

  /* Gradient Definitions */
  --gradient-cta: linear-gradient(135deg, #06B6D4 0%, #0EA5E9 100%);
  --gradient-cta-hover: linear-gradient(135deg, #0891B2 0%, #0284C7 100%);
  --gradient-footer: linear-gradient(135deg, #1E293B 0%, #0EA5E9 100%);

  /* Border Colors */
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-dark: #9CA3AF;

  /* Legacy Support - Updated Values */
  --fetchly-blue: #0EA5E9;
  --cool: #6B7280;
  --gray: #F9FAFB;

  /* Color variables for use with var() functions */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-cool: var(--cool);
  --color-gray: var(--gray);
  --color-fetchly-blue: var(--fetchly-blue);

  /* Sidebar Colors */
  --sidebar-selected-bg: var(--primary);
  --sidebar-selected-text: var(--text-white);
  --sidebar-hover-bg: var(--glass-bg);
  --sidebar-hover-text: var(--foreground);

  /* Font variables */
  --font-sans: 'Inter', 'Nunito', system-ui, sans-serif;
  --font-display: 'Nunito', 'Inter', system-ui, sans-serif;
}

body {
  background: var(--background);
  background-size: 400% 400%;
  animation: gradient 20s ease infinite;
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
  background-attachment: fixed;
  /* Mobile optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Safe area support for iOS */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Fetchly Frosted Glass Design System */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.glass-modal {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 25px 50px rgba(31, 38, 135, 0.25);
  border-radius: 1.5rem;
}

.glass-button {
  background: var(--gradient-cta);
  border: none;
  border-radius: 0.75rem;
  color: var(--text-white);
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
  cursor: pointer;
}

.glass-button:hover {
  background: var(--gradient-cta-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

.glass-button:active {
  transform: translateY(0) scale(1.02);
}

/* Glass Card Hover Effect */
.glass-card:hover {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 25px 50px rgba(31, 38, 135, 0.2);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Enhanced Button Styles */
.btn-primary {
  background: var(--gradient-cta);
  background-size: 200% 200%;
  color: var(--text-white);
  padding: 0.875rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  color: var(--foreground);
  border: 1px solid var(--border-light);
  padding: 0.875rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

/* Text Utilities */
.text-primary {
  color: var(--foreground);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

/* Background Utilities */
.bg-gradient-main {
  background: var(--background);
}

.bg-gradient-alt {
  background: var(--background-alt);
}

.bg-gradient-footer {
  background: var(--gradient-footer);
}

/* Enhanced Button Layout */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: var(--gradient-cta-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(31, 38, 135, 0.15);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  background: rgba(248, 250, 252, 0.9); /* Light gray background */
  color: #1e293b; /* Dark slate text */
  padding: 0.875rem 2rem;
  border-radius: 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid rgba(14, 165, 233, 0.3); /* Fetchly blue border */
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(100, 116, 139, 0.1) 100%);
  border-color: rgba(14, 165, 233, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(14, 165, 233, 0.2);
}

.text-gradient {
  background: linear-gradient(135deg, #0ea5e9 0%, #64748b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.text-gradient {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Mobile-specific optimizations for Capacitor */
@media (max-width: 768px) {
  /* Touch-friendly button sizes */
  .btn-primary, .btn-secondary {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  /* Larger touch targets */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved spacing for mobile */
  .glass-card {
    padding: 1rem;
    margin: 0.5rem;
  }

  /* Mobile-optimized text sizes */
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }
}

/* iOS specific optimizations */
@supports (-webkit-touch-callout: none) {
  .ios-safe-area {
    padding-top: max(env(safe-area-inset-top), 20px);
    padding-bottom: max(env(safe-area-inset-bottom), 20px);
  }
}

/* Android specific optimizations */
@media (max-width: 768px) and (orientation: portrait) {
  .android-keyboard-adjust {
    height: calc(100vh - env(keyboard-inset-height, 0px));
  }
}

/* Smooth scrolling for mobile */
* {
  -webkit-overflow-scrolling: touch;
}

/* Disable text selection on interactive elements */
button, .btn-primary, .btn-secondary, .clickable {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Provider Dashboard Sidebar/Tab Button Styles */
.sidebar-selected {
  background: var(--sidebar-selected-bg) !important;
  color: var(--sidebar-selected-text) !important;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.12);
  transform: scale(1.05);
  font-weight: 600;
}
.sidebar-unselected {
  background: transparent;
  color: #1e293b;
  transition: background 0.2s, color 0.2s;
}
.sidebar-unselected:hover {
  background: var(--sidebar-hover-bg);
  color: var(--sidebar-hover-text);
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.08);
}

/* Scrollbar Hide Utility */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
