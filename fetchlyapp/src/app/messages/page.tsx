'use client';

import React from 'react';
import { MessageCircle, Users, HeadphonesIcon } from 'lucide-react';
import ChatInterface from '@/components/chat/ChatInterface';
import { useAuth } from '@/contexts/AuthContext';

export default function MessagesPage() {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto text-center">
            <div className="glass-card p-8 rounded-3xl">
              <MessageCircle className="w-20 h-20 text-gray-400 mx-auto mb-6" />
              <h1 className="text-2xl font-bold text-gray-800 mb-4">
                Sign in to Chat
              </h1>
              <p className="text-gray-600 mb-6">
                Connect with other pet owners and service providers through our secure messaging system.
              </p>
              <div className="space-y-3">
                <a
                  href="/auth/signin"
                  className="block w-full px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 font-semibold"
                >
                  Sign In
                </a>
                <a
                  href="/auth/signup"
                  className="block w-full px-6 py-3 bg-white border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-300 font-semibold"
                >
                  Create Account
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      {/* Header */}
      <div className="sticky top-0 z-40 glass-card border-b border-white/20">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  Messages
                </h1>
                <p className="text-sm text-gray-500 hidden md:block">
                  Connect with the pet community
                </p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-semibold text-gray-700">Community</span>
                </div>
                <p className="text-xs text-gray-500">Pet Owners & Providers</p>
              </div>
              <div className="text-center">
                <div className="flex items-center space-x-2">
                  <HeadphonesIcon className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-semibold text-gray-700">24/7 Support</span>
                </div>
                <p className="text-xs text-gray-500">Always here to help</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Chat Interface */}
      <div className="container mx-auto px-4 py-6">
        <div className="h-[calc(100vh-12rem)] max-w-7xl mx-auto">
          <div className="h-full bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
            <ChatInterface className="h-full" />
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation Helper */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 glass-card border-t border-white/20 p-4">
        <div className="flex items-center justify-center space-x-6">
          <div className="flex items-center space-x-2 text-green-600">
            <MessageCircle className="w-5 h-5" />
            <span className="text-sm font-medium">Messages</span>
          </div>
          <div className="w-px h-6 bg-gray-300"></div>
          <div className="flex items-center space-x-2 text-gray-500">
            <HeadphonesIcon className="w-5 h-5" />
            <span className="text-sm">Support Available</span>
          </div>
        </div>
      </div>
    </div>
  );
}
