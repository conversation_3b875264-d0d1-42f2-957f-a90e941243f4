'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Gift, Star, Award, Crown, Zap, Heart, Coffee, ShoppingBag,
  Percent, DollarSign, Calendar, Clock, Check, ArrowRight,
  TrendingUp, Target, Sparkles, Trophy, Medal, Gem, X
} from 'lucide-react';
import QuickActions from '@/components/QuickActions';

interface Reward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  cashValue?: number;
  category: 'discount' | 'cashback' | 'service' | 'premium' | 'exclusive';
  icon: any;
  color: string;
  bgColor: string;
  available: boolean;
  expiresAt?: Date;
  popularity: number;
}

interface UserRewards {
  totalPoints: number;
  pointsEarned: number;
  pointsRedeemed: number;
  currentTier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  nextTierPoints: number;
  redeemedRewards: string[];
}

export default function RewardsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [userRewards, setUserRewards] = useState<UserRewards>({
    totalPoints: 1250,
    pointsEarned: 2100,
    pointsRedeemed: 850,
    currentTier: 'Silver',
    nextTierPoints: 750,
    redeemedRewards: []
  });

  const [rewards, setRewards] = useState<Reward[]>([
    {
      id: '1',
      title: '$5 Service Credit',
      description: 'Get $5 off your next pet service booking',
      pointsCost: 500,
      cashValue: 5,
      category: 'cashback',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      available: true,
      popularity: 95
    },
    {
      id: '2',
      title: '20% Off Grooming',
      description: 'Save 20% on any grooming service',
      pointsCost: 300,
      category: 'discount',
      icon: Percent,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      available: true,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      popularity: 87
    },
    {
      id: '3',
      title: 'Free Pet Health Check',
      description: 'Complimentary basic health examination',
      pointsCost: 800,
      cashValue: 25,
      category: 'service',
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      available: true,
      popularity: 92
    },
    {
      id: '4',
      title: 'Premium Support Access',
      description: '30 days of priority customer support',
      pointsCost: 400,
      category: 'premium',
      icon: Crown,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      available: true,
      popularity: 78
    },
    {
      id: '5',
      title: 'Exclusive Pet Photo Session',
      description: 'Professional pet photography session',
      pointsCost: 1200,
      cashValue: 50,
      category: 'exclusive',
      icon: Sparkles,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      available: userRewards.currentTier === 'Gold' || userRewards.currentTier === 'Platinum',
      popularity: 89
    },
    {
      id: '6',
      title: '$10 Cash Back',
      description: 'Direct cash back to your Fetchly wallet',
      pointsCost: 1000,
      cashValue: 10,
      category: 'cashback',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      available: true,
      popularity: 96
    }
  ]);

  const categories = [
    { id: 'all', label: 'All Rewards', icon: Gift },
    { id: 'cashback', label: 'Cash Back', icon: DollarSign },
    { id: 'discount', label: 'Discounts', icon: Percent },
    { id: 'service', label: 'Services', icon: Heart },
    { id: 'premium', label: 'Premium', icon: Crown },
    { id: 'exclusive', label: 'Exclusive', icon: Sparkles }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }
    loadRewardsData();
  }, [user, router]);

  const loadRewardsData = async () => {
    try {
      setLoading(true);
      // TODO: Load real rewards data from API
    } catch (error) {
      console.error('Error loading rewards data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredRewards = rewards.filter(reward => 
    selectedCategory === 'all' || reward.category === selectedCategory
  );

  const handleRedeemReward = async (rewardId: string) => {
    // Show coming soon popup instead of actual redemption
    setShowComingSoon(true);
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'text-orange-600 bg-orange-100';
      case 'Silver': return 'text-gray-600 bg-gray-100';
      case 'Gold': return 'text-yellow-600 bg-yellow-100';
      case 'Platinum': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'Bronze': return Medal;
      case 'Silver': return Award;
      case 'Gold': return Trophy;
      case 'Platinum': return Crown;
      default: return Star;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your rewards...</p>
        </div>
      </div>
    );
  }

  const TierIcon = getTierIcon(userRewards.currentTier);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Fetchly Rewards</h1>
              <p className="text-purple-100 mt-2">Redeem your points for amazing rewards and benefits</p>
            </div>
            <div className="flex items-center space-x-4">
              <button 
                onClick={() => router.push('/dashboard')}
                className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg flex items-center space-x-2"
              >
                <ArrowRight className="w-5 h-5 rotate-180" />
                <span className="hidden md:block">Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Points Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available Points</p>
                <p className="text-3xl font-bold text-gray-900">{userRewards.totalPoints.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Star className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Current Tier</p>
                <div className="flex items-center space-x-2 mt-1">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getTierColor(userRewards.currentTier)}`}>
                    {userRewards.currentTier}
                  </span>
                  <TierIcon className="w-5 h-5 text-gray-400" />
                </div>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Crown className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Next Tier Progress</p>
                <div className="mt-2">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{userRewards.nextTierPoints} points to go</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                      style={{ width: `${((2000 - userRewards.nextTierPoints) / 2000) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Category Filter */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Browse Rewards</h3>
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-100 text-blue-700 border-2 border-blue-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="font-medium">{category.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Rewards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRewards.map((reward, index) => {
            const Icon = reward.icon;
            const canAfford = userRewards.totalPoints >= reward.pointsCost;
            const isRedeemed = userRewards.redeemedRewards.includes(reward.id);
            
            return (
              <motion.div
                key={reward.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`bg-white rounded-lg shadow-sm overflow-hidden ${
                  !reward.available ? 'opacity-60' : ''
                }`}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg ${reward.bgColor}`}>
                      <Icon className={`w-6 h-6 ${reward.color}`} />
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Points Required</p>
                      <p className="text-lg font-bold text-gray-900">{reward.pointsCost.toLocaleString()}</p>
                    </div>
                  </div>

                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{reward.title}</h4>
                  <p className="text-gray-600 text-sm mb-4">{reward.description}</p>

                  {reward.cashValue && (
                    <div className="flex items-center space-x-2 mb-4">
                      <span className="text-sm text-gray-500">Value:</span>
                      <span className="text-sm font-medium text-green-600">${reward.cashValue}</span>
                    </div>
                  )}

                  {reward.expiresAt && (
                    <div className="flex items-center space-x-2 mb-4 text-orange-600">
                      <Clock className="w-4 h-4" />
                      <span className="text-sm">Expires {reward.expiresAt.toLocaleDateString()}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-gray-600">{reward.popularity}% popular</span>
                    </div>

                    <button
                      onClick={() => handleRedeemReward(reward.id)}
                      disabled={!canAfford || !reward.available || isRedeemed}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                        isRedeemed
                          ? 'bg-green-100 text-green-700 cursor-not-allowed'
                          : canAfford && reward.available
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      {isRedeemed ? (
                        <div className="flex items-center space-x-1">
                          <Check className="w-4 h-4" />
                          <span>Redeemed</span>
                        </div>
                      ) : canAfford && reward.available ? (
                        'Redeem'
                      ) : !reward.available ? (
                        'Unavailable'
                      ) : (
                        'Not Enough Points'
                      )}
                    </button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {filteredRewards.length === 0 && (
          <div className="text-center py-12">
            <Gift className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No rewards found</h3>
            <p className="text-gray-500">Try selecting a different category</p>
          </div>
        )}
          </div>

          {/* Sidebar */}
          <div>
            <QuickActions currentPage="rewards" />
          </div>
        </div>
      </div>

      {/* Coming Soon Popup */}
      {showComingSoon && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 w-full max-w-md mx-4 text-center">
            <div className="flex justify-end mb-4">
              <button
                onClick={() => setShowComingSoon(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Rewards Coming Soon!</h3>
              <p className="text-gray-600 text-lg">
                Hold on tight, Fido! 🐕
              </p>
              <p className="text-gray-500 mt-2">
                We're working hard to bring you amazing rewards. Stay tuned for exciting updates!
              </p>
            </div>

            <button
              onClick={() => setShowComingSoon(false)}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium py-3 px-6 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all"
            >
              Got it!
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
