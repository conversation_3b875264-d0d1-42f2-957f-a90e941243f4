'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Package,
  Plus,
  Edit3,
  Eye,
  Trash2,
  Calendar,
  Clock,
  DollarSign,
  Users,
  Search,
  Filter,
  MoreVertical,
  Star,
  TrendingUp,
  BarChart3,
  Settings,
  Copy,
  Share2,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface Service {
  id: string;
  name: string;
  category: string;
  description: string;
  price: number;
  duration: number;
  active: boolean;
  bookings: number;
  rating: number;
  totalRevenue: number;
  lastBooked: string;
  images: string[];
  requirements?: string[];
  addOns?: { name: string; price: number }[];
}

const mockServices: Service[] = [
  {
    id: 'SRV001',
    name: 'Full Service Grooming',
    category: 'Grooming',
    description: 'Complete grooming package including bath, haircut, nail trimming, and ear cleaning',
    price: 85,
    duration: 120,
    active: true,
    bookings: 45,
    rating: 4.8,
    totalRevenue: 3825,
    lastBooked: '2024-01-19',
    images: ['/api/placeholder/300/200'],
    requirements: ['Pet must be up to date on vaccinations', 'No aggressive behavior'],
    addOns: [
      { name: 'Nail <PERSON>', price: 10 },
      { name: 'Teeth Brushing', price: 15 },
      { name: 'Flea Treatment', price: 25 }
    ]
  },
  {
    id: 'SRV002',
    name: 'Basic Grooming',
    category: 'Grooming',
    description: 'Essential grooming services including bath, brush, and nail trim',
    price: 55,
    duration: 90,
    active: true,
    bookings: 32,
    rating: 4.6,
    totalRevenue: 1760,
    lastBooked: '2024-01-18',
    images: ['/api/placeholder/300/200'],
    requirements: ['Pet must be up to date on vaccinations'],
    addOns: [
      { name: 'Ear Cleaning', price: 8 },
      { name: 'Nail Polish', price: 10 }
    ]
  },
  {
    id: 'SRV003',
    name: 'Veterinary Checkup',
    category: 'Medical',
    description: 'Comprehensive health examination and consultation',
    price: 120,
    duration: 60,
    active: true,
    bookings: 28,
    rating: 4.9,
    totalRevenue: 3360,
    lastBooked: '2024-01-20',
    images: ['/api/placeholder/300/200'],
    requirements: ['Bring vaccination records', 'Pet should be fasted for blood work if needed'],
    addOns: [
      { name: 'Blood Work', price: 80 },
      { name: 'X-Ray', price: 150 },
      { name: 'Vaccination Update', price: 45 }
    ]
  },
  {
    id: 'SRV004',
    name: 'Dental Cleaning',
    category: 'Medical',
    description: 'Professional dental cleaning and oral health assessment',
    price: 150,
    duration: 90,
    active: true,
    bookings: 18,
    rating: 4.7,
    totalRevenue: 2700,
    lastBooked: '2024-01-17',
    images: ['/api/placeholder/300/200'],
    requirements: ['Pre-anesthetic blood work required', 'Pet must be fasted 12 hours prior'],
    addOns: [
      { name: 'Tooth Extraction', price: 75 },
      { name: 'Dental X-Ray', price: 100 }
    ]
  },
  {
    id: 'SRV005',
    name: 'Pet Sitting (Daily)',
    category: 'Boarding',
    description: 'Daily pet sitting service at your home',
    price: 45,
    duration: 480,
    active: false,
    bookings: 12,
    rating: 4.5,
    totalRevenue: 540,
    lastBooked: '2024-01-10',
    images: ['/api/placeholder/300/200'],
    requirements: ['Home visit required before service', 'Emergency contact information needed'],
    addOns: [
      { name: 'Plant Watering', price: 5 },
      { name: 'Mail Collection', price: 3 }
    ]
  }
];

const categories = ['All', 'Grooming', 'Medical', 'Boarding', 'Training'];

export default function ServicesPage() {
  const { user } = useAuth();
  const [services, setServices] = useState(mockServices);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const filteredServices = services.filter(service => {
    const matchesCategory = selectedCategory === 'All' || service.category === selectedCategory;
    const matchesSearch = service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const totalRevenue = services.reduce((sum, service) => sum + service.totalRevenue, 0);
  const totalBookings = services.reduce((sum, service) => sum + service.bookings, 0);
  const activeServices = services.filter(service => service.active).length;
  const averageRating = services.reduce((sum, service) => sum + service.rating, 0) / services.length;

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Service Management</h1>
            <p className="text-cool-600">Manage your services, pricing, and availability</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="btn-secondary">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button 
              onClick={() => setShowAddModal(true)}
              className="btn-primary"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Service
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-primary-100 rounded-xl">
                <Package className="w-6 h-6 text-primary-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+2 this month</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{activeServices}</h3>
            <p className="text-cool-600 text-sm">Active Services</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-secondary-100 rounded-xl">
                <DollarSign className="w-6 h-6 text-secondary-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+12.5%</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">${totalRevenue.toLocaleString()}</h3>
            <p className="text-cool-600 text-sm">Total Revenue</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent-100 rounded-xl">
                <Calendar className="w-6 h-6 text-accent-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+8.2%</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{totalBookings}</h3>
            <p className="text-cool-600 text-sm">Total Bookings</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-warm-100 rounded-xl">
                <Star className="w-6 h-6 text-warm-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+0.2</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{averageRating.toFixed(1)}</h3>
            <p className="text-cool-600 text-sm">Average Rating</p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="glass-card rounded-2xl p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Category Filter */}
              <div className="flex bg-white/50 rounded-lg p-1">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                      selectedCategory === category 
                        ? 'bg-white text-primary-600 shadow-sm' 
                        : 'text-cool-600 hover:text-cool-800'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>

              {/* View Mode Toggle */}
              <div className="flex bg-white/50 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 rounded-md transition-all duration-200 ${
                    viewMode === 'grid' ? 'bg-white text-primary-600 shadow-sm' : 'text-cool-600'
                  }`}
                >
                  Grid
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 rounded-md transition-all duration-200 ${
                    viewMode === 'list' ? 'bg-white text-primary-600 shadow-sm' : 'text-cool-600'
                  }`}
                >
                  List
                </button>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cool-500" />
                <input
                  type="text"
                  placeholder="Search services..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <button className="btn-secondary">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
        </div>

        {/* Services Grid/List */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredServices.map((service) => (
              <div key={service.id} className="glass-card rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                <div className="relative">
                  <img 
                    src={service.images[0]} 
                    alt={service.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 right-4 flex gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      service.active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                    }`}>
                      {service.active ? 'Active' : 'Inactive'}
                    </span>
                    <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
                      {service.category}
                    </span>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-bold text-cool-800 text-lg">{service.name}</h3>
                    <button className="p-1 hover:bg-cool-100 rounded transition-colors duration-200">
                      <MoreVertical className="w-4 h-4 text-cool-500" />
                    </button>
                  </div>
                  
                  <p className="text-cool-600 text-sm mb-4 line-clamp-2">{service.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-cool-500 mb-1">Price</p>
                      <p className="font-bold text-cool-800">${service.price}</p>
                    </div>
                    <div>
                      <p className="text-xs text-cool-500 mb-1">Duration</p>
                      <p className="font-bold text-cool-800">{service.duration}min</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium text-cool-700">{service.rating}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-cool-500" />
                      <span className="text-sm text-cool-600">{service.bookings} bookings</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <button 
                      onClick={() => setSelectedService(service)}
                      className="flex-1 btn-primary text-sm py-2"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </button>
                    <button className="btn-secondary text-sm py-2 px-3">
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <button className="btn-secondary text-sm py-2 px-3 text-red-600 hover:bg-red-50">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="glass-card rounded-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Service</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Category</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Price</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Duration</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Bookings</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Revenue</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/20">
                  {filteredServices.map((service) => (
                    <tr key={service.id} className="hover:bg-white/30 transition-colors duration-200">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <img 
                            src={service.images[0]} 
                            alt={service.name}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                          <div>
                            <p className="font-medium text-cool-800">{service.name}</p>
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3 text-yellow-500 fill-current" />
                              <span className="text-xs text-cool-600">{service.rating}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
                          {service.category}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className="font-bold text-cool-800">${service.price}</span>
                      </td>
                      <td className="px-6 py-4">
                        <span className="text-cool-700">{service.duration}min</span>
                      </td>
                      <td className="px-6 py-4">
                        <span className="text-cool-700">{service.bookings}</span>
                      </td>
                      <td className="px-6 py-4">
                        <span className="font-bold text-cool-800">${service.totalRevenue.toLocaleString()}</span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          service.active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                        }`}>
                          {service.active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex gap-2">
                          <button 
                            onClick={() => setSelectedService(service)}
                            className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200">
                            <Edit3 className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
