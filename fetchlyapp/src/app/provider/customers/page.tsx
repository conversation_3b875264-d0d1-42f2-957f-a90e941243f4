'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Users,
  Search,
  Filter,
  Plus,
  Eye,
  Edit3,
  MessageCircle,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  Star,
  Heart,
  PawPrint,
  Download,
  Upload,
  MoreVertical,
  UserPlus,
  Send,
  Gift,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react';

interface Pet {
  id: string;
  name: string;
  type: string;
  breed: string;
  age: number;
  weight: number;
  color: string;
  notes?: string;
  medicalHistory?: string[];
  lastVisit?: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  joinDate: string;
  lastVisit: string;
  totalSpent: number;
  totalBookings: number;
  status: 'Active' | 'VIP' | 'Inactive' | 'New';
  rating: number;
  pets: Pet[];
  notes?: string;
  preferredServices: string[];
  communicationPreference: 'email' | 'phone' | 'sms';
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

const mockCustomers: Customer[] = [
  {
    id: 'CUST001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Oak Street, San Francisco, CA 94102',
    joinDate: '2023-06-15',
    lastVisit: '2024-01-15',
    totalSpent: 1250,
    totalBookings: 18,
    status: 'VIP',
    rating: 5.0,
    communicationPreference: 'email',
    preferredServices: ['Full Service Grooming', 'Nail Trimming'],
    pets: [
      {
        id: 'PET001',
        name: 'Buddy',
        type: 'Dog',
        breed: 'Golden Retriever',
        age: 4,
        weight: 65,
        color: 'Golden',
        notes: 'Very friendly, loves treats',
        medicalHistory: ['Vaccinated', 'Neutered'],
        lastVisit: '2024-01-15'
      },
      {
        id: 'PET002',
        name: 'Luna',
        type: 'Cat',
        breed: 'Persian',
        age: 2,
        weight: 8,
        color: 'White',
        notes: 'Shy but gentle',
        medicalHistory: ['Vaccinated', 'Spayed'],
        lastVisit: '2024-01-10'
      }
    ],
    emergencyContact: {
      name: 'Mike Johnson',
      phone: '+****************',
      relationship: 'Husband'
    }
  },
  {
    id: 'CUST002',
    name: 'Mike Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Pine Avenue, San Francisco, CA 94103',
    joinDate: '2023-09-22',
    lastVisit: '2024-01-12',
    totalSpent: 890,
    totalBookings: 12,
    status: 'Active',
    rating: 4.8,
    communicationPreference: 'phone',
    preferredServices: ['Veterinary Checkup', 'Dental Cleaning'],
    pets: [
      {
        id: 'PET003',
        name: 'Max',
        type: 'Dog',
        breed: 'German Shepherd',
        age: 6,
        weight: 75,
        color: 'Black and Tan',
        notes: 'Well-trained, protective',
        medicalHistory: ['Vaccinated', 'Hip dysplasia monitoring'],
        lastVisit: '2024-01-12'
      }
    ],
    emergencyContact: {
      name: 'Lisa Wilson',
      phone: '+****************',
      relationship: 'Wife'
    }
  },
  {
    id: 'CUST003',
    name: 'Emily Davis',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Elm Drive, San Francisco, CA 94104',
    joinDate: '2023-11-08',
    lastVisit: '2024-01-08',
    totalSpent: 1680,
    totalBookings: 22,
    status: 'VIP',
    rating: 4.9,
    communicationPreference: 'sms',
    preferredServices: ['Full Service Grooming', 'Basic Grooming', 'Pet Sitting'],
    pets: [
      {
        id: 'PET004',
        name: 'Bella',
        type: 'Dog',
        breed: 'Poodle',
        age: 3,
        weight: 45,
        color: 'Cream',
        notes: 'Hypoallergenic coat, loves water',
        medicalHistory: ['Vaccinated', 'Spayed'],
        lastVisit: '2024-01-08'
      },
      {
        id: 'PET005',
        name: 'Charlie',
        type: 'Dog',
        breed: 'Beagle',
        age: 5,
        weight: 30,
        color: 'Tricolor',
        notes: 'Food motivated, escape artist',
        medicalHistory: ['Vaccinated', 'Neutered', 'Allergic to chicken'],
        lastVisit: '2024-01-05'
      }
    ]
  },
  {
    id: 'CUST004',
    name: 'David Brown',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Maple Court, San Francisco, CA 94105',
    joinDate: '2024-01-02',
    lastVisit: '2024-01-19',
    totalSpent: 320,
    totalBookings: 3,
    status: 'New',
    rating: 4.7,
    communicationPreference: 'email',
    preferredServices: ['Dental Cleaning'],
    pets: [
      {
        id: 'PET006',
        name: 'Rocky',
        type: 'Dog',
        breed: 'Bulldog',
        age: 7,
        weight: 50,
        color: 'Brindle',
        notes: 'Senior dog, breathing issues',
        medicalHistory: ['Vaccinated', 'Neutered', 'Respiratory monitoring'],
        lastVisit: '2024-01-19'
      }
    ]
  }
];

export default function CustomersPage() {
  const { user } = useAuth();
  const [customers, setCustomers] = useState(mockCustomers);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesStatus = statusFilter === 'All' || customer.status === statusFilter;
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.pets.some(pet => pet.name.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesStatus && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'VIP': return 'bg-yellow-100 text-yellow-700';
      case 'Active': return 'bg-green-100 text-green-700';
      case 'New': return 'bg-blue-100 text-blue-700';
      case 'Inactive': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const totalCustomers = customers.length;
  const vipCustomers = customers.filter(c => c.status === 'VIP').length;
  const totalRevenue = customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
  const averageSpent = totalRevenue / totalCustomers;

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Customer Management</h1>
            <p className="text-cool-600">Manage your customer relationships and communication</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="btn-secondary">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button 
              onClick={() => setShowAddModal(true)}
              className="btn-primary"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Add Customer
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-primary-100 rounded-xl">
                <Users className="w-6 h-6 text-primary-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+5 this month</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{totalCustomers}</h3>
            <p className="text-cool-600 text-sm">Total Customers</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-yellow-100 rounded-xl">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+2 this month</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{vipCustomers}</h3>
            <p className="text-cool-600 text-sm">VIP Customers</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-secondary-100 rounded-xl">
                <DollarSign className="w-6 h-6 text-secondary-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+12.5%</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">${totalRevenue.toLocaleString()}</h3>
            <p className="text-cool-600 text-sm">Total Revenue</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent-100 rounded-xl">
                <TrendingUp className="w-6 h-6 text-accent-600" />
              </div>
              <span className="text-green-500 text-sm font-medium">+8.2%</span>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">${Math.round(averageSpent)}</h3>
            <p className="text-cool-600 text-sm">Average Spent</p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="glass-card rounded-2xl p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="All">All Status</option>
                <option value="VIP">VIP</option>
                <option value="Active">Active</option>
                <option value="New">New</option>
                <option value="Inactive">Inactive</option>
              </select>

              {/* View Mode Toggle */}
              <div className="flex bg-white/50 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                    viewMode === 'list' ? 'bg-white text-primary-600 shadow-sm' : 'text-cool-600'
                  }`}
                >
                  List View
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                    viewMode === 'grid' ? 'bg-white text-primary-600 shadow-sm' : 'text-cool-600'
                  }`}
                >
                  Grid View
                </button>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cool-500" />
                <input
                  type="text"
                  placeholder="Search customers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <button className="btn-secondary">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
        </div>

        {/* Customers Content */}
        {viewMode === 'list' ? (
          <div className="glass-card rounded-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Customer</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Pet(s)</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Contact</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Last Visit</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Total Spent</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/20">
                  {filteredCustomers.map((customer) => (
                    <tr key={customer.id} className="hover:bg-white/30 transition-colors duration-200">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                            <span className="text-primary-600 font-bold">
                              {customer.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-cool-800">{customer.name}</p>
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3 text-yellow-500 fill-current" />
                              <span className="text-xs text-cool-600">{customer.rating}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          {customer.pets.slice(0, 2).map((pet) => (
                            <div key={pet.id} className="flex items-center gap-2">
                              <PawPrint className="w-3 h-3 text-cool-500" />
                              <span className="text-sm text-cool-700">{pet.name} ({pet.breed})</span>
                            </div>
                          ))}
                          {customer.pets.length > 2 && (
                            <span className="text-xs text-cool-500">+{customer.pets.length - 2} more</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Mail className="w-3 h-3 text-cool-500" />
                            <span className="text-sm text-cool-700">{customer.email}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="w-3 h-3 text-cool-500" />
                            <span className="text-sm text-cool-700">{customer.phone}</span>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-cool-500" />
                          <span className="text-cool-700">{customer.lastVisit}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <p className="font-bold text-cool-800">${customer.totalSpent}</p>
                          <p className="text-xs text-cool-600">{customer.totalBookings} bookings</p>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(customer.status)}`}>
                          {customer.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex gap-2">
                          <button 
                            onClick={() => setSelectedCustomer(customer)}
                            className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200">
                            <MessageCircle className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-accent-600 hover:bg-accent-100 rounded-lg transition-colors duration-200">
                            <Edit3 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCustomers.map((customer) => (
              <div key={customer.id} className="glass-card rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-bold text-lg">
                        {customer.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-bold text-cool-800">{customer.name}</h3>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm text-cool-600">{customer.rating}</span>
                      </div>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                    {customer.status}
                  </span>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-cool-500" />
                    <span className="text-sm text-cool-700">{customer.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-cool-500" />
                    <span className="text-sm text-cool-700">{customer.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-cool-500" />
                    <span className="text-sm text-cool-700">Last visit: {customer.lastVisit}</span>
                  </div>
                </div>

                <div className="border-t border-white/20 pt-4 mb-4">
                  <h4 className="font-medium text-cool-800 mb-2">Pets ({customer.pets.length})</h4>
                  <div className="space-y-1">
                    {customer.pets.slice(0, 2).map((pet) => (
                      <div key={pet.id} className="flex items-center gap-2">
                        <PawPrint className="w-3 h-3 text-cool-500" />
                        <span className="text-sm text-cool-700">{pet.name} - {pet.breed}</span>
                      </div>
                    ))}
                    {customer.pets.length > 2 && (
                      <span className="text-xs text-cool-500">+{customer.pets.length - 2} more pets</span>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-xs text-cool-500 mb-1">Total Spent</p>
                    <p className="font-bold text-cool-800">${customer.totalSpent}</p>
                  </div>
                  <div>
                    <p className="text-xs text-cool-500 mb-1">Bookings</p>
                    <p className="font-bold text-cool-800">{customer.totalBookings}</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button 
                    onClick={() => setSelectedCustomer(customer)}
                    className="flex-1 btn-primary text-sm py-2"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View
                  </button>
                  <button className="btn-secondary text-sm py-2 px-3">
                    <MessageCircle className="w-4 h-4" />
                  </button>
                  <button className="btn-secondary text-sm py-2 px-3">
                    <Edit3 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
