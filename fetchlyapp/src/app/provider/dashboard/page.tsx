'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProvider } from '@/contexts/ProviderContext';
import { useSearchParams } from 'next/navigation';
import {
  BarChart3,
  Calendar,
  DollarSign,
  Star,
  Users,
  Settings,
  Bell,

  Clock,
  Activity,
  Home,
  Package,
  User,
  Wallet,
  CalendarDays,
  ThumbsUp,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Lock,
  AlertTriangle
} from 'lucide-react';

// Import tab components
import CalendarTab from '@/components/provider/dashboard/CalendarTab';
import BookingsTab from '@/components/provider/dashboard/BookingsTab';
import ClientsTab from '@/components/provider/dashboard/ClientsTab';
import ServicesTab from '@/components/provider/dashboard/ServicesTab';
import WalletTab from '@/components/provider/dashboard/WalletTab';
import ReviewsTab from '@/components/provider/dashboard/ReviewsTab';
import AnalyticsTab from '@/components/provider/dashboard/AnalyticsTab';
import BlogTab from '@/components/provider/dashboard/BlogTab';
import IntegrationsTab from '@/components/provider/dashboard/IntegrationsTab';
import NotificationsTab from '@/components/provider/dashboard/NotificationsTab';
import SettingsTab from '@/components/provider/dashboard/SettingsTab';
import SubscriptionTab from '@/components/provider/dashboard/SubscriptionTab';
import { useRouter } from 'next/navigation';

// Sidebar navigation items
const sidebarItems = [
  { id: 'overview', name: 'Overview', icon: Home },
  { id: 'calendar', name: 'Calendar', icon: CalendarDays },
  { id: 'bookings', name: 'Bookings', icon: Clock },
  { id: 'clients', name: 'Clients', icon: Users },
  { id: 'services', name: 'Services', icon: Package },
  { id: 'wallet', name: 'Wallet', icon: Wallet },
  { id: 'reviews', name: 'Reviews', icon: ThumbsUp },
  { id: 'analytics', name: 'Analytics', icon: BarChart3 },
  { id: 'subscription', name: 'Subscription', icon: Lock },
  { id: 'blog', name: 'Blog', icon: ExternalLink },
  { id: 'integrations', name: 'Integrations', icon: ExternalLink },
  { id: 'notifications', name: 'Notifications', icon: Bell },
  { id: 'settings', name: 'Settings', icon: Settings }
];

// Main Dashboard Component
export default function ProviderDashboard() {
  const { user } = useAuth();
  const {
    provider,
    services,
    bookings,
    isLoading,
    error,
    getDashboardStats
  } = useProvider();

  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Authentication and role checking
  useEffect(() => {
    if (!user) {
      console.log('🔧 No user found, redirecting to signin');
      router.push('/auth/signin');
      return;
    }

    // Redirect non-providers to their appropriate dashboard
    if (user.role !== 'provider') {
      console.log('🔧 User is not a provider, redirecting to pet owner dashboard');
      router.push('/dashboard');
      return;
    }

    console.log('🔧 Provider user authenticated:', user.role);
  }, [user, router]);

  // Handle tab query parameter
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['overview', 'bookings', 'services', 'earnings', 'reviews', 'analytics', 'subscription', 'blog', 'integrations', 'notifications', 'settings'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Quick actions data - moved inside component to access setActiveTab
  const quickActions = [
    {
      id: 'add-service',
      name: 'Add Service',
      icon: Package,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => {
        setActiveTab('services');
        // Trigger add service modal in ServicesTab
        setTimeout(() => {
          const addServiceEvent = new CustomEvent('openAddServiceModal');
          window.dispatchEvent(addServiceEvent);
        }, 100);
      }
    },
    {
      id: 'view-calendar',
      name: 'View Calendar',
      icon: Calendar,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => setActiveTab('calendar')
    },
    {
      id: 'manage-bookings',
      name: 'Manage Bookings',
      icon: Clock,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => setActiveTab('bookings')
    },
    {
      id: 'view-earnings',
      name: 'View Earnings',
      icon: DollarSign,
      color: 'bg-yellow-500 hover:bg-yellow-600',
      action: () => setActiveTab('wallet')
    }
  ];

  // Get dashboard statistics
  const stats = getDashboardStats();

  // Function to render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return null; // Will render overview content below
      case 'calendar':
        return <CalendarTab />;
      case 'bookings':
        return <BookingsTab />;
      case 'clients':
        return <ClientsTab />;
      case 'services':
        return <ServicesTab />;
      case 'wallet':
        return <WalletTab />;
      case 'reviews':
        return <ReviewsTab />;
      case 'analytics':
        return <AnalyticsTab />;
      case 'subscription':
        return <SubscriptionTab />;
      case 'blog':
        return <BlogTab />;
      case 'integrations':
        return <IntegrationsTab />;
      case 'notifications':
        return <NotificationsTab />;
      case 'settings':
        return <SettingsTab />;
      default:
        return null;
    }
  };

  // Helper functions
  const formatDate = (date: any) => {
    if (date && typeof date.toDate === 'function') {
      return date.toDate().toLocaleDateString();
    }
    return new Date(date).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Dashboard</h2>
          <p className="text-gray-500">Please wait while we fetch your data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Something went wrong</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Access control
  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Lock className="w-8 h-8 text-gray-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Access Denied</h1>
          <p className="text-gray-600">You need to be logged in as a service provider to access this dashboard.</p>
        </div>
      </div>
    );
  }

  // Main dashboard render
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-50">
      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} bg-gradient-to-b from-green-600 to-blue-600 shadow-xl transition-all duration-300 ease-in-out fixed lg:relative left-0 top-16 sm:top-20 lg:top-0 h-[calc(100vh-4rem)] sm:h-[calc(100vh-5rem)] lg:h-screen z-30 ${sidebarCollapsed ? 'lg:block' : 'lg:block'}`}>
          {/* Sidebar Header */}
          <div className="p-6 border-b border-white/20">
            <div className="flex items-center justify-between">
              {!sidebarCollapsed && (
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-xl overflow-hidden">
                    <img
                      src={user?.avatar || '/favicon.png'}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/favicon.png';
                      }}
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-white">Welcome to your</p>
                    <p className="text-xs text-green-100">Dashboard Management System</p>
                  </div>
                </div>
              )}
              <button
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="p-2 rounded-lg hover:bg-white/20 transition-colors"
              >
                {sidebarCollapsed ? (
                  <ChevronRight className="w-5 h-5 text-white" />
                ) : (
                  <ChevronLeft className="w-5 h-5 text-white" />
                )}
              </button>
            </div>
          </div>

          {/* Sidebar Navigation */}
          <nav className="p-4 space-y-2">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-all duration-200 ${
                  activeTab === item.id
                    ? 'bg-white/20 text-white backdrop-blur-sm'
                    : 'text-green-100 hover:bg-white/10 hover:text-white'
                }`}
              >
                <item.icon className="w-5 h-5" />
                {!sidebarCollapsed && <span className="font-medium">{item.name}</span>}
              </button>
            ))}
          </nav>

          {/* Sidebar Footer */}
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/20">
            {!sidebarCollapsed && provider && (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {provider.businessName}
                  </p>
                  <p className="text-xs text-green-100 truncate">
                    {provider.membershipTier === 'pro' ? 'Pro Member' : 'Free Member'}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className={`flex-1 lg:${sidebarCollapsed ? 'ml-16' : 'ml-64'} transition-all duration-300`}>
          {/* Main Content Area */}
          <main className="pt-20 lg:pt-6 p-3 sm:p-6">
            {/* Render tab content or overview */}
            {activeTab !== 'overview' ? (
              renderTabContent()
            ) : (
              <div className="space-y-6">
                <h1 className="text-2xl font-bold text-gray-800 mb-4">Overview</h1>
                {/* Quick Stats Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
                  <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm font-medium text-gray-600">Total Revenue</p>
                        <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800">${stats.totalRevenue}</p>
                        {stats.totalRevenue > 0 ? (
                          <p className="text-xs sm:text-sm text-gray-500 mt-1">
                            From {stats.totalBookings} bookings
                          </p>
                        ) : (
                          <p className="text-xs sm:text-sm text-gray-500 mt-1">
                            No revenue yet
                          </p>
                        )}
                      </div>
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                        <DollarSign className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                        <p className="text-3xl font-bold text-gray-800">{stats.totalBookings}</p>
                        <p className="text-sm text-green-600 mt-1">
                          <Calendar className="w-4 h-4 inline mr-1" />
                          {bookings?.filter(b => b.status === 'pending').length || 0} pending
                        </p>
                      </div>
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-xl flex items-center justify-center">
                        <Users className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Average Rating</p>
                        <p className="text-3xl font-bold text-gray-800">{stats.rating}</p>
                        <p className="text-sm text-yellow-600 mt-1">
                          <Star className="w-4 h-4 inline mr-1" />
                          {stats.reviewCount} reviews
                        </p>
                      </div>
                      <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                        <Star className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Response Rate</p>
                        <p className="text-3xl font-bold text-gray-800">{stats.responseRate}%</p>
                        <p className="text-sm text-blue-600 mt-1">
                          <Clock className="w-4 h-4 inline mr-1" />
                          {stats.completionRate}% completion
                        </p>
                      </div>
                      <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl flex items-center justify-center">
                        <Activity className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {quickActions.map((action) => (
                      <button
                        key={action.id}
                        onClick={action.action}
                        className={`${action.color} text-white p-4 rounded-xl transition-all duration-200 hover:scale-105 hover:shadow-lg`}
                      >
                        <action.icon className="w-6 h-6 mx-auto mb-2" />
                        <p className="text-sm font-medium">{action.name}</p>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Recent Activity & Provider Info */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Recent Bookings */}
                  <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-800">Recent Bookings</h3>
                      <button
                        onClick={() => setActiveTab('bookings')}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        View All
                      </button>
                    </div>
                    {!bookings || bookings.length === 0 ? (
                      <div className="text-center py-8">
                        <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                        <p className="text-gray-500">No bookings yet</p>
                        <p className="text-sm text-gray-400">Your bookings will appear here</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {bookings.slice(0, 5).map((booking) => (
                          <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                              <p className="font-medium text-gray-800">{booking.customerName}</p>
                              <p className="text-sm text-gray-600">{booking.petName} - {booking.petType}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium text-gray-800">{formatDate(booking.date)}</p>
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                                {booking.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Provider Profile Summary */}
                  <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-800">Profile Summary</h3>
                      <button
                        onClick={() => setActiveTab('profile')}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        Edit Profile
                      </button>
                    </div>
                    {provider ? (
                      <div className="space-y-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                            <User className="w-8 h-8 text-gray-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-800">{provider.businessName}</h4>
                            <p className="text-sm text-gray-600">{provider.ownerName}</p>
                            <div className="flex items-center mt-1">
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                                provider.membershipTier === 'pro'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {provider.membershipTier === 'pro' ? 'Pro Member' : 'Free Member'}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                          <div>
                            <p className="text-sm text-gray-600">Services</p>
                            <p className="font-semibold text-gray-800">{services?.length || 0}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Rating</p>
                            <p className="font-semibold text-gray-800">{stats.rating}/5.0</p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <User className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                        <p className="text-gray-500">Complete your profile</p>
                        <button
                          onClick={() => router.push('/provider/profile')}
                          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Set Up Profile
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </main>
        </div>
      </div>

    </div>
  );
}
