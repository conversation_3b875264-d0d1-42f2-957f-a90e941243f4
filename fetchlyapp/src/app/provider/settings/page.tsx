'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Settings,
  Bell,
  Lock,
  Shield,
  Key,
  Download,
  Trash2,
  HelpCircle,
  MessageCircle,
  ExternalLink,
  ChevronRight,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Smartphone,
  Mail,
  Globe,
  CreditCard,
  Users,
  Calendar,
  Package,
  BarChart3,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

interface NotificationSettings {
  email: {
    bookings: boolean;
    cancellations: boolean;
    payments: boolean;
    reviews: boolean;
    marketing: boolean;
  };
  sms: {
    bookings: boolean;
    cancellations: boolean;
    emergencies: boolean;
  };
  push: {
    bookings: boolean;
    messages: boolean;
    updates: boolean;
  };
}

interface BusinessSettings {
  autoAcceptBookings: boolean;
  requireDeposit: boolean;
  allowCancellations: boolean;
  cancellationWindow: number;
  bufferTime: number;
  maxAdvanceBooking: number;
}

export default function SettingsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('notifications');
  const [showPassword, setShowPassword] = useState(false);
  const [notifications, setNotifications] = useState<NotificationSettings>({
    email: {
      bookings: true,
      cancellations: true,
      payments: true,
      reviews: true,
      marketing: false
    },
    sms: {
      bookings: true,
      cancellations: true,
      emergencies: true
    },
    push: {
      bookings: true,
      messages: true,
      updates: false
    }
  });

  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({
    autoAcceptBookings: true,
    requireDeposit: false,
    allowCancellations: true,
    cancellationWindow: 24,
    bufferTime: 15,
    maxAdvanceBooking: 90
  });

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'business', label: 'Business', icon: Package },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'billing', label: 'Billing', icon: CreditCard },
    { id: 'data', label: 'Data & Privacy', icon: Lock },
    { id: 'support', label: 'Support', icon: HelpCircle }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Settings</h1>
            <p className="text-cool-600">Manage your account preferences and security settings</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button className="btn-secondary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Reset to Defaults
            </button>
            <button className="btn-primary">
              <Save className="w-4 h-4 mr-2" />
              Save All Changes
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="glass-card rounded-xl p-4 sticky top-24">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-primary-500 text-white shadow-lg'
                          : 'text-cool-700 hover:bg-primary-50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="glass-card rounded-xl p-8">
              {activeTab === 'notifications' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-bold text-cool-800 mb-2">Notification Preferences</h2>
                    <p className="text-cool-600">Choose how you want to be notified about important events</p>
                  </div>

                  {/* Email Notifications */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4 flex items-center gap-2">
                      <Mail className="w-5 h-5" />
                      Email Notifications
                    </h3>
                    <div className="space-y-4">
                      {Object.entries(notifications.email).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                          <div>
                            <p className="font-medium text-cool-800 capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
                            <p className="text-sm text-cool-600">
                              {key === 'bookings' && 'Get notified when new bookings are made'}
                              {key === 'cancellations' && 'Receive alerts for booking cancellations'}
                              {key === 'payments' && 'Payment confirmations and failed payments'}
                              {key === 'reviews' && 'New customer reviews and ratings'}
                              {key === 'marketing' && 'Product updates and promotional content'}
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input 
                              type="checkbox" 
                              checked={value}
                              onChange={(e) => setNotifications({
                                ...notifications,
                                email: { ...notifications.email, [key]: e.target.checked }
                              })}
                              className="sr-only peer" 
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* SMS Notifications */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4 flex items-center gap-2">
                      <Smartphone className="w-5 h-5" />
                      SMS Notifications
                    </h3>
                    <div className="space-y-4">
                      {Object.entries(notifications.sms).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                          <div>
                            <p className="font-medium text-cool-800 capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
                            <p className="text-sm text-cool-600">
                              {key === 'bookings' && 'SMS alerts for new bookings'}
                              {key === 'cancellations' && 'Text notifications for cancellations'}
                              {key === 'emergencies' && 'Urgent notifications and emergencies'}
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input 
                              type="checkbox" 
                              checked={value}
                              onChange={(e) => setNotifications({
                                ...notifications,
                                sms: { ...notifications.sms, [key]: e.target.checked }
                              })}
                              className="sr-only peer" 
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Push Notifications */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4 flex items-center gap-2">
                      <Bell className="w-5 h-5" />
                      Push Notifications
                    </h3>
                    <div className="space-y-4">
                      {Object.entries(notifications.push).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                          <div>
                            <p className="font-medium text-cool-800 capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
                            <p className="text-sm text-cool-600">
                              {key === 'bookings' && 'Browser notifications for bookings'}
                              {key === 'messages' && 'New customer messages'}
                              {key === 'updates' && 'App updates and announcements'}
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input 
                              type="checkbox" 
                              checked={value}
                              onChange={(e) => setNotifications({
                                ...notifications,
                                push: { ...notifications.push, [key]: e.target.checked }
                              })}
                              className="sr-only peer" 
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'business' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-bold text-cool-800 mb-2">Business Settings</h2>
                    <p className="text-cool-600">Configure how your business operates and handles bookings</p>
                  </div>

                  {/* Booking Settings */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4 flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Booking Management
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                        <div>
                          <p className="font-medium text-cool-800">Auto-Accept Bookings</p>
                          <p className="text-sm text-cool-600">Automatically confirm new bookings without manual approval</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={businessSettings.autoAcceptBookings}
                            onChange={(e) => setBusinessSettings({
                              ...businessSettings,
                              autoAcceptBookings: e.target.checked
                            })}
                            className="sr-only peer" 
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                        <div>
                          <p className="font-medium text-cool-800">Require Deposit</p>
                          <p className="text-sm text-cool-600">Require customers to pay a deposit when booking</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={businessSettings.requireDeposit}
                            onChange={(e) => setBusinessSettings({
                              ...businessSettings,
                              requireDeposit: e.target.checked
                            })}
                            className="sr-only peer" 
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                        <div>
                          <p className="font-medium text-cool-800">Allow Cancellations</p>
                          <p className="text-sm text-cool-600">Let customers cancel their bookings</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={businessSettings.allowCancellations}
                            onChange={(e) => setBusinessSettings({
                              ...businessSettings,
                              allowCancellations: e.target.checked
                            })}
                            className="sr-only peer" 
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Time Settings */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4 flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      Time Management
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-cool-700 mb-2">Cancellation Window (hours)</label>
                        <input
                          type="number"
                          value={businessSettings.cancellationWindow}
                          onChange={(e) => setBusinessSettings({
                            ...businessSettings,
                            cancellationWindow: parseInt(e.target.value)
                          })}
                          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        />
                        <p className="text-xs text-cool-500 mt-1">Minimum hours before appointment for cancellation</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-cool-700 mb-2">Buffer Time (minutes)</label>
                        <input
                          type="number"
                          value={businessSettings.bufferTime}
                          onChange={(e) => setBusinessSettings({
                            ...businessSettings,
                            bufferTime: parseInt(e.target.value)
                          })}
                          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        />
                        <p className="text-xs text-cool-500 mt-1">Time between appointments for preparation</p>
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-cool-700 mb-2">Maximum Advance Booking (days)</label>
                        <input
                          type="number"
                          value={businessSettings.maxAdvanceBooking}
                          onChange={(e) => setBusinessSettings({
                            ...businessSettings,
                            maxAdvanceBooking: parseInt(e.target.value)
                          })}
                          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        />
                        <p className="text-xs text-cool-500 mt-1">How far in advance customers can book appointments</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'security' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-bold text-cool-800 mb-2">Security Settings</h2>
                    <p className="text-cool-600">Manage your account security and access controls</p>
                  </div>

                  {/* Password Settings */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4 flex items-center gap-2">
                      <Lock className="w-5 h-5" />
                      Password & Authentication
                    </h3>
                    <div className="space-y-4">
                      <button className="w-full flex items-center justify-between p-4 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                        <div className="flex items-center gap-3">
                          <Lock className="w-5 h-5 text-cool-600" />
                          <div className="text-left">
                            <p className="font-medium text-cool-800">Change Password</p>
                            <p className="text-sm text-cool-600">Update your account password</p>
                          </div>
                        </div>
                        <ChevronRight className="w-4 h-4 text-cool-500" />
                      </button>
                      
                      <button className="w-full flex items-center justify-between p-4 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                        <div className="flex items-center gap-3">
                          <Shield className="w-5 h-5 text-cool-600" />
                          <div className="text-left">
                            <p className="font-medium text-cool-800">Two-Factor Authentication</p>
                            <p className="text-sm text-cool-600">Add an extra layer of security</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Enabled</span>
                          <ChevronRight className="w-4 h-4 text-cool-500" />
                        </div>
                      </button>
                      
                      <button className="w-full flex items-center justify-between p-4 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                        <div className="flex items-center gap-3">
                          <Key className="w-5 h-5 text-cool-600" />
                          <div className="text-left">
                            <p className="font-medium text-cool-800">API Keys</p>
                            <p className="text-sm text-cool-600">Manage integration access keys</p>
                          </div>
                        </div>
                        <ChevronRight className="w-4 h-4 text-cool-500" />
                      </button>
                    </div>
                  </div>

                  {/* Login Activity */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4">Recent Login Activity</h3>
                    <div className="space-y-3">
                      {[
                        { device: 'MacBook Pro', location: 'San Francisco, CA', time: '2 hours ago', current: true },
                        { device: 'iPhone 14', location: 'San Francisco, CA', time: '1 day ago', current: false },
                        { device: 'Chrome Browser', location: 'San Francisco, CA', time: '3 days ago', current: false }
                      ].map((session, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary-100 rounded-lg">
                              <Smartphone className="w-4 h-4 text-primary-600" />
                            </div>
                            <div>
                              <p className="font-medium text-cool-800">{session.device}</p>
                              <p className="text-sm text-cool-600">{session.location} • {session.time}</p>
                            </div>
                          </div>
                          {session.current ? (
                            <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Current</span>
                          ) : (
                            <button className="text-red-600 hover:bg-red-100 px-3 py-1 rounded-lg text-sm transition-colors duration-200">
                              Revoke
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'billing' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-bold text-cool-800 mb-2">Billing & Subscription</h2>
                    <p className="text-cool-600">Manage your subscription and payment methods</p>
                  </div>

                  {/* Current Plan */}
                  <div className="p-6 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-bold mb-2">Pro Tier</h3>
                        <p className="opacity-90">Advanced features for growing businesses</p>
                        <p className="text-sm opacity-75 mt-2">Next billing: February 20, 2024</p>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold">$20</p>
                        <p className="opacity-90">per month</p>
                      </div>
                    </div>
                  </div>

                  {/* Payment Methods */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4">Payment Methods</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-primary-100 rounded-lg">
                            <CreditCard className="w-5 h-5 text-primary-600" />
                          </div>
                          <div>
                            <p className="font-medium text-cool-800">•••• •••• •••• 4242</p>
                            <p className="text-sm text-cool-600">Expires 12/25</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">Primary</span>
                          <button className="text-cool-600 hover:text-cool-800">Edit</button>
                        </div>
                      </div>
                    </div>
                    <button className="mt-4 btn-secondary">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Payment Method
                    </button>
                  </div>

                  {/* Billing History */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4">Billing History</h3>
                    <div className="space-y-3">
                      {[
                        { date: 'Jan 20, 2024', amount: '$20.00', status: 'Paid', invoice: 'INV-001' },
                        { date: 'Dec 20, 2023', amount: '$20.00', status: 'Paid', invoice: 'INV-002' },
                        { date: 'Nov 20, 2023', amount: '$20.00', status: 'Paid', invoice: 'INV-003' }
                      ].map((bill, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                          <div>
                            <p className="font-medium text-cool-800">{bill.invoice}</p>
                            <p className="text-sm text-cool-600">{bill.date}</p>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className="font-bold text-cool-800">{bill.amount}</span>
                            <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">{bill.status}</span>
                            <button className="text-primary-600 hover:underline text-sm">Download</button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'data' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-bold text-cool-800 mb-2">Data & Privacy</h2>
                    <p className="text-cool-600">Control your data and privacy settings</p>
                  </div>

                  {/* Data Export */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4">Data Management</h3>
                    <div className="space-y-4">
                      <button className="w-full flex items-center justify-between p-4 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                        <div className="flex items-center gap-3">
                          <Download className="w-5 h-5 text-cool-600" />
                          <div className="text-left">
                            <p className="font-medium text-cool-800">Export Data</p>
                            <p className="text-sm text-cool-600">Download all your business data</p>
                          </div>
                        </div>
                        <ChevronRight className="w-4 h-4 text-cool-500" />
                      </button>
                      
                      <button className="w-full flex items-center justify-between p-4 bg-red-50 hover:bg-red-100 rounded-lg transition-colors duration-200">
                        <div className="flex items-center gap-3">
                          <Trash2 className="w-5 h-5 text-red-600" />
                          <div className="text-left">
                            <p className="font-medium text-red-800">Delete Account</p>
                            <p className="text-sm text-red-600">Permanently delete your account and all data</p>
                          </div>
                        </div>
                        <ChevronRight className="w-4 h-4 text-red-500" />
                      </button>
                    </div>
                  </div>

                  {/* Privacy Settings */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4">Privacy Controls</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                        <div>
                          <p className="font-medium text-cool-800">Profile Visibility</p>
                          <p className="text-sm text-cool-600">Make your profile visible to customers</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" defaultChecked className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                        <div>
                          <p className="font-medium text-cool-800">Analytics Tracking</p>
                          <p className="text-sm text-cool-600">Allow anonymous usage analytics</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" defaultChecked className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'support' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-bold text-cool-800 mb-2">Support & Help</h2>
                    <p className="text-cool-600">Get help and support for your account</p>
                  </div>

                  {/* Support Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <button className="p-6 bg-white/50 hover:bg-white/70 rounded-xl transition-colors duration-200 text-left">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 bg-primary-100 rounded-lg">
                          <HelpCircle className="w-5 h-5 text-primary-600" />
                        </div>
                        <h3 className="font-bold text-cool-800">Help Center</h3>
                      </div>
                      <p className="text-cool-600 text-sm">Browse frequently asked questions and guides</p>
                    </button>

                    <button className="p-6 bg-white/50 hover:bg-white/70 rounded-xl transition-colors duration-200 text-left">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 bg-secondary-100 rounded-lg">
                          <MessageCircle className="w-5 h-5 text-secondary-600" />
                        </div>
                        <h3 className="font-bold text-cool-800">Contact Support</h3>
                      </div>
                      <p className="text-cool-600 text-sm">Get help from our support team</p>
                    </button>

                    <button className="p-6 bg-white/50 hover:bg-white/70 rounded-xl transition-colors duration-200 text-left">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 bg-accent-100 rounded-lg">
                          <Globe className="w-5 h-5 text-accent-600" />
                        </div>
                        <h3 className="font-bold text-cool-800">Community Forum</h3>
                      </div>
                      <p className="text-cool-600 text-sm">Connect with other service providers</p>
                    </button>

                    <button className="p-6 bg-white/50 hover:bg-white/70 rounded-xl transition-colors duration-200 text-left">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 bg-warm-100 rounded-lg">
                          <ExternalLink className="w-5 h-5 text-warm-600" />
                        </div>
                        <h3 className="font-bold text-cool-800">Video Tutorials</h3>
                      </div>
                      <p className="text-cool-600 text-sm">Watch step-by-step video guides</p>
                    </button>
                  </div>

                  {/* System Status */}
                  <div>
                    <h3 className="text-lg font-bold text-cool-800 mb-4">System Status</h3>
                    <div className="space-y-3">
                      {[
                        { service: 'Booking System', status: 'Operational', color: 'green' },
                        { service: 'Payment Processing', status: 'Operational', color: 'green' },
                        { service: 'Email Notifications', status: 'Operational', color: 'green' },
                        { service: 'Mobile App', status: 'Operational', color: 'green' }
                      ].map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
                          <span className="font-medium text-cool-800">{item.service}</span>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full bg-${item.color}-500`}></div>
                            <span className={`text-sm text-${item.color}-600`}>{item.status}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
