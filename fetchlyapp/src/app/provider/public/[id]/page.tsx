'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Provider, Service } from '@/lib/firebase/providers';
import { getProviderByUserId, getProviderServices } from '@/lib/firebase/providers';
import { collection, query, where, getDocs, doc, getDoc, addDoc, deleteDoc, updateDoc, increment } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  MapPin, Phone, Mail, Globe, Award, Star, Clock, CheckCircle,
  Heart, Share2, MessageCircle, Calendar, PawPrint, ArrowLeft,
  Verified, Shield, Users, TrendingUp
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function PublicProviderProfile({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth();
  const router = useRouter();
  const resolvedParams = use(params);
  const providerId = resolvedParams.id;
  const [provider, setProvider] = useState<Provider | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviews, setReviews] = useState<any[]>([]);
  const [reviewRating, setReviewRating] = useState(0);
  const [reviewTitle, setReviewTitle] = useState('');
  const [reviewComment, setReviewComment] = useState('');

  useEffect(() => {
    const loadProviderData = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading public provider profile for userId:', providerId);

        // Load provider data
        let providerData;
        try {
          providerData = await getProviderByUserId(providerId);
        } catch (error) {
          console.error('Error fetching provider data:', error);
          throw new Error('Failed to load provider data. Please try again later.');
        }

        if (!providerData) {
          toast.error('Provider not found');
          router.push('/community');
          return;
        }

        setProvider(providerData);
        console.log('✅ Provider loaded:', {
          businessName: providerData.businessName,
          profilePhoto: providerData.profilePhoto,
          bannerPhoto: providerData.bannerPhoto,
          hasProfilePhoto: !!providerData.profilePhoto,
          hasBannerPhoto: !!providerData.bannerPhoto,
          fullProviderData: providerData
        });

        // Load services
        const servicesData = await getProviderServices(providerData.id!);
        setServices(servicesData);

        // Load public posts
        const postsQuery = query(
          collection(db, 'posts'),
          where('userId', '==', providerId),
          where('isPublic', '==', true)
        );
        const postsSnapshot = await getDocs(postsQuery);
        const postsData = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date()
        }));
        setPosts(postsData);

        // Load real follower count from Firestore
        await loadFollowerCount();

        // Load reviews
        await loadReviews();

        // Check if current user is following this provider
        if (user) {
          await checkIfFollowing();
        }

      } catch (error) {
        console.error('❌ Error loading provider:', error);
        toast.error('Failed to load provider profile');
      } finally {
        setLoading(false);
      }
    };

    loadProviderData();
  }, [providerId, router]);

  // Load follower count from Firestore
  const loadFollowerCount = async () => {
    try {
      console.log('🔍 Loading follower count for provider:', providerId);
      const followersQuery = query(
        collection(db, 'followers'),
        where('providerId', '==', providerId)
      );
      const followersSnapshot = await getDocs(followersQuery);
      const count = followersSnapshot.size;
      console.log('📊 Follower count loaded:', count);
      setFollowerCount(count);
    } catch (error) {
      console.error('Error loading follower count:', error);
      setFollowerCount(0);
    }
  };

  // Check if current user is following this provider
  const checkIfFollowing = async () => {
    if (!user) return;

    try {
      const followQuery = query(
        collection(db, 'followers'),
        where('providerId', '==', providerId),
        where('userId', '==', user.id)
      );
      const followSnapshot = await getDocs(followQuery);
      setIsFollowing(!followSnapshot.empty);
    } catch (error) {
      console.error('Error checking follow status:', error);
    }
  };

  // Load reviews from Firestore
  const loadReviews = async () => {
    try {
      const reviewsQuery = query(
        collection(db, 'reviews'),
        where('providerId', '==', providerId)
      );
      const reviewsSnapshot = await getDocs(reviewsQuery);
      const reviewsData = reviewsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));
      setReviews(reviewsData);
    } catch (error) {
      console.error('Error loading reviews:', error);
    }
  };

  const handleBookService = (service: Service) => {
    if (!user) {
      // Store the current URL to redirect back after sign in
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to book services');
      return;
    }
    
    // TODO: Implement booking modal
    toast.success(`Booking for "${service.name}" - Coming soon!`);
  };



  const handleFollow = async () => {
    if (!user) {
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to follow providers');
      return;
    }

    try {
      console.log('🔄 Follow action:', { isFollowing, providerId, userId: user.id });

      if (isFollowing) {
        // Unfollow: Remove from followers collection
        console.log('👋 Unfollowing provider...');
        const followQuery = query(
          collection(db, 'followers'),
          where('providerId', '==', providerId),
          where('userId', '==', user.id)
        );
        const followSnapshot = await getDocs(followQuery);

        if (!followSnapshot.empty) {
          await deleteDoc(followSnapshot.docs[0].ref);
          setIsFollowing(false);
          setFollowerCount(prev => {
            const newCount = prev - 1;
            console.log('📉 Follower count decreased:', prev, '->', newCount);
            return newCount;
          });
          toast.success('Unfollowed provider');
        }
      } else {
        // Follow: Add to followers collection
        console.log('👍 Following provider...');
        await addDoc(collection(db, 'followers'), {
          providerId: providerId,
          userId: user.id,
          userName: user.name,
          userEmail: user.email,
          followedAt: new Date()
        });

        setIsFollowing(true);
        setFollowerCount(prev => {
          const newCount = prev + 1;
          console.log('📈 Follower count increased:', prev, '->', newCount);
          return newCount;
        });
        toast.success('Following provider!');
      }
    } catch (error) {
      console.error('Error following/unfollowing provider:', error);
      toast.error('Failed to update follow status');
    }
  };

  const handleBookNow = () => {
    if (!user) {
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to book services');
      return;
    }
    setShowBookingModal(true);
  };

  const handleViewAvailability = () => {
    setShowAvailabilityModal(true);
  };

  const handleLeaveReview = () => {
    if (!user) {
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to leave a review');
      return;
    }
    // Reset review form
    setReviewRating(0);
    setReviewTitle('');
    setReviewComment('');
    setShowReviewModal(true);
  };

  const handleStarClick = (rating: number) => {
    setReviewRating(rating);
  };

  const handleStarHover = (rating: number) => {
    // For visual feedback during hover
  };

  const submitReview = async () => {
    if (!user || !reviewRating) {
      toast.error('Please select a rating');
      return;
    }

    try {
      await addDoc(collection(db, 'reviews'), {
        providerId: providerId,
        userId: user.id,
        userName: user.name,
        userEmail: user.email,
        rating: reviewRating,
        title: reviewTitle,
        comment: reviewComment,
        createdAt: new Date()
      });

      toast.success('Review submitted! Thank you for your feedback.');
      setShowReviewModal(false);
      loadReviews(); // Reload reviews
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading provider profile...</p>
        </div>
      </div>
    );
  }

  if (!provider) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Provider Not Found</h2>
          <p className="text-gray-600 mb-4">This provider profile doesn't exist or isn't available.</p>
          <button
            onClick={() => router.push('/community')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Community
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating Header */}
      <div className="relative z-20 bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 py-2 sm:py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="group flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-sm rounded-xl sm:rounded-2xl px-3 sm:px-6 py-2 sm:py-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white border border-white/20"
            >
              <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 group-hover:text-green-600 transition-colors duration-300" />
              <span className="text-sm sm:text-base text-gray-700 font-medium group-hover:text-green-700 transition-colors duration-300">Back</span>
            </button>

            <div className="flex items-center gap-2 sm:gap-4">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`group relative p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl backdrop-blur-xl transition-all duration-300 hover:scale-110 ${
                  isFavorite
                    ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg shadow-red-500/25'
                    : 'bg-white/90 text-gray-600 hover:bg-white shadow-lg border border-white/20'
                }`}
              >
                <Heart className={`w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 transition-all duration-300 ${isFavorite ? 'fill-current scale-110' : 'group-hover:scale-110'}`} />
              </button>

              <button
                onClick={handleFollow}
                className={`group relative px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 rounded-xl sm:rounded-2xl backdrop-blur-xl transition-all duration-300 hover:scale-105 font-medium text-sm sm:text-base ${
                  isFollowing
                    ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-lg shadow-green-500/25'
                    : 'bg-white/90 text-gray-700 hover:bg-gradient-to-r hover:from-green-500 hover:to-blue-500 hover:text-white shadow-lg border border-white/20'
                }`}
              >
                <Users className="w-4 h-4 sm:w-5 sm:h-5 inline mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{isFollowing ? 'Following' : 'Follow'}</span>
                <span className="sm:hidden">{isFollowing ? 'Following' : 'Follow'}</span>
                <span className="ml-1 sm:ml-2 text-xs sm:text-sm opacity-75">({followerCount})</span>
              </button>
              <button className="group p-4 rounded-2xl bg-white/90 backdrop-blur-xl text-gray-600 hover:bg-white shadow-lg border border-white/20 transition-all duration-300 hover:scale-110">
                <Share2 className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Banner Section */}
      <div className="relative z-10 -mt-2 sm:-mt-4">
        <div className="max-w-7xl mx-auto px-3 sm:px-6">
          {/* Stunning Banner Card */}
          <div className="relative overflow-hidden rounded-2xl sm:rounded-3xl shadow-2xl bg-gradient-to-br from-green-600 via-blue-600 to-purple-600">
            {/* Banner Image with Overlay */}
            <div className="relative h-48 sm:h-64 md:h-80 lg:h-[32rem] overflow-hidden">
              {provider.bannerPhoto && provider.bannerPhoto !== '/fetchlylogo.png' ? (
                <img
                  src={provider.bannerPhoto}
                  alt="Provider Banner"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    console.error('Banner image failed to load:', provider.bannerPhoto);
                    e.currentTarget.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-green-600 via-blue-600 to-purple-600 relative">
                  {/* Animated Pattern Overlay */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-10 left-10 w-20 h-20 bg-white/30 rounded-full animate-bounce delay-100"></div>
                    <div className="absolute top-20 right-20 w-16 h-16 bg-white/20 rounded-full animate-bounce delay-300"></div>
                    <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/25 rounded-full animate-bounce delay-500"></div>
                    <div className="absolute bottom-10 right-10 w-12 h-12 bg-white/30 rounded-full animate-bounce delay-700"></div>
                  </div>
                </div>
              )}



              {/* Floating Elements */}
              <div className="absolute top-8 right-8 bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2">
                <div className="flex items-center space-x-2 text-white">
                  <Verified className="w-5 h-5 text-green-400" />
                  <span className="font-medium">Verified Provider</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

        {/* Floating Profile Card */}
        <div className="max-w-7xl mx-auto px-3 sm:px-6 -mt-12 sm:-mt-16 md:-mt-20 lg:-mt-24 relative z-20">
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 p-4 sm:p-6 md:p-8 mb-6 sm:mb-8">
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4 sm:gap-6 md:gap-8">
              {/* Stunning Profile Photo */}
              <div className="relative group">
                {provider.profilePhoto ? (
                  <div className="relative">
                    <img
                      src={provider.profilePhoto}
                      alt={provider.businessName || provider.ownerName}
                      className="w-24 h-24 sm:w-32 sm:h-32 md:w-36 md:h-36 lg:w-40 lg:h-40 rounded-2xl sm:rounded-3xl object-cover border-2 sm:border-4 border-white shadow-2xl group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 rounded-2xl sm:rounded-3xl bg-gradient-to-br from-green-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                ) : (
                  <div className="w-24 h-24 sm:w-32 sm:h-32 md:w-36 md:h-36 lg:w-40 lg:h-40 rounded-2xl sm:rounded-3xl bg-gradient-to-br from-green-500 via-blue-500 to-purple-500 flex items-center justify-center text-white text-2xl sm:text-3xl md:text-4xl font-bold border-2 sm:border-4 border-white shadow-2xl group-hover:scale-105 transition-transform duration-300">
                    {provider.businessName?.charAt(0) || provider.ownerName?.charAt(0) || 'P'}
                  </div>
                )}
                {/* Animated Online Status */}
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full animate-pulse shadow-lg">
                  <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-75"></div>
                </div>
                {/* Verification Badge */}
                {provider.verified && (
                  <div className="absolute -top-2 -left-2 bg-blue-500 text-white p-2 rounded-full shadow-lg">
                    <Verified className="w-4 h-4" />
                  </div>
                )}
              </div>

              {/* Provider Info */}
              <div className="flex-1 text-center lg:text-left">
                <div className="flex flex-col lg:flex-row lg:items-center gap-4 mb-4">
                  <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                    {provider.businessName || provider.ownerName}
                  </h1>
                  <div className="flex items-center justify-center lg:justify-start gap-3">
                    {provider.verified && (
                      <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                        <Verified className="w-4 h-4" />
                        Verified
                      </div>
                    )}
                    {provider.featured && (
                      <div className="bg-yellow-100 text-yellow-600 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                        <Award className="w-4 h-4" />
                        Featured
                      </div>
                    )}
                  </div>
                </div>

                <p className="text-2xl text-gray-600 mb-6 font-light">{provider.serviceType}</p>

                {/* Stats Row */}
                <div className="flex flex-wrap items-center justify-center lg:justify-start gap-6 mb-6">
                  <div className="flex items-center gap-2 bg-yellow-50 px-4 py-2 rounded-2xl">
                    <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    <span className="font-bold text-lg">{provider.rating || 5.0}</span>
                    <span className="text-gray-500">({provider.reviewCount || 0} reviews)</span>
                  </div>
                  <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-2xl">
                    <MapPin className="w-5 h-5 text-gray-500" />
                    <span className="font-medium">{provider.city}, {provider.state}</span>
                  </div>
                  <div className="flex items-center gap-2 bg-green-50 px-4 py-2 rounded-2xl">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-green-600">Available Now</span>
                  </div>
                </div>

                {/* Description */}
                {provider.description && (
                  <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 mb-6">
                    <p className="text-gray-700 text-lg leading-relaxed">{provider.description}</p>
                  </div>
                )}

                {/* Stunning Action Buttons */}
                <div className="flex flex-wrap gap-4">
                  <button
                    onClick={handleBookNow}
                    className="group bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center gap-3"
                  >
                    <Calendar className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                    Book Now
                    <div className="absolute inset-0 rounded-2xl bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </button>

                  <button
                    onClick={handleViewAvailability}
                    className="group bg-white/90 backdrop-blur-sm border-2 border-gray-200 hover:border-green-300 text-gray-700 hover:text-green-600 px-8 py-4 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center gap-3"
                  >
                    <Clock className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                    View Availability
                  </button>

                  {user && (
                    <button
                      onClick={handleLeaveReview}
                      className="group bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center gap-3"
                    >
                      <Star className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                      Leave Review
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Stunning Services Section */}
            <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center">
                  <PawPrint className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Services Offered</h2>
              </div>

              <div className="grid gap-6">
                {services.map((service, index) => (
                  <div key={service.id} className="group relative bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 border border-gray-100 hover:border-green-200 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                    {/* Service Number Badge */}
                    <div className="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                      {index + 1}
                    </div>

                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">{service.name}</h3>
                          <div className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium">
                            Popular
                          </div>
                        </div>
                        <p className="text-gray-600 mb-4 leading-relaxed">{service.description}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{service.duration || '1 hour'}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Available Today</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-4">
                        <div className="text-right">
                          <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                            ${service.price}
                          </div>
                          <div className="text-sm text-gray-500">per session</div>
                        </div>
                        <button
                          onClick={() => handleBookService(service)}
                          className="group bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-6 py-3 rounded-2xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center gap-2"
                        >
                          <Calendar className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                          Book Now
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Posts */}
            {posts.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Updates</h2>
                <div className="space-y-4">
                  {posts.slice(0, 3).map((post) => (
                    <div key={post.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                      <p className="text-gray-800 mb-2">{post.content}</p>
                      <span className="text-sm text-gray-500">
                        {post.timestamp.toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Reviews */}
            {reviews.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Customer Reviews</h2>
                <div className="space-y-4">
                  {reviews.slice(0, 3).map((review) => (
                    <div key={review.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < (review.rating || 5) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="font-medium text-gray-900">{review.userName || 'Anonymous'}</span>
                      </div>
                      {review.title && (
                        <h4 className="font-medium text-gray-800 mb-1">{review.title}</h4>
                      )}
                      <p className="text-gray-600 mb-2">{review.comment}</p>
                      <span className="text-sm text-gray-500">
                        {review.createdAt.toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Info */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="font-bold text-gray-900 mb-4">Contact Information</h3>
              <div className="space-y-3">
                {provider.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">{provider.phone}</span>
                  </div>
                )}
                {provider.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">{provider.email}</span>
                  </div>
                )}
                {provider.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="w-5 h-5 text-gray-400" />
                    <a href={provider.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      Visit Website
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Stats */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="font-bold text-gray-900 mb-4">Provider Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Bookings</span>
                  <span className="font-semibold">{provider.totalBookings || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Response Rate</span>
                  <span className="font-semibold">{provider.responseRate || 100}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Response Time</span>
                  <span className="font-semibold">{provider.responseTime || '< 1 hour'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Book Service</h3>
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Service
                  </label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Choose a service...</option>
                    {services.map((service) => (
                      <option key={service.id} value={service.id}>
                        {service.name} - ${service.price}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Date
                  </label>
                  <input
                    type="date"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Time
                  </label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select time...</option>
                    <option value="09:00">9:00 AM</option>
                    <option value="10:00">10:00 AM</option>
                    <option value="11:00">11:00 AM</option>
                    <option value="14:00">2:00 PM</option>
                    <option value="15:00">3:00 PM</option>
                    <option value="16:00">4:00 PM</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests (Optional)
                  </label>
                  <textarea
                    rows={3}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any special requests or notes..."
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => setShowBookingModal(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      // TODO: Submit booking request
                      toast.success('Booking request sent! Provider will be notified.');
                      setShowBookingModal(false);
                    }}
                    className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Send Request
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Availability Modal */}
      {showAvailabilityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Available Hours</h3>
                <button
                  onClick={() => setShowAvailabilityModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                {provider?.businessHours && Object.entries(provider.businessHours).map(([day, hours]) => (
                  <div key={day} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium text-gray-900 capitalize">
                      {day}
                    </div>
                    <div className="text-gray-600">
                      {hours.closed ? (
                        <span className="text-red-500">Closed</span>
                      ) : (
                        <span>{hours.open} - {hours.close}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <button
                  onClick={() => setShowAvailabilityModal(false)}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Review Modal */}
      {showReviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Leave a Review</h3>
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rating *
                  </label>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => handleStarClick(star)}
                        onMouseEnter={() => handleStarHover(star)}
                        className="text-3xl transition-colors duration-150 hover:scale-110 transform"
                      >
                        <Star
                          className={`w-8 h-8 ${
                            star <= reviewRating
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300 hover:text-yellow-300'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {reviewRating === 0 && 'Click to rate'}
                    {reviewRating === 1 && '⭐ Poor'}
                    {reviewRating === 2 && '⭐⭐ Fair'}
                    {reviewRating === 3 && '⭐⭐⭐ Good'}
                    {reviewRating === 4 && '⭐⭐⭐⭐ Very Good'}
                    {reviewRating === 5 && '⭐⭐⭐⭐⭐ Excellent'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Review Title
                  </label>
                  <input
                    type="text"
                    value={reviewTitle}
                    onChange={(e) => setReviewTitle(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Great service!"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Review *
                  </label>
                  <textarea
                    rows={4}
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Tell others about your experience..."
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => setShowReviewModal(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={submitReview}
                    disabled={!reviewRating || !reviewComment.trim()}
                    className="flex-1 px-4 py-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Submit Review
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
