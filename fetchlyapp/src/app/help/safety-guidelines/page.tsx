'use client';

import { Shield, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function HelpSafetyGuidelinesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-20">
      <div className="container mx-auto px-4 py-16">
        {/* Back Button */}
        <div className="max-w-4xl mx-auto mb-8">
          <Link href="/help" className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors duration-300">
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Help Center</span>
          </Link>
        </div>

        {/* Header */}
        <div className="max-w-4xl mx-auto text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full text-white">
              <Shield className="w-8 h-8" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Safety Guidelines
          </h1>
          <p className="text-xl text-gray-600">
            Essential safety information for using Fetchly services
          </p>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Safety Overview</h2>
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 mb-4">
                At Fetchly, your safety and your pet's wellbeing are our highest priorities. We've implemented comprehensive safety measures and guidelines to ensure every interaction on our platform is secure and positive.
              </p>
              <p className="text-gray-700 mb-6">
                For detailed safety guidelines, emergency contacts, and comprehensive safety information, please visit our dedicated Safety Guidelines page.
              </p>
            </div>
            
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Key Safety Features:</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Comprehensive provider background checks and verification</li>
                <li>• 24/7 customer support and emergency assistance</li>
                <li>• Secure payment processing and transaction protection</li>
                <li>• Insurance coverage for qualifying services</li>
                <li>• Real-time service tracking and communication</li>
              </ul>
            </div>

            <div className="text-center">
              <Link 
                href="/safety" 
                className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-medium"
              >
                <Shield className="w-5 h-5" />
                <span>View Complete Safety Guidelines</span>
              </Link>
            </div>
          </div>

          {/* Emergency Contact Card */}
          <div className="bg-red-50 border border-red-200 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-red-900 mb-4">Emergency Contact</h2>
            <p className="text-red-800 mb-4">
              If you're experiencing an emergency or urgent safety concern, contact us immediately:
            </p>
            <div className="space-y-2">
              <p className="text-red-900 font-semibold">
                Emergency Hotline: <a href="tel:+17875550911" className="underline">+****************</a>
              </p>
              <p className="text-red-900 font-semibold">
                Email: <a href="mailto:<EMAIL>" className="underline"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
