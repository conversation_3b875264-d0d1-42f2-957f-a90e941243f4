'use client';

import { Users, Star, DollarSign, Calendar, Shield, TrendingUp, CheckCircle, Award, Smartphone } from 'lucide-react';
import Link from 'next/link';

export default function ProviderInfoPage() {
  const benefits = [
    {
      icon: DollarSign,
      title: "Increase Your Income",
      description: "Earn up to 40% more with our premium booking platform",
      details: ["Competitive commission rates", "Weekly payouts", "Transparent pricing"]
    },
    {
      icon: Calendar,
      title: "Flexible Scheduling",
      description: "Set your own hours and manage bookings easily",
      details: ["Control your availability", "Easy calendar management", "Instant booking notifications"]
    },
    {
      icon: Users,
      title: "Grow Your Client Base",
      description: "Connect with thousands of pet owners in Puerto Rico",
      details: ["Verified pet owners", "Repeat customer system", "Referral bonuses"]
    },
    {
      icon: Shield,
      title: "Insurance & Protection",
      description: "Comprehensive coverage for peace of mind",
      details: ["Liability insurance included", "Secure payment processing", "Dispute resolution support"]
    }
  ];

  const requirements = [
    {
      title: "Professional Experience",
      items: [
        "Minimum 1 year of pet care experience",
        "Valid certifications or licenses (if applicable)",
        "Professional references from previous clients",
        "Clean background check"
      ]
    },
    {
      title: "Documentation",
      items: [
        "Government-issued photo ID",
        "Proof of insurance (liability coverage)",
        "Business license (if applicable)",
        "Vaccination records (for pet boarding/sitting)"
      ]
    },
    {
      title: "Equipment & Setup",
      items: [
        "Reliable smartphone with internet access",
        "Professional-grade equipment for your services",
        "Safe, clean workspace (for in-home services)",
        "Transportation (for mobile services)"
      ]
    }
  ];

  const tiers = [
    {
      name: "Starter (Free)",
      price: "Free",
      commission: "5% commission",
      features: [
        "Basic profile listing",
        "Standard booking system",
        "Customer messaging",
        "Payment processing",
        "Basic analytics",
        "Email support"
      ],
      popular: false
    },
    {
      name: "Pro",
      price: "$20/month",
      commission: "3% commission (optional)",
      features: [
        "Everything in Starter",
        "AI Assistant (FetchBot)",
        "Calendar sync integration",
        "Custom branding options",
        "Bulk messaging tools",
        "Advanced analytics",
        "Priority support",
        "Featured listing placement"
      ],
      popular: true
    }
  ];

  const steps = [
    {
      step: 1,
      title: "Apply Online",
      description: "Complete our comprehensive application form with your experience and credentials."
    },
    {
      step: 2,
      title: "Verification Process",
      description: "We'll verify your background, references, and documentation (typically 3-5 business days)."
    },
    {
      step: 3,
      title: "Profile Setup",
      description: "Create your professional profile with photos, services, and pricing."
    },
    {
      step: 4,
      title: "Start Earning",
      description: "Go live on the platform and start receiving booking requests from pet owners."
    }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-white/20 rounded-full">
                <Users className="w-12 h-12" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Provider Information
            </h1>
            <p className="text-xl text-purple-100 max-w-2xl mx-auto">
              Everything you need to know about becoming a service provider on Fetchly
            </p>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Why Join Fetchly as a Provider?
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white">
                      <benefit.icon className="w-6 h-6" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{benefit.title}</h3>
                  </div>
                  <p className="text-gray-600 mb-4">{benefit.description}</p>
                  <ul className="space-y-2">
                    {benefit.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-700">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Tiers */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Choose Your Plan
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {tiers.map((tier, index) => (
                <div key={index} className={`bg-white rounded-2xl shadow-lg p-8 relative ${tier.popular ? 'ring-2 ring-purple-500' : ''}`}>
                  {tier.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                        Most Popular
                      </span>
                    </div>
                  )}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                    <div className="text-4xl font-bold text-purple-600 mb-2">{tier.price}</div>
                    <div className="text-gray-600">{tier.commission}</div>
                  </div>
                  <ul className="space-y-3 mb-8">
                    {tier.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link 
                    href="/provider/register" 
                    className={`w-full py-3 px-6 rounded-lg font-semibold text-center block transition-colors ${
                      tier.popular 
                        ? 'bg-purple-600 text-white hover:bg-purple-700' 
                        : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                    }`}
                  >
                    Get Started
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Requirements */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Provider Requirements
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {requirements.map((req, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{req.title}</h3>
                  <ul className="space-y-3">
                    {req.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Application Process */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              How to Get Started
            </h2>
            
            <div className="space-y-8">
              {steps.map((step, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8 flex items-center space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl flex-shrink-0">
                    {step.step}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{step.title}</h3>
                    <p className="text-gray-700">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
              <h2 className="text-3xl font-bold mb-4">Ready to Start Your Provider Journey?</h2>
              <p className="text-xl text-purple-100 mb-8">
                Join hundreds of trusted providers serving pet families across Puerto Rico
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/provider/register" className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  Apply Now
                </Link>
                <Link href="/help" className="bg-white/20 text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/30 transition-colors">
                  Back to Help Center
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
