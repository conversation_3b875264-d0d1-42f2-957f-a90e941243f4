'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams as useNextSearchParams, useRouter } from 'next/navigation';
import { Search, MapPin, Star, Phone, Calendar, Clock, MessageCircle, SlidersHorizontal } from 'lucide-react';
import Image from 'next/image';
import { searchProviders, getActiveProviders, getEmergencyProviders, type SearchFilters, type Provider as ProviderType } from '@/lib/firebase/providers';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

type SortOption = 'Recommended' | 'Highest Rated' | 'Lowest Price' | 'Most Reviews';

interface SearchPageParams extends SearchFilters {
  searchQuery?: string;
  emergency?: boolean;
}

type SearchParams = SearchPageParams;

// Sample data for dropdowns
const SERVICE_TYPES = ['All Services', 'Dog Walking', 'Pet Sitting', 'Grooming', 'Boarding', 'Training'] as const;
type ServiceType = typeof SERVICE_TYPES[number];

const PET_TYPES = ['All Pets', 'Dog', '<PERSON>', '<PERSON>', 'Rabbit', 'Other'] as const;
type PetType = typeof PET_TYPES[number];

const LOCATIONS = ['Anywhere', 'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'] as const;
type Location = typeof LOCATIONS[number];

const SORT_OPTIONS: SortOption[] = ['Recommended', 'Highest Rated', 'Most Reviews', 'Lowest Price'];

interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  rating: number;
  reviewCount: number;
  city: string;
  state: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  specialties: string[];
  verified: boolean;
  featured: boolean;
  profilePhoto?: string;
  responseTime: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  activeServices?: any[];
  hasActiveServices?: boolean;
  distance?: number;
  businessHours?: Record<string, { open: string; close: string; closed?: boolean }>;
}

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function SearchPage() {
  const router = useRouter();
  const searchParams = useNextSearchParams();
  const { isAuthenticated } = useAuth();
  
  // State management
  const [providers, setProviders] = useState<ProviderType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  
  // Search filters state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState<ServiceType>('All Services');
  const [selectedLocation, setSelectedLocation] = useState<Location>('Anywhere');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedPetType, setSelectedPetType] = useState<PetType>('All Pets');
  const [sortBy, setSortBy] = useState<SortOption>('Recommended');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    minRating: 0,
    maxDistance: 20,
    priceRange: 'any' as const,
    availability: 'any' as const,
    verified: false
  });

  const emergencyKeywords = useMemo(() => ['emergency', 'vet', 'veterinary', 'hospital', 'clinic'], []);

  // Perform search with the given parameters
  const performSearch = useCallback(async (searchParams: SearchParams) => {
    try {
      setLoading(true);
      setError(null);
      
      // Extract search query from params if it exists
      const { searchQuery: query, ...filters } = searchParams;
      
      console.log('🔍 Searching providers with filters:', filters);
      const results = await searchProviders(filters);
      
      if (results.length === 0) {
        console.log('ℹ️ No providers found matching the criteria');
        setProviders([]);
        return;
      }
      
      console.log(`✅ Found ${results.length} providers`);
      
      // Process and filter results
      let processedResults = results.map(provider => ({
        ...provider,
        id: provider.id || '',
        businessName: provider.businessName || 'Unnamed Business',
        ownerName: provider.ownerName || '',
        serviceType: provider.serviceType || 'Service',
        rating: provider.rating || 0,
        reviewCount: provider.reviewCount || 0,
        city: provider.city || '',
        state: provider.state || '',
        address: provider.address || '',
        phone: provider.phone || '',
        email: provider.email || '',
        description: provider.description || '',
        specialties: provider.specialties || [],
        verified: provider.verified || false,
        featured: provider.featured || false,
        responseTime: provider.responseTime || 'Varies',
        activeServices: provider.activeServices || [],
        hasActiveServices: (provider.activeServices?.length || 0) > 0,
      }));
      
      // Apply search query filter if provided
      if (query) {
        const searchTerm = query.toLowerCase();
        processedResults = processedResults.filter(provider => 
          provider.businessName.toLowerCase().includes(searchTerm) ||
          provider.serviceType.toLowerCase().includes(searchTerm) ||
          provider.description.toLowerCase().includes(searchTerm) ||
          provider.specialties.some(s => s.toLowerCase().includes(searchTerm))
        );
      }
      
      // Apply sorting
      processedResults.sort((a, b) => {
        switch (sortBy) {
          case 'Highest Rated':
            return (b.rating || 0) - (a.rating || 0);
          case 'Most Reviews':
            return (b.reviewCount || 0) - (a.reviewCount || 0);
          // Add more sorting options as needed
          default:
            return 0; // Default sorting (Recommended)
        }
      });
      
      setProviders(processedResults);
      
    } catch (err) {
      console.error('Error searching providers:', err);
      setError('Failed to load providers. Please try again later.');
      toast.error('Failed to load providers');
      setProviders([]);
    } finally {
      setLoading(false);
    }
  }, [sortBy]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    const searchParams: SearchParams = {
      location: selectedLocation || undefined,
      service: selectedService === 'All Services' ? undefined : selectedService,
      date: selectedDate || undefined,
      petType: selectedPetType || undefined,
      searchQuery: searchQuery || undefined,
      ...filters
    };
    
    // Update URL with search parameters
    const params = new URLSearchParams();
    if (searchParams.location) params.set('location', searchParams.location);
    if (searchParams.service) params.set('service', searchParams.service);
    if (searchParams.date) params.set('date', searchParams.date);
    if (searchParams.petType) params.set('petType', searchParams.petType);
    if (searchParams.searchQuery) params.set('query', searchParams.searchQuery);
    
    router.push(`/search?${params.toString()}`);
    
    // Check for emergency searches
    const isEmergencySearch = searchQuery && emergencyKeywords.some(keyword =>
      searchQuery.toLowerCase().includes(keyword)
    );

    if (isEmergencySearch) {
      // Show emergency providers first
      const emergencyParams: SearchParams = { ...searchParams, emergency: true };
      performSearch(emergencyParams);
    } else {
      // Regular search
      performSearch(searchParams);
    }
  };

  const toggleFavorite = (id: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(id)) {
        newFavorites.delete(id);
      } else {
        newFavorites.add(id);
      }
      return newFavorites;
    });
  };

  // Handle booking button click
  const handleBookNow = (providerId: string) => {
    if (!isAuthenticated) {
      router.push('/auth/signup');
      return;
    }
    // Navigate to booking page with provider ID
    router.push(`/book/${providerId}`);
  };
      const isEmergencySearch = searchQuery && emergencyKeywords.some(keyword =>
        searchQuery.toLowerCase().includes(keyword)
      );

      let results;
      if (isEmergencySearch) {
        results = await getEmergencyProviders();
      } else {
        results = await searchProviders(filters);
      }

      setProviders(results.map((p: any) => ({ ...p, id: p.id ?? '' })));
      toast.success(`Found ${results.length} providers`);
    } catch (error) {
      console.error('Error searching providers:', error);
      toast.error('Search failed');
    } finally {
      setLoading(false);
    }
  };

  // Handle search with current form values
  const handleSearch = async () => {
    await performSearch();
  };

  // Filter providers based on search criteria
  const filteredProviders = providers.filter(provider => {
    // Search query filter
    const matchesSearch = !searchQuery ||
      provider.businessName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.serviceType?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.specialties?.some(specialty => specialty.toLowerCase().includes(searchQuery.toLowerCase()));

    // Service type filter - improved logic
    const matchesService = selectedService === 'All Services' ||
      provider.serviceType?.toLowerCase() === selectedService.toLowerCase() ||
      provider.serviceType?.toLowerCase().includes(selectedService.toLowerCase()) ||
      // Handle common variations
      (selectedService === 'Grooming' && provider.serviceType?.toLowerCase().includes('groom')) ||
      (selectedService === 'Pet Sitting' && provider.serviceType?.toLowerCase().includes('sitting')) ||
      (selectedService === 'Dog Walking' && provider.serviceType?.toLowerCase().includes('walking')) ||
      (selectedService === 'Veterinary' && (provider.serviceType?.toLowerCase().includes('vet') || provider.serviceType?.toLowerCase().includes('veterinary'))) ||
      (selectedService === 'Training' && provider.serviceType?.toLowerCase().includes('train'));

    // Location filter
    const matchesLocation = !selectedLocation ||
      provider.city?.toLowerCase().includes(selectedLocation.toLowerCase()) ||
      provider.state?.toLowerCase().includes(selectedLocation.toLowerCase()) ||
      provider.address?.toLowerCase().includes(selectedLocation.toLowerCase());

    // Pet type filter
    const matchesPetType = !selectedPetType ||
      provider.specialties?.some(specialty => specialty.toLowerCase().includes(selectedPetType.toLowerCase()));

    // Rating filter
    const matchesRating = !filters.minRating || (provider.rating && provider.rating >= filters.minRating);

    // Verified filter
    const matchesVerified = !filters.verified || provider.verified;

    console.log(`🔍 Provider ${provider.businessName}:`, {
      serviceType: provider.serviceType,
      selectedService,
      matchesService,
      matchesSearch,
      matchesLocation,
      matchesPetType,
      matchesRating,
      matchesVerified,
      finalMatch: matchesSearch && matchesService && matchesLocation && matchesPetType && matchesRating && matchesVerified
    });

    return matchesSearch && matchesService && matchesLocation && matchesPetType && matchesRating && matchesVerified;
  });

  const toggleFavorite = (providerId: string) => {
    setFavorites(prev =>
      prev.includes(providerId)
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };



  return (
    <div className="min-h-screen bg-gradient-to-r from-green-600 to-blue-600 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-green-400/30 to-blue-400/20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-blue-400/30 to-green-400/20 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-green-300/20 to-blue-300/20 blur-3xl"></div>
      </div>

      <div className="relative z-10 pt-24 pb-12">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white">
              Find Pet Services
            </h1>
            <p className="text-xl text-green-100 max-w-2xl mx-auto leading-relaxed">
              Search for trusted pet care providers and their services in your area
            </p>
          </div>

          {/* Search Form */}
          <div className="max-w-6xl mx-auto mb-12">
            <div className="bg-white/95 backdrop-blur-sm border border-white/20 rounded-3xl p-8 shadow-2xl">
              {/* Main Search Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {/* Location */}
                <div className="relative group">
                  <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500 group-focus-within:text-blue-600 transition-colors" />
                  <input
                    type="text"
                    placeholder="Enter location"
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 placeholder-gray-500 shadow-sm hover:shadow-md"
                  />
                </div>

                {/* Service Type */}
                <div className="relative group">
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full px-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 shadow-sm hover:shadow-md appearance-none cursor-pointer"
                  >
                    {serviceTypes.map(service => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>

                {/* Date */}
                <div className="relative group">
                  <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500 group-focus-within:text-blue-600 transition-colors" />
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 shadow-sm hover:shadow-md"
                  />
                </div>

                {/* Pet Type */}
                <div className="relative group">
                  <select
                    value={selectedPetType}
                    onChange={(e) => setSelectedPetType(e.target.value)}
                    className="w-full px-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 shadow-sm hover:shadow-md appearance-none cursor-pointer"
                  >
                    <option value="">All Pets</option>
                    <option value="dog">Dogs</option>
                    <option value="cat">Cats</option>
                    <option value="bird">Birds</option>
                    <option value="rabbit">Rabbits</option>
                    <option value="fish">Fish</option>
                    <option value="reptile">Reptiles</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              {/* Search Bar and Button */}
              <div className="flex gap-4">
                <div className="flex-1 relative group">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500 group-focus-within:text-blue-600 transition-colors" />
                  <input
                    type="text"
                    placeholder="Search services, emergency care, veterinary..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 placeholder-gray-500 shadow-sm hover:shadow-md"
                  />
                </div>

                <button
                  onClick={handleSearch}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center gap-3 min-w-[140px] justify-center"
                >
                  <Search className="w-5 h-5" />
                  {loading ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="max-w-7xl mx-auto">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4">
              <div>
                <h2 className="text-2xl font-bold text-white mb-2">
                  Available Providers
                </h2>
                <p className="text-white">
                  <span className="font-semibold text-white">{filteredProviders.length}</span> providers found
                </p>
              </div>

              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-4 py-2 rounded-xl bg-white text-green-600 hover:bg-gray-100 transition-all duration-200 border border-white"
                >
                  <SlidersHorizontal className="w-4 h-4" />
                  Filters
                </button>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 rounded-xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="mb-8 p-6 backdrop-blur-xl bg-white/80 border border-blue-200/30 rounded-2xl shadow-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Minimum Rating</label>
                    <select
                      value={filters.minRating}
                      onChange={(e) => setFilters({...filters, minRating: Number(e.target.value)})}
                      className="w-full px-4 py-3 rounded-xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
                    >
                      <option value={0}>Any Rating</option>
                      <option value={3}>3+ Stars</option>
                      <option value={4}>4+ Stars</option>
                      <option value={4.5}>4.5+ Stars</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Max Distance</label>
                    <select
                      value={filters.maxDistance}
                      onChange={(e) => setFilters({...filters, maxDistance: Number(e.target.value)})}
                      className="w-full px-4 py-3 rounded-xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
                    >
                      <option value={10}>Within 10 miles</option>
                      <option value={25}>Within 25 miles</option>
                      <option value={50}>Within 50 miles</option>
                      <option value={100}>Within 100 miles</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Verified Only</label>
                    <label className="flex items-center p-3 rounded-xl border border-blue-200/50 bg-white/90 backdrop-blur-sm cursor-pointer hover:bg-blue-50/50 transition-all">
                      <input
                        type="checkbox"
                        checked={filters.verified}
                        onChange={(e) => setFilters({...filters, verified: e.target.checked})}
                        className="rounded border-blue-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show only verified providers</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* No Results Message */}
            {!loading && filteredProviders.length === 0 && (
              <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-2xl p-12 text-center shadow-lg">
                <Search className="w-16 h-16 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">No providers available</h3>
                <p className="text-gray-600 mb-6">
                  {providers.length === 0
                    ? "No providers are currently available in your area."
                    : "No providers match your search criteria. Try adjusting your filters or search terms."
                  }
                </p>
                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedService('All Services');
                      setSelectedLocation('');
                      setSelectedDate('');
                      setSelectedPetType('');
                      setFilters({
                        minRating: 0,
                        maxDistance: 20,
                        priceRange: 'any',
                        availability: 'any',
                        verified: false
                      });
                    }}
                    className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
                  >
                    Clear All Filters
                  </button>
                  <button
                    onClick={loadProviders}
                    className="px-6 py-3 border border-blue-600 text-blue-600 rounded-xl hover:bg-blue-50 transition-colors"
                  >
                    Refresh Results
                  </button>
                </div>
              </div>
            )}

            {/* Provider Cards */}
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredProviders.map((provider) => (
                  <div key={provider.id} className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
                    {/* Provider Image & Name */}
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                        {provider.profilePhoto ? (
                          <Image
                            src={provider.profilePhoto}
                            alt={provider.businessName}
                            width={64}
                            height={64}
                            className="w-16 h-16 rounded-2xl object-cover"
                          />
                        ) : (
                          <span className="text-white font-bold text-lg">
                            {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
                          </span>
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                          {provider.businessName}
                        </h3>
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${i < Math.floor(provider.rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                            />
                          ))}
                          <span className="text-sm text-gray-500">
                            {provider.rating?.toFixed(1) || 'New'} ({provider.reviewCount || 0})
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Provider Info */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <MapPin className="w-4 h-4 text-blue-500" />
                        <span>{provider.address}, {provider.city}, {provider.state}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="w-4 h-4 text-blue-500" />
                        <a href={`tel:${provider.phone}`} className="hover:text-blue-600 hover:underline">
                          {provider.phone}
                        </a>
                      </div>
                    </div>

                    {/* Specialties */}
                    {provider.specialties && provider.specialties.length > 0 && (
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-2 mb-4">
                          {provider.specialties.slice(0, 3).map((specialty: string, i: number) => (
                            <span key={i} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">
                              {specialty}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-3">
                      <button
                        onClick={() => window.location.href = isAuthenticated ? `/appointments?providerId=${provider.id}` : '/auth/signup'}
                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                      >
                        <Calendar className="w-4 h-4" />
                        {isAuthenticated ? 'Book Appointment' : 'Sign Up to Book'}
                      </button>
                      <button
                        onClick={() => window.location.href = `/provider/public/${provider.id}`}
                        className="w-full border border-blue-300 text-blue-600 hover:bg-blue-50 py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2"
                      >
                        <MessageCircle className="w-4 h-4" />
                        View Profile
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  );
}