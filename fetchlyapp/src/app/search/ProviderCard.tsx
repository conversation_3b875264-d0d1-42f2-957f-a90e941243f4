'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import {
  Star,
  MapPin,
  Calendar,
  Phone,
  MessageCircle,
  Mail,
  Loader2,
  X,
  User,
  AlertCircle,
  Heart,
  Share2,
  Verified,
  Award
} from 'lucide-react';

interface ProviderCardProps {
  provider: any; // Replace with your Provider type
  showSimpleCard: boolean;
}

export default function ProviderCard({ provider, showSimpleCard }: ProviderCardProps) {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [bookingDate, setBookingDate] = useState('');
  const [bookingTime, setBookingTime] = useState('');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingError, setBookingError] = useState('');

  const handleBookNow = () => {
    if (!isAuthenticated) {
      router.push(`/auth/signin?redirect=/search`);
      return;
    }
    setShowBookingModal(true);
  };

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!bookingDate || !bookingTime) {
      setBookingError('Please select both date and time');
      return;
    }

    setIsSubmitting(true);
    setBookingError('');

    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerId: provider.id,
          serviceId: provider.serviceType.toLowerCase(),
          serviceName: provider.serviceType,
          date: bookingDate,
          time: bookingTime,
          notes,
          providerName: provider.businessName,
          providerPhoto: provider.profilePhoto,
          userId: user?.id,
          userName: user?.name,
          userEmail: user?.email,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create booking');
      }

      await response.json();
      toast.success('Booking request sent successfully!');
      setShowBookingModal(false);
      
      // Reset form
      setBookingDate('');
      setBookingTime('');
      setNotes('');
      
    } catch (error) {
      console.error('Error creating booking:', error);
      setBookingError('Failed to create booking. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const BookingModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-xl font-bold">Book Appointment</h3>
          <button 
            onClick={() => setShowBookingModal(false)}
            className="text-gray-400 hover:text-gray-600"
            disabled={isSubmitting}
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        
        <div className="p-6">
          <form onSubmit={handleBookingSubmit}>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  min={new Date().toISOString().split('T')[0]}
                  value={bookingDate}
                  onChange={(e) => setBookingDate(e.target.value)}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Time
                </label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  value={bookingTime}
                  onChange={(e) => setBookingTime(e.target.value)}
                  required
                >
                  <option value="">Select a time</option>
                  {['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'].map((t) => (
                    <option key={t} value={t}>{t}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (Optional)
                </label>
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows={3}
                  placeholder="Any special requests or instructions..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>

              {bookingError && (
                <div className="p-3 bg-red-50 text-red-600 rounded-lg flex items-center gap-2">
                  <AlertCircle className="w-5 h-5" />
                  <span>{bookingError}</span>
                </div>
              )}

              <div className="pt-4 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setShowBookingModal(false)}
                  disabled={isSubmitting}
                  className="px-4 py-2.5 text-gray-600 hover:bg-gray-100 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 py-2.5 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-lg font-medium shadow-sm hover:shadow-md transition-all flex items-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Booking...
                    </>
                  ) : (
                    'Confirm Booking'
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );

  if (showSimpleCard) {
    // Simple card for non-logged users
    return (
      <div className="relative">
        {showBookingModal && <BookingModal />}
        
        <div className="backdrop-blur-xl bg-white/90 border border-green-200/30 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
          {/* Provider Image & Name */}
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center shadow-lg">
              {provider.profilePhoto ? (
                <Image
                  src={provider.profilePhoto}
                  alt={provider.businessName}
                  width={64}
                  height={64}
                  className="w-16 h-16 rounded-2xl object-cover"
                />
              ) : (
                <span className="text-white font-bold text-lg">
                  {provider.businessName?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                </span>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-lg text-gray-900 group-hover:text-green-600 transition-colors">
                {provider.businessName}
              </h3>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(provider.rating || 0)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="text-sm text-gray-600 ml-1">
                  {provider.rating?.toFixed(1) || 'New'}
                </span>
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="flex items-center gap-2 mb-3">
            <MapPin className="w-4 h-4 text-green-500" />
            <span className="text-gray-700">{provider.city}, {provider.state} {provider.zipCode}</span>
          </div>

          {/* Type of Service */}
          <div className="mb-3">
            <span className="px-3 py-1 bg-gradient-to-r from-green-100 to-blue-100 text-green-700 rounded-full text-sm font-medium">
              {provider.serviceType}
            </span>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-2">
            <button
              onClick={handleBookNow}
              className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              <Calendar className="w-4 h-4" />
              Book Now
            </button>
            <a
              href={`/provider/public/${provider.id}`}
              className="w-full text-center bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2"
            >
              <User className="w-4 h-4" />
              View Profile
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Full card for logged-in users
  return (
    <div className="relative">
      {showBookingModal && <BookingModal />}
      
      <div className="backdrop-blur-xl bg-white/90 border border-green-200/30 rounded-3xl p-6 hover:shadow-2xl hover:bg-white/95 transition-all duration-300 group hover:scale-[1.02]">
        {/* Header with provider info */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center shadow-lg">
              {provider.profilePhoto ? (
                <Image
                  src={provider.profilePhoto}
                  alt={provider.businessName}
                  width={64}
                  height={64}
                  className="w-16 h-16 rounded-2xl object-cover"
                />
              ) : (
                <span className="text-white font-bold text-lg">
                  {provider.businessName?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                </span>
              )}
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-700 transition-colors">
                  {provider.businessName}
                </h3>
                {provider.verified && (
                  <Verified className="w-5 h-5 text-green-500" />
                )}
                {provider.featured && (
                  <Award className="w-5 h-5 text-yellow-500" />
                )}
              </div>
              <p className="text-gray-600 font-medium">{provider.ownerName}</p>
              <p className="text-sm text-gray-500">{provider.serviceType}</p>
            </div>
          </div>

          <div className="flex gap-2">
            <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
              <Heart className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <Share2 className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Rating and Reviews */}
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(provider.rating || 0)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
            <span className="ml-1 text-gray-700 font-medium">
              {provider.rating?.toFixed(1) || 'New'}
            </span>
            <span className="text-gray-400 ml-1">•</span>
            <a href="#reviews" className="text-green-600 hover:underline">
              {provider.reviewCount || 0} reviews
            </a>
          </div>
        </div>

        {/* Location and Contact */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center gap-2 text-gray-700">
            <MapPin className="w-4 h-4 text-green-500 flex-shrink-0" />
            <p>{provider.address}, {provider.city}, {provider.state} {provider.zipCode}</p>
          </div>
          
          {provider.phone && (
            <div className="flex items-center gap-2 text-gray-700">
              <Phone className="w-4 h-4 text-green-500 flex-shrink-0" />
              <a href={`tel:${provider.phone}`} className="hover:underline">
                {provider.phone}
              </a>
            </div>
          )}
          
          {provider.email && (
            <div className="flex items-center gap-2 text-gray-700">
              <Mail className="w-4 h-4 text-green-500 flex-shrink-0" />
              <a href={`mailto:${provider.email}`} className="hover:underline">
                {provider.email}
              </a>
            </div>
          )}
        </div>

        {/* Description */}
        <p className="text-gray-600 mb-6 line-clamp-3">
          {provider.bio || 'No description available.'}
        </p>

        {/* Services */}
        <div className="mb-6">
          <h4 className="font-semibold text-gray-800 mb-2">Services</h4>
          <div className="flex flex-wrap gap-2">
            {provider.services?.map((service: string, i: number) => (
              <span key={i} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                {service}
              </span>
            )) || (
              <p className="text-gray-500">No services listed</p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          <div className="flex gap-3">
            <button 
              onClick={handleBookNow}
              className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              <Calendar className="w-5 h-5" />
              Book Appointment
            </button>
            <button className="p-3 bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 rounded-xl transition-colors">
              <MessageCircle className="w-5 h-5" />
            </button>
          </div>
          <a
            href={`/provider/public/${provider.id}`}
            className="w-full text-center bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2"
          >
            <User className="w-4 h-4" />
            View Full Profile
          </a>
        </div>
      </div>
    </div>
  );
}
