'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getProviderByUserId } from '@/lib/firebase/providers';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export default function ProviderTestPage() {
  const { user } = useAuth();
  const [testUserId, setTestUserId] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [allProviders, setAllProviders] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]);

  const testProviderAccess = async () => {
    if (!testUserId.trim()) {
      alert('Please enter a user ID to test');
      return;
    }

    setLoading(true);
    try {
      console.log('🧪 Testing provider access for userId:', testUserId);
      const provider = await getProviderByUserId(testUserId);
      setResult(provider);
      console.log('🧪 Test result:', provider);
    } catch (error) {
      console.error('🧪 Test error:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const loadAllProviders = async () => {
    setLoading(true);
    try {
      const providersSnapshot = await getDocs(collection(db, 'providers'));
      const providers = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAllProviders(providers);
      console.log('🧪 All providers:', providers);
    } catch (error) {
      console.error('🧪 Error loading providers:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAllUsers = async () => {
    setLoading(true);
    try {
      const usersSnapshot = await getDocs(
        query(collection(db, 'users'), where('role', '==', 'provider'))
      );
      const users = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAllUsers(users);
      console.log('🧪 All provider users:', users);
    } catch (error) {
      console.error('🧪 Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Provider Profile Debug Test</h1>
        
        <div className="bg-white rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current User Info</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        <div className="bg-white rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Provider Access</h2>
          <div className="flex gap-4 mb-4">
            <input
              type="text"
              value={testUserId}
              onChange={(e) => setTestUserId(e.target.value)}
              placeholder="Enter user ID to test"
              className="flex-1 px-4 py-2 border rounded-lg"
            />
            <button
              onClick={testProviderAccess}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Access'}
            </button>
          </div>
          
          {result && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Result:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">All Providers</h2>
              <button
                onClick={loadAllProviders}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
              >
                Load
              </button>
            </div>
            <div className="max-h-96 overflow-auto">
              <pre className="bg-gray-100 p-4 rounded text-sm">
                {JSON.stringify(allProviders, null, 2)}
              </pre>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Provider Users</h2>
              <button
                onClick={loadAllUsers}
                disabled={loading}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Load
              </button>
            </div>
            <div className="max-h-96 overflow-auto">
              <pre className="bg-gray-100 p-4 rounded text-sm">
                {JSON.stringify(allUsers, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="flex gap-4">
            <button
              onClick={() => setTestUserId(user?.id || '')}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Test My Profile
            </button>
            <button
              onClick={() => window.open('/provider/profile', '_blank')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Open Provider Profile
            </button>
            <button
              onClick={() => window.open('/community', '_blank')}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Open Community
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
