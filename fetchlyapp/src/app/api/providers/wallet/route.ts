import { NextRequest, NextResponse } from 'next/server';
import { stripe, logProductionEvent } from '@/lib/stripe/server-config';
import { verifyIdToken, adminDb } from '@/lib/firebase-admin';
import { COLLECTIONS } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      decodedToken = await verifyIdToken(token);
    } catch (error) {
      logProductionEvent('wallet_auth_failed', { error: error.message }, 'error');
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    if (!adminDb) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    // Get provider data
    const providerDoc = await adminDb.collection(COLLECTIONS.PROVIDERS).doc(decodedToken.uid).get();
    if (!providerDoc.exists) {
      return NextResponse.json({ error: 'Provider profile not found' }, { status: 404 });
    }

    const provider = providerDoc.data();

    if (!provider?.stripeAccountId) {
      return NextResponse.json({
        success: true,
        balance: { available: 0, pending: 0 },
        payouts: [],
        isOnboarded: false,
        message: 'No Stripe account found',
      });
    }

    try {
      // Get Stripe account balance
      const balance = await stripe.balance.retrieve({
        stripeAccount: provider.stripeAccountId,
      });

      // Calculate available and pending amounts
      const availableAmount = balance.available.reduce((sum, item) => sum + item.amount, 0) / 100;
      const pendingAmount = balance.pending.reduce((sum, item) => sum + item.amount, 0) / 100;

      // Get recent payouts
      const payouts = await stripe.payouts.list(
        { limit: 10 },
        { stripeAccount: provider.stripeAccountId }
      );

      // Get account status
      const account = await stripe.accounts.retrieve(provider.stripeAccountId);
      const isOnboarded = account.details_submitted && 
                         account.charges_enabled && 
                         account.payouts_enabled;

      logProductionEvent('wallet_data_retrieved', { 
        providerId: decodedToken.uid, 
        availableAmount, 
        pendingAmount,
        payoutCount: payouts.data.length
      });

      return NextResponse.json({
        success: true,
        balance: {
          available: availableAmount,
          pending: pendingAmount,
        },
        payouts: payouts.data.map(payout => ({
          id: payout.id,
          amount: payout.amount / 100,
          currency: payout.currency,
          status: payout.status,
          arrival_date: payout.arrival_date,
          created: payout.created,
          description: payout.description,
        })),
        isOnboarded,
        accountId: provider.stripeAccountId,
        status: {
          detailsSubmitted: account.details_submitted,
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
        },
      });

    } catch (error: any) {
      logProductionEvent('stripe_wallet_data_failed', { 
        providerId: decodedToken.uid, 
        error: error.message 
      }, 'error');

      // If Stripe account exists but has issues, return empty data instead of demo data
      return NextResponse.json({
        success: true,
        balance: { available: 0, pending: 0 },
        payouts: [],
        isOnboarded: false,
        error: 'Unable to retrieve wallet data. Please complete Stripe onboarding.',
      });
    }

  } catch (error: any) {
    logProductionEvent('wallet_api_error', { error: error.message }, 'error');
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
