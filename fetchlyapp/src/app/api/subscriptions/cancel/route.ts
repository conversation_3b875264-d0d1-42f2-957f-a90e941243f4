import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { ServerSubscriptionService } from '@/lib/stripe/server-subscription-service';

// Cancel subscription
async function cancelSubscriptionHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { cancelAtPeriodEnd = true } = await request.json();
    
    if (user.role !== 'provider') {
      return NextResponse.json({
        success: false,
        error: 'Only providers can cancel subscriptions'
      }, { status: 403 });
    }

    const result = await ServerSubscriptionService.cancelSubscription(user.id, cancelAtPeriodEnd);

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: cancelAtPeriodEnd 
        ? 'Subscription will be cancelled at the end of the billing period'
        : 'Subscription cancelled immediately'
    });
  } catch (error) {
    console.error('Cancel subscription error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to cancel subscription'
    }, { status: 500 });
  }
}

export const POST = withMiddleware(cancelSubscriptionHandler, {
  auth: true,
  requireVerified: true
});
