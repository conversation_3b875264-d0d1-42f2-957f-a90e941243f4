import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { UserService } from '@/lib/services';

// Get user profile
async function getProfile<PERSON><PERSON><PERSON>(request: NextRequest) {
  try {
    const user = (request as any).user;
    
    const userProfile = await UserService.getUserById(user.id);
    if (!userProfile) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      user: userProfile
    });
  } catch (error) {
    console.error('Get profile error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get user profile'
    }, { status: 500 });
  }
}

// Update user profile
async function updateProfileHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    const updateData = (request as any).validatedData;
    
    const updatedUser = await UserService.updateUser(user.id, updateData);
    
    return NextResponse.json({
      success: true,
      user: updatedUser,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update profile'
    }, { status: 500 });
  }
}

// Validation schema for profile updates
const updateProfileSchema = {
  name: { required: false, type: 'string' as const, minLength: 2, maxLength: 100 },
  phone: { required: false, type: 'string' as const },
  location: { required: false, type: 'string' as const, maxLength: 200 },
  bio: { required: false, type: 'string' as const, maxLength: 500 },
  avatar: { required: false, type: 'string' as const }
};

export const GET = withMiddleware(getProfileHandler, {
  auth: true,
  requireVerified: true
});

export const PUT = withMiddleware(updateProfileHandler, {
  auth: true,
  requireVerified: true,
  validation: updateProfileSchema
});
