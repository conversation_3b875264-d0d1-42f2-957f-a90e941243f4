import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Call the send-email API
    const emailResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/send-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'test',
        to: '<EMAIL>',
        subject: '🧪 Test Email from Fetchly Platform',
        data: {
          message: 'Congratulations! Your email notification system is working correctly.',
          timestamp: new Date(),
          platform: 'Fetchly',
          environment: process.env.NODE_ENV || 'development',
          testType: 'Email Configuration Test'
        }
      })
    });

    const result = await emailResponse.json();

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully! Check your <NAME_EMAIL>'
      });
    } else {
      throw new Error(result.error || 'Failed to send email');
    }
  } catch (error: any) {
    console.error('Test email failed:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to send test email',
      details: error.toString()
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email test endpoint. Use POST to send a test email.'
  });
}
