import { NextRequest, NextResponse } from 'next/server';
import { corsHeaders, securityHeaders } from '@/lib/middleware';

export async function POST(request: NextRequest) {
  try {
    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    });
    
    // Clear refresh token cookie
    response.cookies.set('refreshToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    });
    
    // Add security headers
    const headers = { ...corsHeaders(), ...securityHeaders() };
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
    
  } catch (error) {
    console.error('Logout error:', error);
    
    const response = NextResponse.json({
      success: false,
      error: 'Logout failed'
    }, { status: 500 });
    
    // Add security headers
    const headers = { ...corsHeaders(), ...securityHeaders() };
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  }
}

export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 });
  
  // Add CORS headers
  const headers = corsHeaders();
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}
