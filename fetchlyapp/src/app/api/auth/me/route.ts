import { NextRequest, NextResponse } from 'next/server';
import { withAuth, corsHeaders, securityHeaders } from '@/lib/middleware';

async function handler(request: NextRequest) {
  try {
    // User is already authenticated by middleware
    const user = (request as any).user;
    
    const response = NextResponse.json({
      success: true,
      user
    });
    
    // Add security headers
    const headers = { ...corsHeaders(), ...securityHeaders() };
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
    
  } catch (error) {
    console.error('Get user error:', error);
    
    const response = NextResponse.json({
      success: false,
      error: 'Failed to get user information'
    }, { status: 500 });
    
    // Add security headers
    const headers = { ...corsHeaders(), ...securityHeaders() };
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  }
}

// Apply authentication middleware
export const GET = withAuth(handler);

export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 });
  
  // Add CORS headers
  const headers = corsHeaders();
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}
