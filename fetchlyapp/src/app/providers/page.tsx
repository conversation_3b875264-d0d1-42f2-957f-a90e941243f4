'use client';

import { useState } from 'react';
import { CheckCircle, Star, DollarSign, Calendar, Users, TrendingUp, Shield, Smartphone } from 'lucide-react';
import Link from 'next/link';

const benefits = [
  {
    icon: DollarSign,
    title: "Increase Your Income",
    description: "Earn up to 40% more with our premium booking platform",
    color: "primary"
  },
  {
    icon: Calendar,
    title: "Flexible Scheduling",
    description: "Set your own hours and manage bookings easily",
    color: "secondary"
  },
  {
    icon: Users,
    title: "Grow Your Client Base",
    description: "Connect with thousands of pet owners in your area",
    color: "accent"
  },
  {
    icon: Shield,
    title: "Insurance & Protection",
    description: "Comprehensive coverage for you and your business",
    color: "warm"
  }
];

const features = [
  "Professional profile creation",
  "Automated booking system",
  "Secure payment processing",
  "Customer review management",
  "Marketing tools & analytics",
  "24/7 customer support",
  "Mobile app for providers",
  "Background check assistance"
];

const testimonials = [
  {
    name: "<PERSON>",
    service: "Pet Grooming",
    rating: 5,
    text: "<PERSON><PERSON><PERSON> has transformed my business! I've doubled my client base in just 3 months.",
    earnings: "$3,200/month"
  },
  {
    name: "<PERSON><PERSON>",
    service: "Veterinary Care",
    rating: 5,
    text: "The platform makes it so easy to manage appointments and connect with pet owners.",
    earnings: "$8,500/month"
  },
  {
    name: "Lisa Rodriguez",
    service: "Pet Daycare",
    rating: 5,
    text: "I love how professional and reliable the booking system is. Highly recommend!",
    earnings: "$2,800/month"
  }
];

export default function ProvidersPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    experience: '',
    location: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-cool-800">
              Join the <span className="text-gradient">Leading</span> Pet Care Platform
            </h1>
            <p className="text-xl md:text-2xl text-cool-600 mb-8 max-w-3xl mx-auto">
              Connect with thousands of pet owners, grow your business, and make a difference in pets' lives
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="#signup" className="btn-primary">
                Start Earning Today
              </Link>
              <Link href="#learn-more" className="btn-secondary">
                Learn More
              </Link>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">10K+</div>
              <div className="text-cool-600">Active Providers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">50K+</div>
              <div className="text-cool-600">Monthly Bookings</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">4.9★</div>
              <div className="text-cool-600">Average Rating</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">$4.2M</div>
              <div className="text-cool-600">Paid to Providers</div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Why Choose Fetchly?
            </h2>
            <p className="text-xl text-cool-600 max-w-2xl mx-auto">
              Join thousands of successful pet care providers who trust Fetchly
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div key={index} className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-${benefit.color}-500 to-${benefit.color}-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-cool-800">{benefit.title}</h3>
                  <p className="text-cool-600">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-cool-800">
                Everything You Need to <span className="text-gradient">Succeed</span>
              </h2>
              <p className="text-xl text-cool-600 mb-8">
                Our comprehensive platform provides all the tools you need to grow your pet care business
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0" />
                    <span className="text-cool-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="glass-card rounded-2xl p-8">
                <div className="text-center mb-6">
                  <Smartphone className="w-16 h-16 mx-auto text-primary-500 mb-4" />
                  <h3 className="text-2xl font-bold text-cool-800 mb-2">Mobile App</h3>
                  <p className="text-cool-600">Manage your business on the go</p>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
                    <span className="text-cool-700">Today's Bookings</span>
                    <span className="font-bold text-primary-500">8</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
                    <span className="text-cool-700">This Week's Earnings</span>
                    <span className="font-bold text-accent-500">$1,240</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
                    <span className="text-cool-700">Average Rating</span>
                    <span className="font-bold text-yellow-500">4.9 ★</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Success Stories
            </h2>
            <p className="text-xl text-cool-600">
              Hear from our top-performing providers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="glass-card rounded-2xl p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-cool-700 mb-4 italic">"{testimonial.text}"</p>
                <div className="border-t border-white/30 pt-4">
                  <div className="font-bold text-cool-800">{testimonial.name}</div>
                  <div className="text-cool-600 text-sm">{testimonial.service}</div>
                  <div className="text-accent-500 font-bold text-sm mt-1">
                    Earning {testimonial.earnings}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Signup Section */}
      <section id="signup" className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="glass-card rounded-2xl p-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold mb-4 text-cool-800">
                  Ready to Get Started?
                </h2>
                <p className="text-cool-600">
                  Join thousands of successful pet care providers today
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Service Type</label>
                    <select
                      value={formData.service}
                      onChange={(e) => setFormData({ ...formData, service: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                      required
                    >
                      <option value="">Select a service</option>
                      <option value="grooming">Pet Grooming</option>
                      <option value="veterinary">Veterinary Care</option>
                      <option value="hotel">Pet Hotels</option>
                      <option value="daycare">Pet Daycare</option>
                      <option value="training">Pet Training</option>
                      <option value="walking">Pet Walking</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Years of Experience</label>
                  <select
                    value={formData.experience}
                    onChange={(e) => setFormData({ ...formData, experience: e.target.value })}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    required
                  >
                    <option value="">Select experience level</option>
                    <option value="0-1">0-1 years</option>
                    <option value="2-5">2-5 years</option>
                    <option value="6-10">6-10 years</option>
                    <option value="10+">10+ years</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Location</label>
                  <input
                    type="text"
                    placeholder="City, State"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    required
                  />
                </div>

                <button type="submit" className="btn-primary w-full">
                  Join Fetchly Today
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
