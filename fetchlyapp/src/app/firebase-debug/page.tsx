'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProvider } from '@/contexts/ProviderContext';
import { 
  getProviderByUserId, 
  getProviderServices, 
  updateProvider,
  createProvider 
} from '@/lib/firebase/providers';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';

export default function FirebaseDebugPage() {
  const { user } = useAuth();
  const { provider, services, refreshProviderData } = useProvider();
  const [debugData, setDebugData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [rawFirebaseData, setRawFirebaseData] = useState<any>(null);

  const checkFirebaseData = async () => {
    if (!user) {
      toast.error('No user logged in');
      return;
    }

    setIsLoading(true);
    try {
      console.log('🔍 Checking Firebase data for user:', user.id);

      // 1. Check user document
      const userDocRef = doc(db, 'users', user.id);
      const userDoc = await getDoc(userDocRef);
      const userData = userDoc.exists() ? userDoc.data() : null;

      // 2. Check provider document
      const providerData = await getProviderByUserId(user.id);

      // 3. Check services
      let servicesData = [];
      if (providerData?.id) {
        servicesData = await getProviderServices(providerData.id);
      }

      // 4. Check providers collection directly
      const providersQuery = query(
        collection(db, 'providers'),
        where('userId', '==', user.id)
      );
      const providersSnapshot = await getDocs(providersQuery);
      const rawProviderDocs = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const debugInfo = {
        timestamp: new Date().toISOString(),
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          fromAuth: user
        },
        userDocument: userData,
        providerFromFunction: providerData,
        providerFromContext: provider,
        rawProviderDocuments: rawProviderDocs,
        services: servicesData,
        servicesFromContext: services
      };

      setDebugData(debugInfo);
      setRawFirebaseData({
        userData,
        providerData,
        servicesData,
        rawProviderDocs
      });

      console.log('🔍 Debug data collected:', debugInfo);

    } catch (error) {
      console.error('❌ Error checking Firebase data:', error);
      toast.error('Error checking Firebase data');
    } finally {
      setIsLoading(false);
    }
  };

  const fixProviderData = async () => {
    if (!user || !rawFirebaseData?.providerData) {
      toast.error('No provider data to fix');
      return;
    }

    try {
      setIsLoading(true);
      const providerData = rawFirebaseData.providerData;

      // Ensure all required fields are present
      const updatedData = {
        ...providerData,
        businessName: providerData.businessName || user.name + "'s Business",
        ownerName: providerData.ownerName || user.name,
        email: providerData.email || user.email,
        profilePhoto: providerData.profilePhoto || '/favicon.png',
        bannerPhoto: providerData.bannerPhoto || '/fetchlylogo.png',
        description: providerData.description || 'Welcome to my pet services business!',
        phone: providerData.phone || '',
        website: providerData.website || '',
        address: providerData.address || '',
        city: providerData.city || '',
        state: providerData.state || '',
        zipCode: providerData.zipCode || '',
        serviceType: providerData.serviceType || 'General Pet Services',
        experience: providerData.experience || '0-1 years',
        specialties: providerData.specialties || [],
        businessHours: providerData.businessHours || {
          monday: { open: '09:00', close: '17:00', closed: false },
          tuesday: { open: '09:00', close: '17:00', closed: false },
          wednesday: { open: '09:00', close: '17:00', closed: false },
          thursday: { open: '09:00', close: '17:00', closed: false },
          friday: { open: '09:00', close: '17:00', closed: false },
          saturday: { open: '10:00', close: '16:00', closed: false },
          sunday: { open: '10:00', close: '16:00', closed: true }
        }
      };

      await updateProvider(providerData.id, updatedData);
      await refreshProviderData();
      
      toast.success('Provider data fixed successfully!');
      await checkFirebaseData(); // Refresh debug data

    } catch (error) {
      console.error('❌ Error fixing provider data:', error);
      toast.error('Error fixing provider data');
    } finally {
      setIsLoading(false);
    }
  };

  const createMissingProvider = async () => {
    if (!user) {
      toast.error('No user logged in');
      return;
    }

    try {
      setIsLoading(true);
      
      const newProviderId = await createProvider({
        userId: user.id,
        businessName: user.name + "'s Business",
        ownerName: user.name,
        email: user.email,
        phone: '',
        serviceType: 'General Pet Services',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        description: 'Welcome to my pet services business!',
        experience: '0-1 years',
        specialties: [],
        status: 'pending',
        verified: false,
        featured: false,
        profilePhoto: '/favicon.png',
        bannerPhoto: '/fetchlylogo.png',
        businessHours: {
          monday: { open: '09:00', close: '17:00', closed: false },
          tuesday: { open: '09:00', close: '17:00', closed: false },
          wednesday: { open: '09:00', close: '17:00', closed: false },
          thursday: { open: '09:00', close: '17:00', closed: false },
          friday: { open: '09:00', close: '17:00', closed: false },
          saturday: { open: '10:00', close: '16:00', closed: false },
          sunday: { open: '10:00', close: '16:00', closed: true }
        }
      });

      console.log('✅ Created provider with ID:', newProviderId);
      await refreshProviderData();
      toast.success('Provider profile created successfully!');
      await checkFirebaseData(); // Refresh debug data

    } catch (error) {
      console.error('❌ Error creating provider:', error);
      toast.error('Error creating provider profile');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      checkFirebaseData();
    }
  }, [user]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Firebase Debug Tool</h1>
          <p className="text-gray-600">Please log in to debug Firebase data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">Firebase Data Debug Tool</h1>
          
          <div className="space-y-6">
            <div className="flex gap-4 flex-wrap">
              <button
                onClick={checkFirebaseData}
                disabled={isLoading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? 'Checking...' : 'Check Firebase Data'}
              </button>
              
              {rawFirebaseData?.providerData && (
                <button
                  onClick={fixProviderData}
                  disabled={isLoading}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  Fix Provider Data
                </button>
              )}
              
              {!rawFirebaseData?.providerData && (
                <button
                  onClick={createMissingProvider}
                  disabled={isLoading}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  Create Provider Profile
                </button>
              )}
            </div>

            {debugData && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-100 rounded-lg p-4">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Debug Summary</h2>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="font-medium">User ID:</span>
                      <span className="text-gray-600">{debugData.user.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">User Role:</span>
                      <span className="text-gray-600">{debugData.user.role}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Provider Found:</span>
                      <span className={debugData.providerFromFunction ? 'text-green-600' : 'text-red-600'}>
                        {debugData.providerFromFunction ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Provider ID:</span>
                      <span className="text-gray-600">
                        {debugData.providerFromFunction?.id || 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Services Count:</span>
                      <span className="text-gray-600">{debugData.services?.length || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-100 rounded-lg p-4">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Raw Firebase Data</h2>
                  <div className="max-h-96 overflow-auto">
                    <pre className="text-xs text-gray-700">
                      {JSON.stringify(debugData, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
