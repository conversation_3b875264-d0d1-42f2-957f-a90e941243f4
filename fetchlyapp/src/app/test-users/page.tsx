'use client';

import { useState, useEffect } from 'react';
import { collection, getDocs, query, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { useAuth } from '@/contexts/AuthContext';

export default function TestUsersPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [pets, setPets] = useState([]);
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTestData = async () => {
      try {
        console.log('🔍 Loading test data...');
        console.log('👤 Current user:', user);

        // Load users
        const usersQuery = query(collection(db, 'users'), limit(10));
        const usersSnapshot = await getDocs(usersQuery);
        const usersData = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('👥 Users found:', usersData);
        setUsers(usersData);

        // Load pets
        const petsQuery = query(collection(db, 'pets'), limit(10));
        const petsSnapshot = await getDocs(petsQuery);
        const petsData = petsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('🐾 Pets found:', petsData);
        setPets(petsData);

        // Load posts
        const postsQuery = query(collection(db, 'posts'), limit(10));
        const postsSnapshot = await getDocs(postsQuery);
        const postsData = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('📝 Posts found:', postsData);
        setPosts(postsData);

      } catch (error) {
        console.error('❌ Error loading test data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTestData();
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading test data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Database Test Data</h1>
        
        {/* Current User */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current User</h2>
          {user ? (
            <div className="bg-gray-50 p-4 rounded-lg">
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Name:</strong> {user.name}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Role:</strong> {user.role}</p>
            </div>
          ) : (
            <p className="text-gray-500">Not authenticated</p>
          )}
        </div>

        {/* Users */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Users ({users.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {users.map((user) => (
              <div key={user.id} className="bg-gray-50 p-4 rounded-lg">
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Name:</strong> {user.name || 'No name'}</p>
                <p><strong>Email:</strong> {user.email || 'No email'}</p>
                <p><strong>Role:</strong> {user.role || 'No role'}</p>
                <a 
                  href={`/profile/public/${user.id}`}
                  className="inline-block mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                >
                  View Profile
                </a>
              </div>
            ))}
          </div>
        </div>

        {/* Pets */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Pets ({pets.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {pets.map((pet) => (
              <div key={pet.id} className="bg-gray-50 p-4 rounded-lg">
                <p><strong>ID:</strong> {pet.id}</p>
                <p><strong>Name:</strong> {pet.name || 'No name'}</p>
                <p><strong>Type:</strong> {pet.type || 'No type'}</p>
                <p><strong>Owner ID:</strong> {pet.userId || 'No owner'}</p>
                <p><strong>Image:</strong> {pet.profileImage || pet.photo || pet.avatar || 'No image'}</p>
                {pet.userId && (
                  <a 
                    href={`/profile/public/${pet.userId}`}
                    className="inline-block mt-2 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                  >
                    View Owner
                  </a>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Posts */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Posts ({posts.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {posts.map((post) => (
              <div key={post.id} className="bg-gray-50 p-4 rounded-lg">
                <p><strong>ID:</strong> {post.id}</p>
                <p><strong>User ID:</strong> {post.userId || 'No user'}</p>
                <p><strong>Content:</strong> {post.content?.substring(0, 100) || 'No content'}...</p>
                <p><strong>Public:</strong> {post.isPublic ? 'Yes' : 'No'}</p>
                <p><strong>Image:</strong> {post.image ? 'Yes' : 'No'}</p>
                {post.userId && (
                  <a 
                    href={`/profile/public/${post.userId}`}
                    className="inline-block mt-2 px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
                  >
                    View Author
                  </a>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
