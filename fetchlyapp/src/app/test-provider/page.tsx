'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getProviderByUserId } from '@/lib/firebase/providers';

export default function TestProviderPage() {
  const { user } = useAuth();
  const [providerData, setProviderData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkProviderProfile = async () => {
    if (!user) {
      setError('No user logged in');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Checking provider profile for user:', user.id);
      const provider = await getProviderByUserId(user.id);
      
      if (provider) {
        console.log('Provider profile found:', provider);
        setProviderData(provider);
      } else {
        console.log('No provider profile found');
        setError('No provider profile found for this user');
      }
    } catch (err) {
      console.error('Error checking provider profile:', err);
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Provider Profile Test</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current User</h2>
          {user ? (
            <div className="space-y-2">
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Name:</strong> {user.name}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Role:</strong> {user.role}</p>
            </div>
          ) : (
            <p className="text-gray-500">No user logged in</p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Provider Profile Check</h2>
          
          <button
            onClick={checkProviderProfile}
            disabled={!user || loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-4"
          >
            {loading ? 'Checking...' : 'Check Provider Profile'}
          </button>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {providerData && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <h3 className="font-semibold mb-2">Provider Profile Found!</h3>
              <div className="space-y-1 text-sm">
                <p><strong>Business Name:</strong> {providerData.businessName}</p>
                <p><strong>Owner Name:</strong> {providerData.ownerName}</p>
                <p><strong>Service Type:</strong> {providerData.serviceType}</p>
                <p><strong>Status:</strong> {providerData.status}</p>
                <p><strong>Membership Tier:</strong> {providerData.membershipTier}</p>
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 text-center">
          <a 
            href="/auth/signup" 
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Go to Signup Page
          </a>
          {' | '}
          <a 
            href="/provider/dashboard" 
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Go to Provider Dashboard
          </a>
        </div>
      </div>
    </div>
  );
}
