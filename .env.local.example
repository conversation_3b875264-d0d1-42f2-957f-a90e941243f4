# Email Configuration
# -------------------
# Set to 'true' to enable email notifications
EMAIL_ENABLED=false

# Email service provider (options: 'sendgrid', 'smtp')
EMAIL_PROVIDER=sendgrid

# SendGrid Configuration (if using SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="Fetchly"

# SMTP Configuration (if using SMTP)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Fetchly"

# Admin email for notifications
ADMIN_EMAIL=<EMAIL>

# Environment
NODE_ENV=development

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_test_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
NEXT_PUBLIC_STRIPE_CONNECT_CLIENT_ID=ca_your_connect_client_id_here
NEXT_PUBLIC_APP_URL=http://localhost:3000
