#!/bin/bash

# Deploy Firestore Indexes for Fetchly Chat System
# This script deploys the necessary indexes for the chat functionality

echo "🚀 Deploying Firestore Indexes for Fetchly Chat System..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ You are not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

# Deploy the indexes
echo "📊 Deploying Firestore indexes..."
firebase deploy --only firestore:indexes

if [ $? -eq 0 ]; then
    echo "✅ Firestore indexes deployed successfully!"
    echo ""
    echo "📋 The following chat indexes have been deployed:"
    echo "   1. Chats by participants and last message time"
    echo "   2. Chats by participants and updated time"
    echo "   3. Support chats filtering"
    echo "   4. Messages by chat ID and timestamp"
    echo "   5. Messages by sender and timestamp"
    echo "   6. Messages with read status"
    echo "   7. Chat notifications by user and read status"
    echo "   8. Chat notifications by user and chat ID"
    echo ""
    echo "🎉 Your chat system is now optimized for production!"
else
    echo "❌ Failed to deploy indexes. Please check the error above."
    exit 1
fi
